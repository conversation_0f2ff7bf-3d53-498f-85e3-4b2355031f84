<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="567a35f6-f539-4989-8b2f-36d7b1a4eb9b" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/cmd-tools/importcate/main.go" beforeDir="false" afterPath="$PROJECT_DIR$/cmd-tools/importcate/main.go" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Go File" />
      </list>
    </option>
  </component>
  <component name="GOROOT" url="file://$PROJECT_DIR$/../../../../workbase/go/gobook/go1.19" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="GoLibraries">
    <option name="indexEntireGoPath" value="false" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/cmd-tools/importcate/cat-hy88-2.csv" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/configs/errors.go" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/internal/controllers/admin/product.go" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/internal/controllers/product.go" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/internal/models/product.go" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/internal/publisher/contentmaker.go" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/internal/publisher/product_publiser.go" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/internal/publisher/publiser.go" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/internal/publisher/titlemaker.go" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/pkg/dbtypes/const.go" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/github.com/iris-contrib/middleware/jwt@v0.0.0-20191026145846-fcf5d9ba6367/config.go" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/github.com/iris-contrib/middleware/jwt@v0.0.0-20191026145846-fcf5d9ba6367/jwt.go" root0="SKIP_INSPECTION" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="1SoUp8UcMSE6KWMJ0KSLjHu4m0J" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "DefaultGoTemplateProperty": "Go File",
    "Go 构建.go build gitlab.com/all_publish/api.executor": "Run",
    "Go 构建.go build gitlab.com/all_publish/api/cmd-tools/importcate.executor": "Run",
    "Go 测试.gitlab.com/all_publish/api/internal/business/eventtrigger 中的 TestLiebiaoTrigger_AutoFlow.executor": "Debug",
    "Go 测试.gitlab.com/all_publish/api/internal/publisher 中的 TestMyPublisher_GenInfoBy.executor": "Run",
    "Go 测试.gitlab.com/all_publish/api/internal/publisher 中的 TestMyPublisher_PubInfoByCompany.executor": "Debug",
    "Go 测试.gitlab.com/all_publish/api/internal/publisher 中的 TestNewTemplateContentMaker.executor": "Run",
    "Go 测试.gitlab.com/all_publish/api/internal/publisher 中的 TestParseContentIndex.executor": "Run",
    "Go 测试.gitlab.com/all_publish/api/internal/services 中的 TestMerchantService_Set2Pass.executor": "Run",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.go.format.on.save.advertiser.fired": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "RunOnceActivity.go.modules.go.list.on.any.changes.was.set": "true",
    "RunOnceActivity.go.watchers.conflict.with.on.save.actions.check.performed": "true",
    "WebServerToolWindowFactoryState": "false",
    "configurable..is.expanded": "false",
    "configurable.GoLibrariesConfigurable.is.expanded": "true",
    "git-widget-placeholder": "develop",
    "go.import.settings.migrated": "true",
    "go.sdk.automatically.set": "true",
    "last_opened_file_path": "/Users/<USER>/data/codestation/git/all_publish/api2/cmd-tools/importcate",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.pluginManager"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/cmd-tools/importcate" />
      <recent name="$PROJECT_DIR$/internal/controllers" />
      <recent name="$PROJECT_DIR$/internal/business/eventtrigger" />
      <recent name="$PROJECT_DIR$/internal/business/merchanter" />
      <recent name="$PROJECT_DIR$/internal/models/merchants" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$" />
      <recent name="$PROJECT_DIR$/internal/business/eventtrigger" />
      <recent name="$PROJECT_DIR$/cmd-tools/importcate" />
      <recent name="$PROJECT_DIR$/internal/services" />
      <recent name="$PROJECT_DIR$/internal/controllers/admin" />
    </key>
  </component>
  <component name="RunManager" selected="Go 构建.go build gitlab.com/all_publish/api/cmd-tools/importcate">
    <configuration default="true" type="GoApplicationRunConfiguration" factoryName="Go Application">
      <module name="api2" />
      <working_directory value="$PROJECT_DIR$/" />
      <go_parameters value="-i" />
      <kind value="FILE" />
      <package value="github.com/bjmayor/all_publish/api" />
      <directory value="$PROJECT_DIR$/" />
      <filePath value="$PROJECT_DIR$/" />
      <method v="2" />
    </configuration>
    <configuration name="go build gitlab.com/all_publish/api" type="GoApplicationRunConfiguration" factoryName="Go Application" temporary="true" nameIsGenerated="true">
      <module name="api2" />
      <working_directory value="$PROJECT_DIR$" />
      <parameters value="-config=config.dev.ini" />
      <kind value="PACKAGE" />
      <package value="gitlab.com/all_publish/api" />
      <directory value="$PROJECT_DIR$/" />
      <filePath value="$PROJECT_DIR$/main.go" />
      <method v="2" />
    </configuration>
    <configuration name="go build gitlab.com/all_publish/api/cmd-tools/importcate" type="GoApplicationRunConfiguration" factoryName="Go Application" temporary="true" nameIsGenerated="true">
      <module name="api2" />
      <working_directory value="$PROJECT_DIR$" />
      <kind value="PACKAGE" />
      <package value="gitlab.com/all_publish/api/cmd-tools/importcate" />
      <directory value="$PROJECT_DIR$/" />
      <filePath value="$PROJECT_DIR$/cmd-tools/importcate/main.go" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="GoTestRunConfiguration" factoryName="Go Test">
      <module name="api2" />
      <working_directory value="$PROJECT_DIR$/" />
      <go_parameters value="-i" />
      <kind value="DIRECTORY" />
      <package value="github.com/bjmayor/all_publish/api" />
      <directory value="$PROJECT_DIR$/" />
      <filePath value="$PROJECT_DIR$/" />
      <framework value="gotest" />
      <method v="2" />
    </configuration>
    <configuration name="gitlab.com/all_publish/api/internal/publisher 中的 TestNewTemplateContentMaker" type="GoTestRunConfiguration" factoryName="Go Test" temporary="true" nameIsGenerated="true">
      <module name="api2" />
      <working_directory value="$PROJECT_DIR$/internal/publisher" />
      <root_directory value="$PROJECT_DIR$" />
      <kind value="PACKAGE" />
      <package value="gitlab.com/all_publish/api/internal/publisher" />
      <directory value="$PROJECT_DIR$/" />
      <filePath value="$PROJECT_DIR$/" />
      <framework value="gotest" />
      <pattern value="^\QTestNewTemplateContentMaker\E$" />
      <method v="2" />
    </configuration>
    <configuration name="gitlab.com/all_publish/api/internal/publisher 中的 TestParseContentIndex" type="GoTestRunConfiguration" factoryName="Go Test" temporary="true" nameIsGenerated="true">
      <module name="api2" />
      <working_directory value="$PROJECT_DIR$/internal/publisher" />
      <root_directory value="$PROJECT_DIR$" />
      <kind value="PACKAGE" />
      <package value="gitlab.com/all_publish/api/internal/publisher" />
      <directory value="$PROJECT_DIR$/" />
      <filePath value="$PROJECT_DIR$/" />
      <framework value="gotest" />
      <pattern value="^\QTestParseContentIndex\E$" />
      <method v="2" />
    </configuration>
    <configuration name="gitlab.com/all_publish/api/internal/services 中的 TestMerchantService_Set2Pass" type="GoTestRunConfiguration" factoryName="Go Test" temporary="true" nameIsGenerated="true">
      <module name="api2" />
      <working_directory value="$PROJECT_DIR$/internal/services" />
      <root_directory value="$PROJECT_DIR$" />
      <kind value="PACKAGE" />
      <package value="gitlab.com/all_publish/api/internal/services" />
      <directory value="$PROJECT_DIR$/" />
      <filePath value="$PROJECT_DIR$/" />
      <framework value="gotest" />
      <pattern value="^\QTestMerchantService_Set2Pass\E$" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Go 构建.go build gitlab.com/all_publish/api/cmd-tools/importcate" />
      <item itemvalue="Go 构建.go build gitlab.com/all_publish/api" />
      <item itemvalue="Go 测试.gitlab.com/all_publish/api/internal/publisher 中的 TestNewTemplateContentMaker" />
      <item itemvalue="Go 测试.gitlab.com/all_publish/api/internal/publisher 中的 TestParseContentIndex" />
      <item itemvalue="Go 测试.gitlab.com/all_publish/api/internal/services 中的 TestMerchantService_Set2Pass" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Go 构建.go build gitlab.com/all_publish/api/cmd-tools/importcate" />
        <item itemvalue="Go 构建.go build gitlab.com/all_publish/api" />
        <item itemvalue="Go 测试.gitlab.com/all_publish/api/internal/services 中的 TestMerchantService_Set2Pass" />
        <item itemvalue="Go 测试.gitlab.com/all_publish/api/internal/publisher 中的 TestNewTemplateContentMaker" />
        <item itemvalue="Go 测试.gitlab.com/all_publish/api/internal/publisher 中的 TestParseContentIndex" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-gosdk-2946fb9b3188-155fe4b6e3a0-org.jetbrains.plugins.go.sharedIndexes.bundled-GO-233.14475.38" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="CUSTOM_BOOLEAN_PROPERTIES">
                <map>
                  <entry key="Show.Git.Branches" value="true" />
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VgoProject">
    <environment>
      <map>
        <entry key="GOPRIVATE" value="https://git.paihang8.com/" />
        <entry key="GOPROXY" value="https://goproxy.cn,direct" />
      </map>
    </environment>
    <settings-migrated>true</settings-migrated>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/utils/auth.go</url>
          <line>51</line>
          <option name="timeStamp" value="64" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/goutils/hy88/publish_api.go</url>
          <line>973</line>
          <option name="timeStamp" value="83" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/goutils/hy88/publish_api.go</url>
          <line>609</line>
          <option name="timeStamp" value="84" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/goutils/brower/httpbrower.go</url>
          <line>187</line>
          <option name="timeStamp" value="85" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v0.3.2/china/publish.go</url>
          <line>102</line>
          <option name="timeStamp" value="182" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v0.3.3/china/product.go</url>
          <line>108</line>
          <option name="timeStamp" value="193" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v0.3.3/china/publish.go</url>
          <line>97</line>
          <option name="timeStamp" value="194" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v0.3.4/china/publish.go</url>
          <line>100</line>
          <option name="timeStamp" value="195" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v0.4.2/b2b168/publish.go</url>
          <line>108</line>
          <option name="timeStamp" value="200" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/cmd-tools/convert/main.go</url>
          <line>57</line>
          <option name="timeStamp" value="208" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/cmd-tools/convert/main.go</url>
          <line>93</line>
          <option name="timeStamp" value="209" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/cmd-tools/convert/main.go</url>
          <line>129</line>
          <option name="timeStamp" value="210" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/cmd-tools/convert/main.go</url>
          <line>160</line>
          <option name="timeStamp" value="211" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/cmd-tools/convert/main.go</url>
          <line>191</line>
          <option name="timeStamp" value="212" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v0.8.9/sites/kuyiso/publish_api.go</url>
          <line>141</line>
          <option name="timeStamp" value="274" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v0.8.9/sites/kuyiso/channel.go</url>
          <line>72</line>
          <option name="timeStamp" value="275" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v0.8.9/sites/kuyiso/user.go</url>
          <line>173</line>
          <option name="timeStamp" value="276" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v0.8.9/sites/kuyiso/publish_api.go</url>
          <line>105</line>
          <option name="timeStamp" value="277" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v0.8.9/sites/kuyiso/channel.go</url>
          <line>188</line>
          <option name="timeStamp" value="278" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v0.9.0/sites/kuyiso/publish_api.go</url>
          <line>150</line>
          <option name="timeStamp" value="279" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v0.9.0/sites/kuyiso/publish_api.go</url>
          <line>105</line>
          <option name="timeStamp" value="280" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v0.9.1/sites/kuyiso/publish_api.go</url>
          <line>105</line>
          <option name="timeStamp" value="281" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v0.9.1/sites/kuyiso/channel.go</url>
          <line>188</line>
          <option name="timeStamp" value="282" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v0.9.1/sites/kuyiso/publish_api.go</url>
          <line>138</line>
          <option name="timeStamp" value="283" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v1.1.8/sites/baixing/token.go</url>
          <line>10</line>
          <option name="timeStamp" value="354" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v1.4.7/sites/souhaohuo/publish_api.go</url>
          <line>126</line>
          <option name="timeStamp" value="390" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/tasks/aicaigou.go</url>
          <line>48</line>
          <option name="timeStamp" value="392" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/tasks/aicaigou.go</url>
          <line>29</line>
          <option name="timeStamp" value="393" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/github.com/gomodule/redigo@v1.7.1-0.20190724094224-574c33c3df38/redis/reply.go</url>
          <line>201</line>
          <option name="timeStamp" value="407" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v1.5.3/sites/liebiao/publish_api.go</url>
          <line>103</line>
          <option name="timeStamp" value="410" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/../../../../workbase/go/gobook/pkg/mod/git.paihang8.com/lib/goutils@v1.7.3/sites/hy88/publisher/message.go</url>
          <line>89</line>
          <option name="timeStamp" value="440" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/tasks/auto_open_merchant.go</url>
          <line>33</line>
          <option name="timeStamp" value="449" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/business/merchanter/hy88.go</url>
          <line>425</line>
          <option name="timeStamp" value="452" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/publisher/publiser.go</url>
          <line>518</line>
          <option name="timeStamp" value="456" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/services/dig.go</url>
          <line>21</line>
          <option name="timeStamp" value="486" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/api2$TestMyPublisher_PubInfoByCompany_in_gitlab_com_all_publish_api_core_publisher.out" NAME="TestMyPublisher_PubInfoByCompany in gitlab.com/all_publish/api/core/publisher Coverage Results" MODIFIED="1622001757111" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="GoCoverage" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" />
  </component>
</project>