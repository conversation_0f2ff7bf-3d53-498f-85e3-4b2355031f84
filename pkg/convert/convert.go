package convert

import "strconv"

type Str string

func (s Str) String() string {
	return string(s)
}

func (s Str) Int() (int, error) {
	v, err := strconv.Atoi(s.String())
	return v, err
}

func (s Str) MustInt() int {
	v, _ := s.Int()
	return v
}

func (s Str) UInt32() (uint32, error) {
	v, err := strconv.Atoi(s.String())
	return uint32(v), err
}

func (s Str) UInt64() (uint64, error) {
	v, err := strconv.Atoi(s.String())
	return uint64(v), err
}

func (s Str) MustUInt32() uint32 {
	v, _ := s.UInt32()
	return v
}

func (s Str) MustUInt64() uint64 {
	v, _ := s.UInt64()
	return v
}
