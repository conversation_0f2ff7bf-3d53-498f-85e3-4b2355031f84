package utils

import (
	"encoding/json"
	"math/rand"
	"time"

	"gitlab.com/all_publish/api/pkg/dbtypes"
)

func init() {
	rand.Seed(time.Now().UnixNano())
}

func Shuffle(choices []string) []string {
	for i := len(choices); i > 0; i-- {
		lastIdx := i - 1
		idx := rand.Intn(i)
		choices[lastIdx], choices[idx] = choices[idx], choices[lastIdx]

	}
	return choices
}

type UrlArr dbtypes.JSONArray

func (u UrlArr) ToUrls() []string {
	// 老版本就是单纯的url数组。
	// 新版本变成了json对象。需要提取url
	urls := make([]string, len(u))
	for i, pic := range u {
		urls[i] = pic
		jsondecode := make(map[string]interface{})
		if err := json.Unmarshal([]byte(pic), &jsondecode); err == nil {
			if url, ok := jsondecode["url"]; ok {
				urls[i] = url.(string)
			}
		}
	}
	return urls
}
