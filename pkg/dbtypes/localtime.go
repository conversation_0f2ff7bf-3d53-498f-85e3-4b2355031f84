package dbtypes

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"time"
)

type LocalTime time.Time

// 转为数据库对象
func (l LocalTime) Value() (driver.Value, error) {
	stamp := fmt.Sprintf("\"%s\"", time.Time(l).Format("15:04:05"))
	return stamp, nil
}

// 数据库对象转过来
func (l *LocalTime) Scan(value interface{}) error {
	s, ok := value.(time.Time)
	if !ok {
		errors.New("Invalid Scan Source")
	}
	*l = LocalTime(s)
	return nil
}

// 对象变字符串
func (l LocalTime) MarshalJSON() ([]byte, error) {
	stamp := fmt.Sprintf("\"%s\"", time.Time(l).Format("15:04:05"))
	return []byte(stamp), nil
}

// 字符串变对象
func (l *LocalTime) UnmarshalJSON(data []byte) error {
	if l == nil {
		return errors.New("null point exception")
	}
	//去掉前后的"
	t, err := time.ParseInLocation("15:04:05", string(data[1:len(data)-1]), SHLocation)
	if err != nil {
		return err
	}
	*l = LocalTime(t)
	return nil
}

func SHNow() time.Time {
	var now = time.Now().In(SHLocation)
	return now
}
