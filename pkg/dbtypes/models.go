package dbtypes

type CertStatus uint8

const (
	CertStatusUnKnow       CertStatus = 0 // 未知状态
	CertStatusNeedRegNo    CertStatus = 1 // 需要营业执照 -> 2
	CertStatusNeedSubmit   CertStatus = 2 // 等待提交 -> 3
	CertStatusSubmitted    CertStatus = 3 //  已经提交审核 ->4,5
	CertStatusPassed       CertStatus = 4 // 审核通过
	CertStatusRejected     CertStatus = 5 // 审核拒绝 -> 1
	CertStatusSubmitFailed CertStatus = 6 // 提交失败
)

type Action uint8

const (
	ActionNone           Action = iota //  无
	ActionUploadLincense               // 上传营业执照
	ActionUploadAnnounce               // 上传企业声明
	ActionEditAicaigou                 // 爱采购编辑资料
)

const (
	MerChantTotalIndicateClosed = -2 //针对黄页88特殊设置。因为黄页88默认是开通的。
)
