package dbtypes

var SCOPES_ENTITY = map[string]string{
	"system_user:list:get:add:delete:modify": "系统用户",
	"user:list:get:add:delete:modify":        "平台用户",
	"company:list:get:delete:modify":         "公司信息",
	"aicaigou:list:get:delete:modify":        "爱采购信息",
	"product:list:get:delete:modify":         "产品信息",
	"info:list:get:delete:modify":            "发布信息",
	"companyproduct:list:get":                "发布产品",
}

// HY-6472
const (
	Role_Administrater        = "超级管理员"
	Role_Company_Adviser      = "企业服务顾问"
	Role_Company_User_Manager = "企业账号管理员"
)

var DEF_Scopes map[string][]string

func init() {
	DEF_Scopes = make(map[string][]string)
	for k, _ := range SCOPES_ENTITY {
		DEF_Scopes[Role_Administrater] = append(DEF_Scopes[Role_Administrater], k)
	}
	DEF_Scopes[Role_Company_User_Manager] = []string{
		"user:list:get:add:delete:modify",
		"company:list:get:delete:modify",
		"aicaigou:list:get:delete:modify",
		"product:list:get:delete:modify",
		"info:list:get:delete:modify",
		"companyproduct:list:get",
	}

	DEF_Scopes[Role_Company_Adviser] = []string{
		"user:list:get:add:delete:modify",
		"company:list:get:delete:modify",
		"aicaigou:list:get:delete:modify",
		"product:list:get:delete:modify",
		"info:list:get:delete:modify",
		"companyproduct:list:get",
	}
}
