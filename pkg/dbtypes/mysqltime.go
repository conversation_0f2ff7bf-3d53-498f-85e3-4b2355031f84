package dbtypes

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"time"
)

type MysqlTime struct {
	time.Time
}

// 转为数据库对象
func (l MysqlTime) Value() (driver.Value, error) {
	stamp := fmt.Sprintf("%s", l.Format("2006-01-02 15:04:05"))
	return stamp, nil
}

// 数据库对象转过来
func (l *MysqlTime) Scan(value interface{}) error {
	t, ok := value.(time.Time)
	if !ok {
		return errors.New("not time.Time")
	}
	*l = MysqlTime{Time: t}
	return nil
}

// 对象变字符串
func (l MysqlTime) MarshalJSON() ([]byte, error) {
	stamp := fmt.Sprintf("\"%s\"", l.Format("15:04:05"))
	return []byte(stamp), nil
}

// 字符串变对象
func (l *MysqlTime) UnmarshalJSON(data []byte) error {
	if l == nil {
		return errors.New("null point exception")
	}
	//去掉前后的"
	t, err := time.ParseInLocation("15:04:05", string(data[1:len(data)-1]), SHLocation)
	if err != nil {
		return err
	}
	*l = MysqlTime{t}
	return nil
}
