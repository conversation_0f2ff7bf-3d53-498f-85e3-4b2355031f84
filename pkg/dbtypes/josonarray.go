package dbtypes

import (
	"database/sql/driver"
	"errors"
	"fmt"
	jsoniter "github.com/json-iterator/go"
	"strconv"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

type JSONArray []string

//转为数据库对象
func (l JSONArray) Value() (driver.Value, error) {
	if str, err := json.Marshal(l); err != nil {
		return nil, err
	} else {
		return str, nil
	}
}

//数据库对象转过来
func (l *JSONArray) Scan(value interface{}) error {
	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return errors.New(fmt.Sprint("Failed to unmarshal JSONB value:", value))
	}
	var m []string
	err := json.Unmarshal(bytes, &m)
	if err != nil {
		return err
	}
	*l = m
	return nil
}

//对象变字符串
func (l JSONArray) MarshalJSON() ([]byte, error) {
	if l == nil {
		return []byte("null"), nil
	}
	t := ([]string)(l)
	return json.Marshal(t)
}

//字符串变对象
func (l *JSONArray) UnmarshalJSON(data []byte) error {
	if l == nil {
		return errors.New("null point exception")
	}
	var m []string
	err := json.Unmarshal(data, &m)
	if err != nil {
		return err
	}
	*l = m
	return nil
}

func (l JSONArray) GetCatID() int {
	if len(l) > 0 {
		for i := len(l) - 1; i >= 0; i-- {
			if l[i] != "0" {
				id, _ := strconv.Atoi(l[i])
				return id
			}
		}
	}
	return 0
}
