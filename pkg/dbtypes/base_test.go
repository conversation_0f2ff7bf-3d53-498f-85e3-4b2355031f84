package dbtypes

import (
	"testing"
	"time"
)

type Inner struct {
	ID          int
	ContactName string   `json:"contact_name,editable"`
	Logo        *string  `json:"logo,editable"`
	Phone       []string `json:"phone,editable"`
	Address     string   `json:"address,editable"`
	MinOrder    *int     `json:"min_order,editable"`
	Inventory   *int     `json:"inventory,editable"`
	AdvisorID   *float64 `json:"advisor_id,editable"`
}

type Outter struct {
	Inner       `json:"inner,editable"`
	AutoPubTime *MysqlTime             `json:"auto_pub_time,editable"`
	Account     map[string]interface{} `json:"account,editable"`
	Price       float32                `json:"price,editable"`
}

func TestUpdateModelFromMap(t *testing.T) {
	t.Run("basic", func(t *testing.T) {
		c := Inner{ContactName: "contactName"}
		m := map[string]interface{}{
			"contact_name": "contactName2",
			"min_order":    nil,
			"inventory":    nil,
		}
		UpdateModelFromMap(&c, m)
		if c.<PERSON>Name == "contactName2" {
			t.Log("ok")
		} else {
			t.Errorf("expected %s, real %s", m["contact_name"], c.ContactName)
		}
	})
	t.Run("basic pointer", func(t *testing.T) {
		var logo = "Logo"
		c := Inner{Logo: &logo}
		m := map[string]interface{}{
			"logo": "logo2",
		}
		UpdateModelFromMap(&c, m)
		if *c.Logo == "logo2" {
			t.Log("ok")
		} else {
			t.Errorf("expected %s, real %s", m["logo"], *c.Logo)
		}
	})

	t.Run("basic more keys", func(t *testing.T) {
		c := Inner{ContactName: "contactName"}
		m := map[string]interface{}{
			"contact_name": "contactName2",
			"otherkeys":    "otherkeys",
		}
		UpdateModelFromMap(&c, m)
		if c.ContactName == "contactName2" {
			t.Log("ok")
		} else {
			t.Errorf("expected %s, real %s", m["contact_name"], c.ContactName)
		}
	})

	t.Run("basic with unmodified keys", func(t *testing.T) {
		c := Inner{ID: 1}
		m := map[string]interface{}{
			"id": 2,
		}
		UpdateModelFromMap(&c, m)
		if c.ID == 1 {
			t.Log("ok")
		} else {
			t.Errorf("expected 1, real %d", c.ID)
		}
	})

	t.Run("with localtime not nil", func(t *testing.T) {
		oldV := MysqlTime(SHNow())
		c := Outter{AutoPubTime: &oldV}
		m := map[string]interface{}{
			"auto_pub_time": "05:32:11",
		}
		UpdateModelFromMap(&c, m)
		if c.AutoPubTime != nil && time.Time(*c.AutoPubTime).Hour() == 5 && time.Time(*c.AutoPubTime).Minute() == 32 && time.Time(*c.AutoPubTime).Second() == 11 {
			t.Log("ok")
		} else {
			t.Errorf("expected %s, real %v", "05:32:11", c.AutoPubTime)
		}
	})

	t.Run("with localtime nil", func(t *testing.T) {
		c := Outter{AutoPubTime: nil}
		m := map[string]interface{}{
			"auto_pub_time": "05:32:11",
		}
		UpdateModelFromMap(&c, m)
		if c.AutoPubTime != nil && time.Time(*c.AutoPubTime).Hour() == 5 && time.Time(*c.AutoPubTime).Minute() == 32 && time.Time(*c.AutoPubTime).Second() == 11 {
			t.Log("ok")
		} else {
			t.Errorf("expected %s, real %v", "05:32:11", c.AutoPubTime)
		}
	})
	t.Run("with json", func(t *testing.T) {
		merchant := Outter{Account: nil}
		account := map[string]interface{}{
			"mobile": "***********",

			"password": "123"}
		m := map[string]interface{}{
			"account": account,
		}
		UpdateModelFromMap(&merchant, m)
		updatedAccount, _ := json.Marshal(merchant.Account)
		originAccount, _ := json.Marshal(account)

		if string(originAccount) == string(updatedAccount) {
			t.Log("ok")
		} else {
			t.Errorf("expected %s, real %s", originAccount, updatedAccount)
		}
	})

	t.Run("with json nil", func(t *testing.T) {
		merchant := Outter{Account: map[string]interface{}{
			"mobile":   "***********",
			"password": "123"}}
		m := map[string]interface{}{
			"account": nil,
		}
		if err := UpdateModelFromMap(&merchant, m); err != nil {
			t.Fatal(err)
		}
		if merchant.Account == nil {
			t.Log("ok")
		} else {
			t.Errorf("expected %v, real %v", nil, merchant.Account)
		}
	})

	t.Run("with embedded struct", func(t *testing.T) {
		c := Outter{Inner: Inner{
			Address:   "address",
			AdvisorID: nil,
		},
			Price: 3.2,
		}

		m := map[string]interface{}{
			"inner": map[string]interface{}{
				"address":    "长沙",
				"advisor_id": float64(12),
			},
			"alias": "",
			"price": 6.4,
		}
		UpdateModelFromMap(&c, m)
		if c.Inner.Address == "长沙" {
			t.Log("ok")
		} else {
			t.Errorf("expected 长沙, real %s", c.Inner.Address)
		}

		if *c.Inner.AdvisorID == 12 {
			t.Log("ok")
		} else {
			t.Errorf("expected 12, real %f", *c.Inner.AdvisorID)
		}

		if c.Price == 6.4 {
			t.Log("ok")
		} else {
			t.Errorf("expected 6.4, real %f", c.Price)
		}
	})
}

func BenchmarkGenPassword(b *testing.B) {
	for i := 0; i < b.N; i++ {
		result := GenPassword(true, true, true, true, 8)
		if len(result) != 8 {
			b.Fatal("length not match")
		}
	}
}
