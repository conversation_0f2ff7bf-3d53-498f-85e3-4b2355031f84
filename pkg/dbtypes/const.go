package dbtypes

const (
	CompanyProductStatusNone             = -1
	CompanyProductStatusWattingpublish   = 0 //等待推送
	CompanyProductStatusPublishSuccessed = 1 //推送成功
	CompanyProductStatusAuditnotpass     = 2 // 推送失败
	CompanyProductStatusPublishFailed    = 3
)

const (
	InfoStatusNone             = 0
	InfoStatusWattingpublish   = 1
	InfoStatusPublishSuccessed = 2
	InfoStatusAuditnotpass     = 3
	InfoStatusPublishFailed    = 4
)

const (
	ProductStatusNone         = 0
	ProductStatusDraft        = 1 //草稿
	ProductStatusWattingaudit = 2 //等待审核
	ProductStatusAuditpass    = 3 //审核通过
	ProductStatusPromote      = 4 //推广
	ProductStatusAuditnotpass = 5 //审核拒绝
	ProductStatusTitleout     = 6 //标题用完, 信息发布用
	ProductStatusContentOut   = 7 //内容已用完, 不能生成更多的信息用innerstatus, 真用完了放在status
	ProductStatusSubjectOut   = 8 //关键词已用完，产品发布用
)

const MaxInfosPerProduct = 20000 //每个产品最多能发布的信息数
const MaxProducts = 30           //最多添加的产品数

//客户端自动生成关键词后缀修饰词
var KW_Sufix = []string{"厂家直销", "总代直销", "批发代理", "量大从优", "价格实惠", "服务周到", "优质服务", "信誉保证", "放心省心", "安全可靠", "造型美观", "款式齐全", "设计合理", "样式优雅", "款式新颖", "色泽光润", "瑰丽多彩", "质量可靠", "售后保障", "品质优良", "品种繁多", "规格齐全", "性能可靠", "操作简单", "制作精良", "经久耐用", "服务至上"}
var KW_Prefix = []string{
	"电动", "二手", "供应", "环保", "进口", "迷你", "全新", "热门", "微型", "销售", "小型", "新款", "优质", "智能", "自动", "承接", "从事", "定做", "定制", "订制", "生产", "制造", "精巧", "可靠",
	"靠谱", "细致", "牢固", "坚实", "精密", "精致", "可折叠", "易碎",
	"耐用", "防震", "防水", "隔音好", "可水洗", "不起皱", "传统", "独特", "优雅", "奢华", "时尚", "逼真",
	"精美", "精细", "普通", "耐磨", "半自动", "便携式"}
var KW_City = []string{
	"北京", "天津", "上海", "重庆", "香港", "澳门", "台湾", "常德", "长沙", "邵阳", "永州", "怀化", "益阳", "岳阳", "张家界", "株洲",
	"湘潭", "衡阳", "郴州", "娄底", "湘西", "洛阳", "焦作", "三门峡", "郑州", "新乡", "鹤壁", "安阳", "濮阳", "开封", "商丘", "许昌",
	"漯河", "平顶山", "南阳", "信阳", "周口", "驻马店", "石家庄", "张家口", "秦皇岛", "承德", "唐山", "廊坊", "保定", "沧州", "衡水",
	"邢台", "邯郸", "南京", "徐州", "连云港", "宿迁", "盐城", "扬州", "泰州", "南通", "镇江", "常州", "无锡", "苏州", "淮安",
	"南昌", "九江", "景德镇", "鹰潭", "新余", "萍乡", "赣州", "上饶", "抚州", "宜春", "吉安", "昆明", "曲靖", "玉溪", "丽江",
	"昭通", "临沧", "保山", "德宏", "怒江", "迪庆", "大理", "楚雄", "红河", "文山", "西双版纳", "普洱", "武汉", "十堰", "襄樊",
	"荆门", "孝感", "黄冈", "鄂州", "黄石", "咸宁", "荆州", "宜昌", "随州", "恩施", "广州", "深圳", "汕头", "珠海", "清远",
	"韶关", "河源", "梅州", "潮州", "揭阳", "汕尾", "惠州", "江门", "佛山", "肇庆", "云浮", "阳江", "茂名", "湛江", "东莞", "大同",
	"太原", "朔州", "阳泉", "长治", "晋城", "忻州", "吕梁", "晋中", "临汾", "运城", "沈阳", "朝阳", "阜新", "铁岭", "抚顺", "本溪",
	"辽阳", "鞍山", "丹东", "大连", "营口", "盘锦", "锦州", "葫芦岛", "长春", "白城", "松原", "吉林", "四平", "辽源", "通化", "白山",
	"延边", "杭州", "湖州", "嘉兴", "舟山", "宁波", "绍兴", "金华", "台州", "温州", "丽水", "衢州", "合肥", "宿州", "淮北",
	"阜阳", "蚌埠", "淮南", "滁州", "马鞍山", "芜湖", "铜陵", "安庆", "黄山", "六安", "巢湖", "池州", "宣城", "亳州", "福州", "南平",
	"三明", "莆田", "泉州", "厦门", "漳州", "龙岩", "宁德", "济南", "聊城", "德州", "东营", "淄博", "潍坊", "烟台", "威海", "青岛",
	"日照", "临沂", "枣庄", "济宁", "泰安", "莱芜", "滨州", "菏泽", "呼和浩特", "包头", "乌海", "赤峰", "呼伦贝尔", "兴安盟", "锡林郭勒盟",
	"乌兰察布", "巴彦淖尔", "阿拉善盟", "通辽", "鄂尔多斯", "哈尔滨", "齐齐哈尔", "黑河", "大庆", "伊春", "鹤岗", "佳木斯",
	"双鸭山", "七台河", "鸡西", "牡丹江", "绥化", "大兴安岭", "南宁", "桂林", "柳州", "梧州", "贵港", "玉林", "钦州", "北海", "防城港",
	"百色", "河池", "贺州", "来宾", "崇左", "海口", "三亚", "成都", "广元", "绵阳", "德阳", "南充", "广安", "遂宁", "内江", "乐山",
	"自贡", "泸州", "宜宾", "攀枝花", "巴中", "达州", "资阳", "眉山", "雅安", "阿坝", "甘孜", "凉山", "贵阳", "六盘水", "遵义", "毕节",
	"铜仁", "安顺", "黔东南", "黔南", "黔西南", "拉萨", "那曲", "昌都", "林芝", "山南", "日喀则", "阿里", "西安", "延安", "铜川", "咸阳",
	"渭南", "宝鸡", "汉中", "榆林", "安康", "商洛", "兰州", "金昌", "白银", "天水", "酒泉", "张掖", "武威", "庆阳", "平凉", "定西", "陇南",
	"临夏", "甘南", "西宁", "海东", "海北", "海南", "黄南", "果洛", "玉树", "海西", "银川", "石嘴山", "吴忠", "固原", "中卫", "乌鲁木齐",
	"克拉玛依", "喀什", "阿克苏", "和田", "吐鲁番", "哈密", "克孜勒苏", "博尔塔拉", "昌吉", "巴音郭楞", "伊犁哈萨克", "伊犁", "塔城",
	"阿勒泰", "醴陵", "鄢陵县", "济源", "中山", "大丰", "武夷山", "张北", "长葛", "诸城", "垦利", "和县", "霍邱", "桐城", "临猗",
	"清徐", "嘉峪关", "仙桃", "潜江", "神农架", "天门", "永新", "库尔勒", "阿拉尔", "石河子", "图木舒克", "五家渠", "迁安", "海拉尔",
	"思茅", "海南省直辖", "襄阳", "阿泰勒", "克孜勒苏柯尔克孜", "通辽市", "乌海市", "巴彦倬尔",
}

func IsInfoStatusValid(status int) bool {
	return status >= InfoStatusWattingpublish && status <= InfoStatusPublishFailed
}

func IsCompanyProductStatusValid(status int) bool {
	return status >= CompanyProductStatusWattingpublish && status <= CompanyProductStatusPublishFailed
}

func IsProductStatusValid(status int) bool {
	return status >= ProductStatusDraft && status <= ProductStatusSubjectOut
}
