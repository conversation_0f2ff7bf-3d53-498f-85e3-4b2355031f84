package dbtypes

import (
	"database/sql/driver"
	"reflect"
	"testing"
)

func TestJSONArray_MarshalJSON(t *testing.T) {
	tests := []struct {
		name    string
		l       JSONArray
		want    []byte
		wantErr bool
	}{
		{
			"ok",
			JSONArray([]string{"a", "b", "c"}),
			[]byte(`["a","b","c"]`),
			false,
		},
		{
			"nil",
			JSONArray([]string{}),
			[]byte(`[]`),
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.l.MarshalJSON()
			if (err != nil) != tt.wantErr {
				t.<PERSON>rf("MarshalJSON() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("MarshalJSON() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestJSONArray_Scan(t *testing.T) {
	type args struct {
		value interface{}
	}
	tests := []struct {
		name    string
		l       JSONArray
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.l.Scan(tt.args.value); (err != nil) != tt.wantErr {
				t.Errorf("Scan() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestJSONArray_UnmarshalJSON(t *testing.T) {
	type args struct {
		data []byte
	}
	var l JSONArray
	tests := []struct {
		name    string
		l       JSONArray
		args    args
		wantErr bool
	}{
		{
			"ok",
			l,
			args{data: []byte(`["a\"b","b","c"]`)},
			false,
		},
		{
			"err",
			l,
			args{data: []byte(`[1,2,3]`)},
			true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.l.UnmarshalJSON(tt.args.data); (err != nil) != tt.wantErr {
				t.Errorf("UnmarshalJSON() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestJSONArray_Value(t *testing.T) {
	tests := []struct {
		name    string
		l       JSONArray
		want    driver.Value
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.l.Value()
			if (err != nil) != tt.wantErr {
				t.Errorf("Value() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Value() got = %v, want %v", got, tt.want)
			}
		})
	}
}
