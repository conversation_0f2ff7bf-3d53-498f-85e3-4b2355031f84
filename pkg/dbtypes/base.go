package dbtypes

import (
	"bytes"
	"fmt"
	"math/rand"
	"reflect"
	"runtime"
	"strings"
	"time"
)

// 放这里才不会循环依赖
type Base struct {
	CreatedAt time.Time `gorm:"index:createdAt;not null;type:datetime" json:"created_at,omitempty"`
	UpdatedAt time.Time `gorm:"index:updatedAt;not null;type:datetime" json:"updated_at,omitempty"`
}

var SHLocation = time.FixedZone("Asia/Shanghai", 8*60*60)

func updateModelFromV(v reflect.Value, m map[string]interface{}) reflect.Value {
	e := v
	s := v.Type()
	for i := 0; i < s.NumField(); i++ {
		tag := s.Field(i).Tag.Get("json")
		name := strings.Split(tag, ",")[0]
		if strings.Contains(tag, "editable") {
			if v, ok := m[name]; ok {
				switch s.Field(i).Type.Kind() {
				case reflect.Ptr:
					target := s.Field(i).Type.Elem()
					switch target.Kind() {
					case reflect.Int:
						if !e.Field(i).IsNil() {
							e.Field(i).Elem().SetInt(int64(v.(float64)))
						} else {
							var v = int64(v.(float64))
							vv := reflect.New(e.Field(i).Type().Elem())
							vv.Elem().SetInt(v)
							e.Field(i).Set(vv)
						}
					case reflect.Float32:
						fallthrough
					case reflect.Float64:
						if !e.Field(i).IsNil() {
							e.Field(i).Elem().SetFloat(float64(v.(float64)))
						} else {
							var v = float64(v.(float64))
							vv := reflect.New(e.Field(i).Type().Elem())
							vv.Elem().SetFloat(v)
							e.Field(i).Set(vv)
						}
					case reflect.Struct:
						switch e.Field(i).Type().Elem() {
						case reflect.TypeOf(LocalTime{}):
							t, _ := time.ParseInLocation("15:04:05", v.(string), SHLocation)
							l := LocalTime(t)
							if !e.Field(i).IsNil() {
								e.Field(i).Elem().Set(reflect.ValueOf(l))
							} else {
								vv := reflect.New(e.Field(i).Type().Elem())
								vv.Elem().Set(reflect.ValueOf(l))
								e.Field(i).Set(vv)
							}
						case reflect.TypeOf(MysqlTime{}):
							t, _ := time.ParseInLocation("15:04:05", v.(string), SHLocation)
							l := MysqlTime{t}
							if !e.Field(i).IsNil() {
								e.Field(i).Elem().Set(reflect.ValueOf(l))
							} else {
								vv := reflect.New(e.Field(i).Type().Elem())
								vv.Elem().Set(reflect.ValueOf(l))
								e.Field(i).Set(vv)
							}
						case reflect.TypeOf(MysqlDateTime{}):
							var l *MysqlDateTime
							if v != nil {
								t, _ := time.ParseInLocation("2006-01-02 15:04:05", v.(string), SHLocation)
								ll := MysqlDateTime(t)
								l = &ll
							} else {
								l = nil
							}
							e.Field(i).Set(reflect.ValueOf(l))
						}
					default:
						if !e.Field(i).IsNil() {
							e.Field(i).Elem().Set(reflect.ValueOf(v))
						} else {
							vv := reflect.New(e.Field(i).Type().Elem())
							vv.Elem().Set(reflect.ValueOf(v))
							e.Field(i).Set(vv)
						}

					}
				case reflect.Slice:
					switch e.Field(i).Type() {
					case reflect.TypeOf(JSONArray{}):
						var data []string
						for _, d := range v.([]interface{}) {
							data = append(data, d.(string))
						}
						e.Field(i).Set(reflect.ValueOf(data))
					}
				case reflect.Int:
					fallthrough
				case reflect.Int8:
					fallthrough
				case reflect.Int16:
					fallthrough
				case reflect.Int32:
					fallthrough
				case reflect.Int64:
					e.Field(i).SetInt(int64(v.(float64)))
				case reflect.Uint:
					fallthrough
				case reflect.Uint8:
					fallthrough
				case reflect.Uint16:
					fallthrough
				case reflect.Uint32:
					fallthrough
				case reflect.Uint64:
					e.Field(i).SetUint(uint64(v.(float64)))
				case reflect.Float32:
					e.Field(i).SetFloat(float64(v.(float64)))
				case reflect.Struct:
					if v != nil {
						f := e.Field(i)
						ff := updateModelFromV(f, v.(map[string]interface{}))
						e.Field(i).Set(ff)
					}
				case reflect.Map:
					if v != nil {
						e.Field(i).Set(reflect.ValueOf(v))
					} else {
						vv := map[string]interface{}(nil)
						e.Field(i).Set(reflect.ValueOf(vv))
					}
				default:
					e.Field(i).Set(reflect.ValueOf(v))
				}
			}
		}
	}
	return v
}

func UpdateModelFromMap(model interface{}, m map[string]interface{}) (err error) {
	defer func() {
		if rval := recover(); rval != nil {
			err = fmt.Errorf("UpdateModelFromMap err :%s", rval)
		}
	}()
	t := reflect.TypeOf(model)
	if t.Kind() != reflect.Ptr {
		panic("model should be a pointer")
	}
	e := reflect.ValueOf(model).Elem()
	ne := updateModelFromV(e, m)
	e.Set(ne)
	return nil
}

type ScopeItem struct {
	Actions     []string `json:"actions"`
	Description string   `json:"description"`
	Scope       string   `json:"scope"`
	Entity      string   `json:"entity"`
}

type Scopes struct {
	Actions map[string]string `json:"actions"`
	Scopes  []ScopeItem       `json:"scopes"`
}

func Struct2Map(obj interface{}) map[string]interface{} {
	obj1 := reflect.TypeOf(obj)
	obj2 := reflect.ValueOf(obj)

	var data = make(map[string]interface{})
	for i := 0; i < obj1.NumField(); i++ {
		name := obj1.Field(i).Name
		tag := obj1.Field(i).Tag.Get("json")
		if tag != "" {
			name = tag
		}
		switch obj1.Field(i).Type.Kind() {
		case reflect.Struct:
			m := Struct2Map(obj2.Field(i).Interface())
			data[name] = m
		default:
			data[name] = obj2.Field(i).Interface()
		}
	}
	return data
}

func GenPassword(num, alpha, upperAlpha, symbol bool, minLen int) string {
	nums := "0123456789"
	alphas := "abcdefghijklmnopqrstuvwxyz"
	upperAlphas := "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	symbols := "@!.[]?<>`~"
	result := ""
	all := ""
	if num {
		idx := rand.Intn(len(nums))
		result += nums[idx : idx+1]
		all += nums
	}
	if alpha {
		idx := rand.Intn(len(alphas))
		result += alphas[idx : idx+1]
		all += alphas
	}
	if upperAlpha {
		idx := rand.Intn(len(upperAlphas))
		result += upperAlphas[idx : idx+1]
		all += upperAlphas
	}
	if symbol {
		idx := rand.Intn(len(symbols))
		result += symbols[idx : idx+1]
		all += symbols
	}
	for i := minLen - len(result); i >= 1; i-- {
		idx := rand.Intn(len(all))
		result += all[idx : idx+1]
	}
	return result
}

// 打印堆栈信息
func GetStackTrace(err interface{}) string {
	buf := new(bytes.Buffer)
	fmt.Fprintf(buf, "%v\n", err)
	for i := 1; ; i++ {
		pc, file, line, ok := runtime.Caller(i)
		if !ok {
			break
		}
		fmt.Fprintf(buf, "%s:%d (0x%x)\n", file, line, pc)
	}
	return buf.String()
}
