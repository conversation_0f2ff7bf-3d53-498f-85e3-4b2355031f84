package dbtypes

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"time"
)

type MysqlDateTime time.Time

const dtLayout = "2006-01-02 15:04:05"

//转为数据库对象
func (l MysqlDateTime) Value() (driver.Value, error) {
	stamp := fmt.Sprintf("%s", time.Time(l).Format(dtLayout))
	return stamp, nil
}

//数据库对象转过来
func (l *MysqlDateTime) Scan(value interface{}) error {
	t := value.(time.Time)
	*l = MysqlDateTime(t)
	return nil
}

//对象变字符串
func (l MysqlDateTime) MarshalJSON() ([]byte, error) {
	stamp := fmt.Sprintf("\"%s\"", time.Time(l).Format(dtLayout))
	return []byte(stamp), nil
}

//字符串变对象
func (l *MysqlDateTime) UnmarshalJSON(data []byte) error {
	if l == nil {
		return errors.New("null point exception")
	}
	//去掉前后的"
	t, err := time.ParseInLocation(dtLayout, string(data[1:len(data)-1]), SHLocation)
	if err != nil {
		return err
	}
	*l = MysqlDateTime(t)
	return nil
}
