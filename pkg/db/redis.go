package db

import (
	"fmt"
	"github.com/gomodule/redigo/redis"
	"gitlab.com/all_publish/api/configs"
	"runtime"
	"sync"
	"time"
)

var pool redis.Pool

func InitRedis(config configs.Config) { //init 用于初始化一些参数，先于main执行
	o := &sync.Once{}
	o.Do(func() {
		cpuNum := runtime.GOMAXPROCS(0)
		pool = redis.Pool{
			MaxIdle:     cpuNum,
			MaxActive:   cpuNum * 4,
			IdleTimeout: 120,
			Dial: func() (redis.Conn, error) {
				return redis.Dial("tcp", fmt.Sprintf("%s:%s", config.Redis.Host, config.Redis.Port),
					redis.DialPassword(config.Redis.Password))
			},
			TestOnBorrow: func(c redis.Conn, t time.Time) error {
				if time.Since(t) < time.Minute {
					return nil
				}
				_, err := c.Do("PING")
				return err
			},
		}
	})
	_, err := GetRedisConn().Do("PING")
	if err != nil {
		panic(err)
	}
}

func GetRedisConn() redis.Conn {
	return pool.Get()
}

func CloseRedis() {
	pool.Close()
}
