package db

import (
	"gitlab.com/all_publish/api/configs"
	"gorm.io/gorm"
	"sync"
)

type DatabaseConnector interface {
	init(config configs.Config)

	Get() *gorm.DB

	Close()
}

type ConnectorType int

const (
	ConnectorTypeMysql = iota
	ConnectorTypePq
)

var connector DatabaseConnector

func Init(config configs.Config, t ConnectorType) {
	o := &sync.Once{}
	o.Do(func() {
		switch t {
		case ConnectorTypeMysql:
			connector = NewMysqlConnector(config)
		}
	})

}

func Instance() DatabaseConnector {
	return connector
}
