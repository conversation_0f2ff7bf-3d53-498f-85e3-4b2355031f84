package db

import (
	"fmt"
	"log"
	"os"
	"time"

	"git.paihang8.com/lib/goutils"
	"git.paihang8.com/lib/goutils/logger"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gorm.io/driver/mysql"
	_ "gorm.io/driver/mysql"
	"gorm.io/gorm"
	gloger "gorm.io/gorm/logger"
)

type MysqlConnector struct {
	eg *gorm.DB
}

func NewMysqlConnector(config configs.Config) DatabaseConnector {
	connector := MysqlConnector{}
	connector.init(config)
	return &connector
}

func (m *MysqlConnector) init(config configs.Config) {
	var err error

	newLogger := gloger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		gloger.Config{
			SlowThreshold: time.Microsecond,
			LogLevel:      gloger.Warn,
			Colorful:      true,
		})
	if config.Web.ShowSql {
		newLogger = gloger.New(
			logger.NewLogger("/tmp/log/sql.log"),
			gloger.Config{
				SlowThreshold: time.Microsecond,
				LogLevel:      gloger.Warn,
				Colorful:      false,
			})
	}

	m.eg, err = gorm.Open(mysql.Open(fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=%s",
		config.Mysql.User,
		config.Mysql.Password,
		config.Mysql.Host,
		config.Mysql.Port,
		config.Mysql.Name,
		goutils.EncodeURIComponent("Asia/Shanghai"),
	)),
		&gorm.Config{PrepareStmt: true, Logger: newLogger, DisableForeignKeyConstraintWhenMigrating: true},
	)

	if err != nil {
		panic(err)
	}
	if config.Web.AugoMigrateDb {
		err := m.eg.AutoMigrate(
		//&models.Company{},
		//&models.Image{},
		//&models.Info{},
		//&models.AicaigouUsers{},
		//&models.Merchant{},
		//&models.BadWords{},
		//&models.Rank{},
		//&models.Merchant{},
		//&models.UserPlatform{},
		//&models.UserStat{},
		// &models.Product{},
		//&models.PageCache{},
		//&models.SystemUser{},
		//&models.User{},
		//&models.UserLoginHistory{},
		//&models.CompanyProduct{},
		//&models.Album{},
		//&models.Infos{},
		//&models.CatMappings{},
		//&models.AreaMappings{},
		//&models.InfoExt{},
		//&models.UserPubStat{},
		//&models.UserPubStatDetails{},
		//&models.ChatSearchRecord{},
		//&models.DailyHistory{},
		)
		if err != nil {
			panic(err)
		}
		for i := 0; i < 10; i++ {
			//m.eg.Table(fmt.Sprintf("image%d", i)).AutoMigrate(&models.Image{})
			if err := m.eg.Table(models.InfoExt{}.TableNameById(uint64(i) + configs.ApiConfig.Web.MaxId + 1)).AutoMigrate(&models.InfoExt{}); err != nil {
				panic(err)
			}
			//m.eg.Table(fmt.Sprintf("image%d", i)).Migrator().AlterColumn(&models.Image{}, "Hash")
		}

	}

}

func (m *MysqlConnector) Get() *gorm.DB {
	return m.eg
}

func (m *MysqlConnector) Close() {
}
