package regutils

import "testing"

func TestIsIdCardNumber(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{"ok1", args{"11010119900307411X"}, true},
		{"ok2", args{"12345678901234567X"}, true},
		{"false1", args{"12345678901234567"}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsIdCardNumber(tt.args.s); got != tt.want {
				t.<PERSON>rf("IsIdCardNumber() = %v, want %v", got, tt.want)
			}
		})
	}
}
