package cachex

import (
	"github.com/gomodule/redigo/redis"
	"gitlab.com/all_publish/api/pkg/db"
)

func Set() {

}

func SetEx(key string, value []byte, seconds int) {
	conn := db.GetRedisConn()
	defer conn.Close()
	conn.Do("setex", key, seconds, value)
}

func Get(key string) (string, bool) {
	conn := db.GetRedisConn()
	defer conn.Close()
	if data, err := redis.String(conn.Do("get", key)); err == nil {
		return data, true
	}
	return "", false
}
