package cachex

import (
	"github.com/magiconair/properties/assert"
	"gitlab.com/all_publish/api/test"
	"testing"
	"time"
)

func TestSetEx(t *testing.T) {
	test.InitTest(t)
	key := "hello"
	value := "world"

	t.Run("expect ok", func(t *testing.T) {
		SetEx(key, []byte(value), 3)
		v, ok := Get(key)
		assert.Equal(t, v, value)
		assert.Equal(t, ok, true)
	})
	t.Run("expect not ok", func(t *testing.T) {
		SetEx(key, []byte(value), 3)
		time.Sleep(time.Second * 3)
		v, ok := Get(key)
		assert.Equal(t, v, "")
		assert.Equal(t, ok, false)
	})

}
