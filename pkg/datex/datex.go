package datex

import "time"

func GetYesterdayTime() (time.Time, time.Time) {
	now := time.Now()
	yesterday := now.AddDate(0, 0, -1) // 获取昨天的日期

	startTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, time.Local)          // 昨天的起始时间
	endTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 23, 59, 59, 999999999, time.Local) // 昨天的结束时间

	return startTime, endTime
}
