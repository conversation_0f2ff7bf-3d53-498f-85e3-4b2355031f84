stages:
- build
- deploy

variables:
  CONTAINER_RELEASE_IMAGE: registry.cn-beijing.aliyuncs.com/aixunpan/all_publish_api_go:$CI_COMMIT_REF_NAME
  DOCKER_TLS_CERTDIR: ""
  DOCKER_CLI_EXPERIMENTAL: "enabled"  # 启用实验性功能（Buildx）


build:
  stage: build
  tags:
    - fafa45
  before_script:
    - docker login -u $FAFA_REGISTRY_USER_NAME -p $FAFA_REGISTRY_USER_PASSWORD registry.cn-beijing.aliyuncs.com
  script:
    - docker build  --pull -t $CONTAINER_RELEASE_IMAGE .
    - docker push $CONTAINER_RELEASE_IMAGE



deploy-master:
  stage: deploy
  before_script:
  - sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories
  - apk --quiet update
  - apk add sshpass
  - apk add openssh-client
  script:
  - export SSHPASS=$AIXUNPAN_USER_PASS
  - sshpass -<NAME_EMAIL> -o StrictHostKeyChecking=no "sudo docker service update --image $CONTAINER_RELEASE_IMAGE all_publish_api_go --with-registry-auth"
  only:
    - master
  tags:
    - fafa45

deploy-dev:
  stage: deploy
  before_script:
  - sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories
  - apk --quiet update
  - apk add sshpass
  - apk add openssh-client
  script:
  - export SSHPASS=$AIXUNPAN_USER_PASS
  - sshpass -<NAME_EMAIL> -o StrictHostKeyChecking=no "sudo docker service update --image $CONTAINER_RELEASE_IMAGE all_publish_dev_api_go --with-registry-auth"
  only:
  - develop
  tags:
    - fafa45

