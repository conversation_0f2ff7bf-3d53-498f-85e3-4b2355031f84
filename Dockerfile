FROM golang:1.20 AS builder
RUN sed -i 's/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's/security.debian.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list.d/debian.sources && apt-get update -y && apt-get install -y --no-install-recommends \
    libc6-dev 
WORKDIR /build
RUN go env -w GOPROXY=https://goproxy.cn,direct
RUN echo "machine git.paihang8.com login bjmayor password gb160718" >  ~/.netrc
COPY ./ .
RUN go mod download
RUN CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -a -installsuffix cgo -o api .



FROM ubuntu:22.04  AS final
ENV TZ=Asia/Shanghai
# 安装 tzdata 包
RUN apt-get update && apt-get install -y tzdata && \
    ln -fs /usr/share/zoneinfo/$TZ /etc/localtime && \
    dpkg-reconfigure --frontend noninteractive tzdata && \
    apt-get clean
RUN  apt-get install -y --no-install-recommends \
    ca-certificates && \
    rm -rf /var/lib/apt/lists/*
WORKDIR /opt/fafa-backend
COPY --from=builder /build/api /opt/fafa-backend/
COPY --from=builder /build/nsswitch.conf /etc/nsswitch.conf
COPY --from=builder /build/assets /opt/fafa-backend/assets
EXPOSE 5000
ENTRYPOINT ["/opt/fafa-backend/api"]