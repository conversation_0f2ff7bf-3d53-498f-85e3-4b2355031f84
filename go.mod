module gitlab.com/all_publish/api

go 1.16

require (
	git.paihang8.com/lib/goutils v1.9.12
	github.com/Shopify/goreferrer v0.0.0-20220729165902-8cddb4f5de06 // indirect
	github.com/ajg/form v1.5.1 // indirect
	github.com/alecthomas/template v0.0.0-20190718012654-fb15b899a751
	github.com/certifi/gocertifi v0.0.0-20191021191039-0944d244cd40 // indirect
	github.com/dgrijalva/jwt-go v3.2.0+incompatible // indirect
	github.com/getsentry/raven-go v0.2.0
	github.com/go-openapi/jsonreference v0.19.3 // indirect
	github.com/go-openapi/spec v0.19.4 // indirect
	github.com/go-playground/locales v0.14.1
	github.com/go-playground/universal-translator v0.18.1
	github.com/go-playground/validator/v10 v10.11.2
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/gocarina/gocsv v0.0.0-20201208093247-67c824bc04d4
	github.com/gomodule/redigo v1.7.1-0.20190724094224-574c33c3df38
	github.com/gorilla/websocket v1.4.2 // indirect
	github.com/imkira/go-interpol v1.1.0 // indirect
	github.com/iris-contrib/middleware/jwt v0.0.0-20191026145846-fcf5d9ba6367
	github.com/iris-contrib/middleware/raven v0.0.0-20191026145846-fcf5d9ba6367
	github.com/iris-contrib/swagger/v12 v12.0.0
	github.com/json-iterator/go v1.1.12
	github.com/juju/errors v0.0.0-20190930114154-d42613fe1ab9 // indirect
	github.com/kataras/iris/v12 v12.0.1
	github.com/kr/pretty v0.3.1 // indirect
	github.com/lestrrat-go/file-rotatelogs v2.4.0+incompatible
	github.com/magiconair/properties v1.8.1
	github.com/mailru/easyjson v0.7.1 // indirect
	github.com/mattn/go-sqlite3 v2.0.3+incompatible // indirect
	github.com/mitchellh/mapstructure v1.4.2
	github.com/moul/http2curl v1.0.0 // indirect
	github.com/mozillazg/go-pinyin v0.20.0 // indirect
	github.com/robfig/cron v1.2.0
	github.com/rs/cors v1.7.0
	github.com/sergi/go-diff v1.0.0 // indirect
	github.com/stretchr/testify v1.8.2
	github.com/swaggo/swag v1.6.3
	github.com/valyala/fasthttp v1.5.0 // indirect
	github.com/xeipuuv/gojsonschema v1.2.0 // indirect
	github.com/yalp/jsonpath v0.0.0-20180802001716-5cc68e5049a0 // indirect
	github.com/yanyiwu/gojieba v1.4.5
	github.com/yudai/gojsondiff v1.0.0 // indirect
	github.com/yudai/golcs v0.0.0-20170316035057-ecda9a501e82 // indirect
	go.uber.org/automaxprocs v1.2.0
	go.uber.org/zap v1.24.0
	golang.org/x/sync v0.8.0
	gopkg.in/go-playground/validator.v9 v9.31.0
	gopkg.in/ini.v1 v1.51.0
	gorm.io/datatypes v1.0.7
	gorm.io/driver/mysql v1.3.5
	gorm.io/driver/sqlite v1.5.0 // indirect
	gorm.io/gorm v1.25.4
)

replace gitlab.com/all_publish/api/ => /Users/<USER>/data/codestation/git/all_publish/api2/
