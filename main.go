package main

import (
	"flag"
	"log"

	"github.com/getsentry/raven-go"
	"github.com/iris-contrib/middleware/jwt"
	ravenIris "github.com/iris-contrib/middleware/raven"
	"github.com/iris-contrib/swagger/v12"              // swagger middleware for Iris
	"github.com/iris-contrib/swagger/v12/swaggerFiles" // swagger embed files"
	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/middleware/logger"
	"github.com/kataras/iris/v12/middleware/pprof"
	"github.com/kataras/iris/v12/mvc"
	"github.com/rs/cors"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/docs"
	"gitlab.com/all_publish/api/internal/controllers"
	"gitlab.com/all_publish/api/internal/controllers/admin"
	v2 "gitlab.com/all_publish/api/internal/controllers/v2"
	"gitlab.com/all_publish/api/internal/tasks"
	"gitlab.com/all_publish/api/pkg/db"
	_ "go.uber.org/automaxprocs"
)

var (
	help       bool   // web服务
	configPath string // 配置文件
)

func init() {
	raven.SetDSN("https://<EMAIL>/6117502")
	flag.BoolVar(&help, "h", false, "帮助")
	flag.StringVar(&configPath, "config", "./config.ini", "配置文件地址")
}

// @title all_publish API DOC
// @version 1.0
// @description This is a sample server for all_publish
// @termsOfService http://swagger.io/terms/

// @contact.name shunping.liu(微信:maynardliu)
// @contact.url https://www.go2live.cn
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization
func main() {
	flag.Parse()
	if help {
		flag.Usage()
		return
	}
	config, err := configs.NewConfig(configPath)
	if err != nil {
		log.Println(err)
		return
	}
	// use swagger middleware to
	//db.InitDB(*config)
	db.Init(*config, db.ConnectorTypeMysql)
	db.InitRedis(*config)
	iris.RegisterOnInterrupt(func() {
		db.Instance().Close()
		db.CloseRedis()
	})

	app := iris.New()
	app.Use(ravenIris.RecoveryHandler)
	corsOptions := cors.Options{
		AllowedOrigins:   []string{"*"},
		AllowCredentials: false,
		AllowedMethods: []string{
			iris.MethodOptions,
			iris.MethodGet,
			iris.MethodDelete,
			iris.MethodPost,
			iris.MethodPut,
			iris.MethodHead,
			iris.MethodPatch,
		},
		AllowedHeaders: []string{"*"},
	}

	corsWrapper := cors.New(corsOptions).ServeHTTP

	app.WrapRouter(corsWrapper)
	//app.UseGlobal(func(context iris.Context) {
	//	context.Gzip(true)
	//})
	//use middlerware
	customLogger := logger.New(logger.Config{
		Status:             true,
		IP:                 true,
		Method:             true,
		Path:               true,
		Query:              true,
		MessageContextKeys: []string{"logger_message"},
		MessageHeaderKeys:  []string{"User-Agent"},
	})

	app.UseGlobal(customLogger)

	j := jwt.New(jwt.Config{
		ValidationKeyGetter: func(token *jwt.Token) (interface{}, error) {
			return []byte(config.Web.JWTSecret), nil
		},
		SigningMethod: jwt.SigningMethodHS256,
		ErrorHandler:  controllers.JwtErrorHandler,
	})
	app.UseGlobal(ravenIris.RecoveryHandler)
	app.Logger().SetLevel("debug")

	docs.SwaggerInfo.Schemes = []string{"http", "https"}
	docs.SwaggerInfo.Host = config.Swagger.Host + ":" + config.Swagger.Port
	app.RegisterView(iris.HTML("./views", ".html"))
	app.Use(iris.Gzip)
	// start record.
	app.UseGlobal(controllers.ValidatePermissionHandler)
	app.UseGlobal(controllers.CheckVersionHandler)
	//routes
	app.Get("/swagger/{any:path}", swagger.DisablingWrapHandler(swaggerFiles.Handler,
		"DISABLE_SWAGGER"))
	// app.HandleDir("/static", configs.ApiConfig.Web.StaticPath)
	app.Post("/auth", controllers.AuthHandler, controllers.AuthResponseHandler)
	// app.Post("/upload/img", controllers.UploadHandler)
	if config.Web.Debug {
		p := pprof.New()
		app.Any("/debug/pprof", p)
		app.Any("/debug/pprof/{action:path}", p)
	}
	mvc.Configure(app.Party("/album"), controllers.HandleAlbum)
	mvc.Configure(app.Party("/v2/album"), v2.HandleAlbum)
	mvc.Configure(app.Party("/company"), controllers.HandleCompany)
	mvc.Configure(app.Party("/aicaigou"), controllers.HandleAicaigou)
	mvc.Configure(app.Party("/info"), controllers.HandleInfo)
	mvc.Configure(app.Party("/merchant"), controllers.HandleMerchant)
	mvc.Configure(app.Party("/product"), controllers.HandleProduct)
	mvc.Configure(app.Party("/user"), controllers.HandleUser)
	mvc.Configure(app.Party("/tools"), controllers.HandleTools)
	mvc.Configure(app.Party("/ranks"), controllers.HandleRanks)
	mvc.Configure(app.Party("/v2/ranks"), v2.HandleRanks)
	mvc.Configure(app.Party("/seeks"), controllers.HandleSeek)
	mvc.Configure(app.Party("/videos"), controllers.HandleVideo)
	mvc.Configure(app.Party("/openapi"), controllers.HandleApi)
	mvc.Configure(app.Party("/companyproduct"), controllers.HandleCompanyProduct)
	mvc.Configure(app.Party("/category/{id:int}"), controllers.HandleCategory)
	mvc.Configure(app.Party("/v2/category/"), v2.HandleCategory)
	mvc.Configure(app.Party("/areas"), controllers.HandleArea)
	mvc.Configure(app.Party("/v2/areas"), v2.HandleArea)

	sys := app.Party("/sys")
	{
		mvc.Configure(sys.Party("/info"), admin.HandleInfo)
		mvc.Configure(sys.Party("/{companyId:uint64}/merchant"), admin.HandleMerchant)
		mvc.Configure(sys.Party("/product"), admin.HandleProduct)
		mvc.Configure(sys.Party("/user"), admin.HandleSysUser)
		mvc.Configure(sys.Party("/task"), admin.HandleTask)
		mvc.Configure(sys.Party("/company"), admin.HandleCompany)
		mvc.Configure(sys.Party("/stats"), admin.HandleUserStats)
		mvc.Configure(sys.Party("/companyproduct"), admin.HandleCompanyProduct)
		mvc.Configure(app.Party("/areas"), admin.HandleArea)
		mvc.Configure(sys.Party("/album"), admin.HandleAlbum)
		mvc.Configure(sys.Party("/{companyId:uint64}/aicaigou"), admin.HandleAicaigou)
	}

	app.OnErrorCode(iris.StatusNotFound, notFound)
	app.OnErrorCode(iris.StatusInternalServerError, internalServerError)
	tasks.StartCronTask()
	app.Run(iris.Addr(config.Web.Host+":"+config.Web.Port), iris.WithConfiguration(iris.Configuration{Other: map[string]interface{}{
		"config": config,
		"jwt":    j,
	}}),
		iris.WithoutBodyConsumptionOnUnmarshal,
		iris.WithoutPathCorrectionRedirection,
		iris.WithoutAutoFireStatusCode, //需要禁用，否则非200状态码无法输出json
		iris.WithRemoteAddrHeader("X-Real-Ip"),
	)
}

func notFound(ctx iris.Context) {
	// when 404 then render the template
	// $views_dir/errors/404.html
	ctx.WriteString("404 not found")
	//ctx.View("404 notfound")
}

func internalServerError(ctx iris.Context) {
	ctx.WriteString("Oups something went wrong, try again")
}
