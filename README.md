# 参考
ORM
https://gorm.io/zh_CN/docs/v2_release_note.html
mysql 转 orm工具
https://codechina.csdn.net/mirrors/xxjwxc/gormt?utm_source=csdn_github_accelerator
gormt
#编译
## mac上编译linux和windows二进制
```cgo
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build
CGO_ENABLED=0 GOOS=linux GOARCH=386 go build
CGO_ENABLED=0 GOOS=windows GOARCH=amd64 go build
```


## linux上编译mac和windows二进制
```cgo
CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 go build
CGO_ENABLED=0 GOOS=windows GOARCH=amd64 go build
```


## windows上编译mac和linux二进制
```cgo
SET CGO_ENABLED=0 SET GOOS=darwin SET GOARCH=amd64 go build main.go
SET CGO_ENABLED=0 SET GOOS=linux SET GOARCH=amd64 go build main.go
```


# Run 
1. git clone
2. 设置代理
 go env -w GOPROXY=https://goproxy.cn,direct
3. 构建二进制包
 go build
4. 运行
 ./api
 
 配置文件默认为:
 ./config.ini
 可以通过参数指定
 ./api --config path/file
 
 #  文档
1. 本地开发地址： http://local.paihang8.com:5000/swagger/index.html
2. 测试环境地址： https://api-dev.paihang8.com/swagger/index.html
3. 线上环境地址: https://api.paihang8.com/swagger/index.html
