out_dir : "./tmp"  # out dir
url_tag : json # web url tag(json,db(https://github.com/google/go-querystring))
language :  中 文# language(English,中 文)
db_tag : gorm # DB tag(gorm,db)
simple : true #simple output
is_out_sql : false # Whether to output sql
is_out_func : true # Whether to output function
is_foreign_key : true # Whether to mark foreign key or not
is_gui : false # Whether to operate on gui
is_table_name : false # Whether to out GetTableName/column function
is_null_to_point : false # database is 'DEFAULT NULL' then set element type as point
is_web_tag: false
is_web_tag_pk_hidden: false

db_info :
  host : "api.paihang8.com"
  port : 3307
  username : "root"
  password : "mdttPass123"
  database : "all_publish"
  type: 0 # database type (0:mysql , 1:sqlite , 2:mssql)
