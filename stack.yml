version: '3.1'

services:
  visualizer:
    image: dockersamples/visualizer
    ports:
      - "9001:8080"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock"
    deploy:
      replicas: 1
      placement:
        constraints: [node.role == manager]

  portainer:
    image: portainer/portainer
    ports:
      - "9000:9000"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock"
    deploy:
      replicas: 1
      placement:
        constraints: [node.role == manager]

  db:
    image: mentalroad/postgresql:latest
    volumes:
      - /Users/<USER>/data/codestation/git/all_publish/api2/data/pg_data:/var/lib/postgresql/data
    ports:
      - 5434:5432
    environment:
      POSTGRES_PASSWORD: mdttPass123


  pgadmin:
    image:  dpage/pgadmin4:4.5
    ports:
      - 8180:80
    volumes:
      - /Users/<USER>/data/codestation/git/all_publish/api2/data/pg_admin:/var/lib/pgadmin
    depends_on:
      - db
    environment:
      PGADMIN_DEFAULT_EMAIL: 'kangyu<PERSON><EMAIL>'
      PGADMIN_DEFAULT_PASSWORD: 'mdttPass123'

  redis:
    image: redis:5.0.4
    volumes:
      - /Users/<USER>/data/codestation/git/all_publish/api2/data/redis:/data
      - /Users/<USER>/data/codestation/git/all_publish/api2/data/redis.conf:/etc/redis/redis.conf
    command: 'redis-server /etc/redis/redis.conf'

  api:
    image: publish_api:v1.1
    ports:
      - 8090:8090
    depends_on
      - db
    volumes:
      - /Users/<USER>/data/codestation/git/all_publish/api2/data/static:/static
      - /Users/<USER>/data/codestation/git/all_publish/api2/data/views:/views
      - /Users/<USER>/data/codestation/git/all_publish/api2/data/config.ini:/config.ini

