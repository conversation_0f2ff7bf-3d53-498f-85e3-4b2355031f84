package test

import (
	"os"
	"path/filepath"
	"strings"
	"testing"

	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/pkg/db"
)

func InitTest(t *testing.T) {
	dir, err := os.Getwd()
	if err != nil {
		t.<PERSON>al(err)
	}
	// 开发环境
	if strings.Contains(dir, "api2") {
		dir = dir[:strings.LastIndex(dir, "api2")]
	}
	config, err := configs.NewConfig(filepath.Join(dir, "api2/config.dev.ini"))
	// config, err := configs.NewConfig(filepath.Join(dir, "api2/config.ini"))

	if err != nil {
		t.Fatal(err)
	}
	db.InitRedis(*config)
	db.Init(*config, db.ConnectorTypeMysql)
	//db.Init(*config, db.ConnectorTypePq)
}
