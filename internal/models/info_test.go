package models

import (
	"encoding/json"
	"testing"
)

func TestAicaigouInfo_Init(t *testing.T) {
	text := `{
	"res_url": {
		"messageid": "*********",
		"messageurl": "http://e.51sole.com/u88126/companyproductdetail_*********.htm"
	},
	"platform": 5,
	"merchant_id": 11755,
	"ext": {
		"synced": true,
		"indexed": {
			"baidu": {
				"name": "百度",
				"status": 1,
				"updated_at": "2021-09-28 15:03:22"
			},
			"baiduyd": {
				"name": "百度移动",
				"status": -1,
				"updated_at": "2021-09-28 15:03:22"
			}
		}
	}
}
`
	var m map[string]interface{}
	json.Unmarshal([]byte(text), &m)
	var item AicaigouInfo
	err := item.Init(m)
	t.Log(err)
	t.Log(m)
	t.Log(item)
}

func TestAicaigouInfo_ToMap(t *testing.T) {
	item := AicaigouInfo{
		ResUrl: ResUrl{
			Messageurl: "www.baidu.com",
			Messageid:  "999",
		},
		Platform:   3,
		MerchantId: 9,
		Ext: AicaigouExt{
			Synced: true,
		},
	}
	t.Log(item)
	t.Log(item.ToMap())
}
