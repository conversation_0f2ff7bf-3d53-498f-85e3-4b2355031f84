package models

type SoleCat struct {
	ID       int    `gorm:"primaryKey;column:id;type:int unsigned;not null"`
	CatName  string `gorm:"column:cat_name;type:varchar(255);not null"`
	ParentID int    `gorm:"index:parent_id;index:caten_level_p;index:parent_id_2;column:parent_id;type:int unsigned;not null;default:0"`
	Level    int    `gorm:"index:level;index:caten_level_p;column:level;type:tinyint unsigned;not null;default:0"`
	CatTrace string `gorm:"index:cat_trace;column:cat_trace;type:varchar(250);not null"`
}

func (SoleCat) TableName() string {
	return "sole_cats"
}
