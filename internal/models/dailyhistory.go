package models

import (
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/datatypes"
)

type StatType int8

const (
	StatTypeUser    = 0
	StatTypeProduct = 1
	StatTypeInfo    = 2
)

// DailyHistory
// 按日数据统计
type DailyHistory struct {
	ID int `json:"id"`
	dbtypes.Base
	StatType StatType          `json:"statType"` // 统计类型
	Data     datatypes.JSONMap `gorm:"type:json;column:data" json:"data,omitempty,editable"`
}
