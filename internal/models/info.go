package models

import (
	"time"

	"github.com/mitchellh/mapstructure"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/datatypes"
)

type Info struct {
	dbtypes.Base
	ID          uint64            `gorm:"primary_key" json:"id"`
	Title       *string           `gorm:"index:product_title;not null" json:"title"`
	Price       float32           `json:"price"`
	ProductName string            `gorm:"-" json:"product_name"` // hy88的别名
	Unit        string            `json:"unit"`
	Pic         dbtypes.JSONArray `gorm:"-" json:"pic,omitempty"`
	//新增标题图片HY-5170
	TitlePic dbtypes.JSONArray `gorm:"-" json:"titlepic,editable"`

	Word dbtypes.JSONArray `gorm:"type:json;column:word" json:"word,omitempty"`

	ProductID *uint64  `gorm:"index:product_id" json:"product_id"` //为空时用来判断是否自动发布的。必须用指针
	Product   *Product `gorm:"constraint:OnDelete:SET NULL;" json:"product,omitempty"`

	Description string `gorm:"-" json:"description"`
	Status      int    `json:"status"`

	PubRes datatypes.JSONMap `gorm:"type:json;column:pub_res" json:"pub_res,omitempty"`

	CompanyID        uint64     `gorm:"index:company_id" json:"company_id"`
	Company          *Company   `json:"company,omitempty"`
	AuditRes         string     `json:"audit_res"`
	WillPubAt        *time.Time `gorm:"index:will_pub_at" json:"will_pub_at"`
	FailedReason     string     `gorm:"type:text;column:failed_reason" json:"failed_reason"`
	FailedTimes      int        `gorm:"default:0" json:"failed_times"`
	ProductHistoryID uint64     `gorm:"index:product_history_id" json:"-"` //用来计算内容的
	DescriptionIdx   string     `json:"-"`

	Platform     PlatForm `gorm:"default:0" json:"platform"` //平台
	PlatFormName string   `gorm:"-" json:"plat_form_name"`   // 平台名称
}

func (Info) TableName() string {
	return "info"
}

type InfoGroupByMerchant struct {
	Merchant uint64 `json:"merchant"`
	Count    int    `json:"count"`
}

/*
*
{"res_url": {"messageid": "*********", "messageurl": "http://e.51sole.com/u88126/companyproductdetail_*********.htm"}, "platform": 5, "merchant_id": 11755,
"ext":{ "synced":true, "indexed":{"baidu":true, "baiduyd":true}}
}
*/
type AicaigouInfo struct {
	ResUrl     `json:"res_url" mapstructure:"res_url"`
	Platform   PlatForm    `json:"platform" mapstructure:"platform"`
	MerchantId int         `json:"merchant_id" mapstructure:"merchant_id"`
	Ext        AicaigouExt `json:"ext" mapstructure:"ext"`
}

type ResUrl struct {
	Messageid  string `json:"messageid" mapstructure:"messageid"`
	Messageurl string `json:"messageurl" mapstructure:"messageurl"`
}

type AicaigouExt struct {
	Synced bool `json:"synced" mapstructure:"synced"`
}
type Indexed struct {
	Name      string `json:"name" mapstructure:"name"`
	Status    int    `json:"status" mapstructure:"status"`         // -1 未收录；0 未查 ；1 已收录
	UpdatedAt string `json:"updated_at" mapstructure:"updated_at"` // 更新时间
}

func (account AicaigouInfo) ToMap() map[string]interface{} {
	var m map[string]interface{}
	mapstructure.Decode(account, &m)
	return m
}

func (account *AicaigouInfo) Init(m map[string]interface{}) error {
	err := mapstructure.Decode(m, account)
	return err
}

type Engine string

const (
	Baidu  Engine = "baidu"
	BaiduM Engine = "baidu_m"
)
