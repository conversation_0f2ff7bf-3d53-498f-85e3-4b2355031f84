package models

import (
	"errors"
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	zh_translations "github.com/go-playground/validator/v10/translations/zh"
	"reflect"
	"strings"
)

var Validate *validator.Validate
var trans ut.Translator

func init() {
	zhongwen := zh.New()
	uni := ut.New(zhongwen, zhongwen)
	trans, _ = uni.GetTranslator("zh")

	Validate = validator.New()
	Validate.RegisterTagNameFunc(func(field reflect.StructField) string {
		label := field.Tag.Get("label")
		if label == "" {
			return field.Name
		}
		return label
	})
	zh_translations.RegisterDefaultTranslations(Validate, trans)
}

func Valiate(v interface{}) error {
	if err := Validate.Struct(v); err != nil {
		errStr := Translate(err.(validator.ValidationErrors))
		return errors.New(errStr)
	}
	return nil
}

func Translate(errs validator.ValidationErrors) string {
	var errList []string
	for _, e := range errs {
		// can translate each error one at a time.
		errList = append(errList, e.Translate(trans))
	}
	return strings.Join(errList, "|")
}
