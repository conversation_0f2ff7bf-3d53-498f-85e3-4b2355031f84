package models

import (
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/datatypes"
)

// 用户发布数据统计
type UserPubStat struct {
	dbtypes.Base
	CompanyID               uint64                 `gorm:"primary_key" json:"company_id"`             // 公司id
	CompanyName             string                 `json:"company_name"`                              // 公司名，方便搜索
	UserName                string                 `json:"user_name"`                                 // 用户名，方便搜索
	ProductCnt              int                    `gorm:"default:0" json:"product_cnt"`              // 添加的产品数
	ProductPromotionCnt     int                    `gorm:"default:0" json:"product_promotion_cnt"`    // 推广中的产品数
	InfoSucceedCnt          int                    `json:"info_succeed_cnt"`                          // 推送成功的信息数
	InfoSucceedCntYesterday int                    `json:"info_succeed_cnt_yesterday"`                // 昨日推送成功的信息数
	InfoFailedCntYesterday  int                    `json:"info_failed_cnt_yesterday"`                 // 昨日推送失败的信息数
	OpEndTime               *dbtypes.MysqlDateTime `gorm:"type:datetime" json:"op_end_time,editable"` // 运营到期时间
	RankedCnt               int                    `json:"ranked_cnt"`                                // 排名总数
	AdvisorName             string                 `json:"advisor_name"`                              // 顾问名字
	AdvisorID               uint64                 `json:"advisor_id"`                                //顾问Id
}

type UserPubStatDetails struct {
	dbtypes.Base
	CompanyID          uint64            `gorm:"primary_key" json:"company_id"`                                     // 公司id
	ProductStats       datatypes.JSONMap `gorm:"type:json;column:product_stats" json:"product_stats"`               // 产品统计
	InfoStats          datatypes.JSONMap `gorm:"type:json;column:info_stats" json:"info_stats"`                     // 信息统计
	InfoStatsYesterday datatypes.JSONMap `gorm:"type:json;column:info_stats_yesterday" json:"info_stats_yesterday"` // 昨日信息统计
	RankedStats        datatypes.JSONMap `gorm:"type:json;column:ranked_stats" json:"ranked_stats"`                 // 排名统计
}

// 统计
type InfoGroupByPlatformStatus struct {
	Platform   int    `json:"platform"`
	Name       string `json:"name"`
	StatusCnt  int    `json:"status_cnt"`
	StatusName int    `json:"status"`
}
