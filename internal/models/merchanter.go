package models

import (
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
)

type MerchantMeta struct {
	Name            string                 `json:"name"`
	Description     string                 `json:"description"`
	PlatForm        PlatForm               `json:"plat_form"`
	RequiredAccount map[string]AccountItem `json:"required_account"`
	HomeUrl         string                 `json:"home_url"`
	LogoUrl         string                 `json:"logo_url"`
	PlatFormName    string                 `gorm:"-", json:"plat_form_name"` // 平台名称
}

type AccountItem struct {
	Type string `json:"type"`
	Desc string `json:"desc"`
}

var MerchantMap map[PlatForm]Merchanter

type Merchanter interface {
	Reg(c Company) (interface{}, error)
	PublishInfo(info Info) (publisher.SimpleMessage, error)
	//PubInfo(field schemas.InfoFields, com Company) 手动发布
	Name() string
	PlatForm() PlatForm
	Clone() Merchanter
	Meta() MerchantMeta
	Account() map[string]interface{}
	UpdateAccount(key string, value interface{})
	LoginBy(account map[string]interface{}) (User, error)
	IsErrorShouldStopAutoPub(err string) bool
	RenewWhenNeeded(cid uint64) bool
	SubmitCert(c Company, ext ...interface{}) (interface{}, error)

	GetCert(cid, id int) (interface{}, error)
	GetApi() interface{}
}

func init() {
	MerchantMap = map[PlatForm]Merchanter{}
}

func RegisterMerchant(name PlatForm, merchanter Merchanter) bool {
	if _, ok := MerchantMap[name]; ok {
		return true
	}
	MerchantMap[name] = merchanter
	return true
}

/*
*
这里不能直接返回。。每个公司的账号不一样。需要clone一个。然后共享token
*/
func GetMerchant(name PlatForm) (Merchanter, bool) {
	if m, ok := MerchantMap[name]; ok {
		return m.Clone(), true
	}
	return nil, false
}
