package models

import (
	"time"

	"gitlab.com/all_publish/api/pkg/dbtypes"
)

type PlatForm int8

const (
	PlatformAll       PlatForm = -1
	PlatformHy88      PlatForm = 0 //黄页88, 以后存数字, 老的数据存的字符串
	PlatformBafang    PlatForm = 1 // 八方资源
	PlatformChina     PlatForm = 2 // 中国供应商
	PlatformKuyiso    PlatForm = 3 // 酷易搜
	PlatformSole      PlatForm = 4 // 51sole
	PlatformSoleExt   PlatForm = 5 // 51sole+爱采购
	PlatformAicaigou  PlatForm = 6 // 爱采购
	PlatformLiebiao   PlatForm = 7 // 列表网
	PlatformBaixing   PlatForm = 8 //  百姓网
	PlatformSouHaoHuo PlatForm = 9 // 搜好货
)

var names map[PlatForm]string
var domains map[string]PlatForm

func init() {
	names = map[PlatForm]string{
		PlatformHy88:      "黄页88",
		PlatformBafang:    "八方资源",
		PlatformChina:     "中国供应商",
		PlatformKuyiso:    "酷易搜",
		PlatformSole:      "搜了网",
		PlatformSoleExt:   "搜了网+爱采购",
		PlatformAicaigou:  "爱采购",
		PlatformLiebiao:   "列表网",
		PlatformBaixing:   "百姓网",
		PlatformSouHaoHuo: "搜好货",
	}
	domains = map[string]PlatForm{
		"huangye88.com":       PlatformHy88,
		"b2b168.com":          PlatformBafang,
		"china.cn":            PlatformChina,
		"kuyiso.com":          PlatformKuyiso,
		"51sole.com":          PlatformSole,
		"51sole.com_aicaigou": PlatformSoleExt,
		"liebiao.com":         PlatformLiebiao,
		"baixing.com":         PlatformBaixing,
		"912688.com":          PlatformSouHaoHuo,
	}
}

func CountOfPlatform() int {
	// 列表网暂停服务了。但是历史数据 还需要支持
	// 百姓网也暂停服务了。
	return len(domains) - 2
}

func PlatFormName(pf PlatForm) string {
	return names[pf]
}

func PlatFormByDomain(domain string) PlatForm {
	return domains[domain]
}

const (
	UserPlatformStatusNotOpened = 0 //未开通
	UserPlatformStatusOpening   = 1 //建设中
	UserPlatformStatusOpened    = 2 //已开通
)

const (
	FuncnameSeek = "seek" //查收录
	FuncnameRank = "rank" //查排名
	FuncnamePost = "post" //发布
)

/*
*
基本都是以company_id 为关联键的，所以都用company_id关联，而不是uid。
*/
type UserPlatform struct {
	dbtypes.Base
	CompanyID    uint64     `gorm:"primary_key" json:"company_id"`
	Platform     PlatForm   `gorm:"primary_key;" json:"platform"`
	PlatFormName string     `gorm:"-" json:"plat_form_name"` // 平台名称
	Funcname     string     `gorm:"primary_key;" json:"funcname"`
	Status       int        `json:"status"`
	ExpireTime   *time.Time `json:"expire_time"`
}

func (UserPlatform) TableName() string {
	return "user_platform"
}
