package models

type AreaMappings struct {
	AreaID         int `gorm:"primaryKey"`
	TargetAreaID   string
	TargetAreaName string
	PlatForm       PlatForm `gorm:"primaryKey"`
	Ext            string   // 扩展字段，譬如 百姓网需要存 城市的拼音
}

var directCitis = []string{"北京", "天津", "上海", "重庆", "香港", "澳门", "台湾"}

func IsDirectCity(name string) bool {
	for _, city := range directCitis {
		if city == name {
			return true
		}
	}
	return false
}
