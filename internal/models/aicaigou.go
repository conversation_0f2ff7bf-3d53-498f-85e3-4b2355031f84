package models

import (
	"encoding/json"
	"git.paihang8.com/lib/goutils"
	"gitlab.com/all_publish/api/pkg/dbtypes"
)

type AicaigouUsers struct {
	CompanyName       string            `json:"company_name,editable" validate:"required" label:"公司名称"`            // 与执照上公司名一致
	CompanyType       string            `json:"company_type,editable" validate:"required" label:"公司类型"`            // 企业或个体工商户
	SocialCreditCode  string            `json:"social_credit_code,editable" validate:"required" label:"社会统一信用代码"`  // 社会统一信用代码
	LinkPerson        string            `json:"link_person,editable" validate:"required" label:"联系人"`              //联系人
	LinkPhone         string            `json:"LinkPhone,editable" validate:"required" label:"注册手机号"`              // 注册手机号
	CompanyAreaIds    dbtypes.JSONArray `gorm:"type:json;column:company_areaids"  json:"company_areaids,editable"` // 公司地址 必填字段 ["省id","市id","县id"], 至少选到第二级
	BusinessImg       string            `json:"business_img,editable" validate:"required" label:"营业执照url路径"`       // 营业执照url路径
	LinkEmail         string            `json:"link_email,editable" validate:"required" label:"邮箱地址"`              // 邮箱地址
	CompanyLogo       string            `json:"company_logo,editable" validate:"required" label:"公司logo"`          // 公司logo 170x170 jpg
	CompanyWeb        string            `json:"company_web,editable" validate:"required" label:"公司网址"`             // 公司网址
	ContractBeginDate string            `json:"contract_begin_date,editable" validate:"required" label:"合同开始日期"`   // 合同开始日期 yyyy-MM-dd格式
	ContractEndDate   string            `json:"contract_end_date,editable" validate:"required" label:"合同结束日期"`     // 合同结束日期 yyyy-MM-dd格式
	ContractFile      string            `json:"contract_file,editable" validate:"required" label:"合同链接URL"`        // 合同链接URL 8M以内，pdf格式
	ProductType       int               `json:"product_type,editable"  label:"产品套餐类型"`                             // 产品套餐类型(Int类型) 默认1
	BrankName         string            `json:"brank_name,editable" validate:"required" label:"银行名称"`              // 银行名称
	BankAreaIds       dbtypes.JSONArray `gorm:"type:json;column:bank_areaids"  json:"bank_areaids,editable"`       // 银行地址 必填字段 ["省id","市id","县id"], 至少选到第二级
	OpenBranch        string            `json:"open_branch,editable" validate:"required" label:"开户支行"`             // 开户支行
	InterBankNum      string            `json:"inter_bank_num,editable"  label:"银联号"`                              // 银联号
	CardNumber        string            `json:"card_number"`                                                       // 银行卡号

	CompanyID uint64 `gorm:"primary_key" json:"company_id"` // 关联登录用户
}

func (u AicaigouUsers) Hash() string {
	s, _ := json.Marshal(u)
	return goutils.Md5str(string(s))
}
