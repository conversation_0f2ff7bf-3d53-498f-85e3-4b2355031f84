package models

type AreaType int8

const (
	AreaTypeNone        = 100 //其实是没数据
	AreaTypeProvince    = 0   //省
	AreaTypeDirectyCity = 2   //直辖市

	AreaTypeCity               = 1 //市
	AreaTypeCountyOfDirectCity = 3 //  直辖市下面的区/县

	AreaTypeCountyOfCity = 4 //区/县
)

type Area struct {
	ID          uint `gorm:"primaryKey"`
	AreaName    string
	AreaEn      string `gorm:"unique"`
	ParentID    uint   `gorm:"index:parent_id;default:0"`
	AreaTrace   string `gorm:"default:''"`
	AreaTraceEn string `gorm:"default:''"`
	OrderID     uint8  `gorm:"index:order_id;default:0"`
	Type        uint8  `gorm:"default:0"`
	CNum        int    `gorm:"default:0"`
	HotID       uint8  `gorm:"default:0"`
	HomeID      uint8  `gorm:"default:0"`
	SiteID      uint8  `gorm:"default:0"`
	SiteHot     bool   `gorm:"default:0"`
}
