package models

import (
	"github.com/go-playground/validator/v10"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"testing"
)

func TestTranslate(t *testing.T) {

	t.Run("CompanyReq", func(t *testing.T) {
		m := Company{
			Base:        dbtypes.Base{},
			ID:          0,
			Name:        "",
			ShortName:   "",
			Introduce:   "",
			MainProduct: "",
		}
		err := Validate.Struct(m)
		if err != nil {
			errStr := Translate(err.(validator.ValidationErrors))
			t.<PERSON>rror(errStr)
		}
	})
}
