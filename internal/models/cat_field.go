package models

type CatField struct {
	ID           int    `gorm:"primaryKey;column:id;type:int;not null"`
	Parentid     int    `gorm:"column:parentid;type:int;not null"`
	Catid        int    `gorm:"index:catid;column:catid;type:int;not null"`
	Displayname  string `gorm:"column:displayname;type:varchar(254);not null"`
	Hintinline   string `gorm:"column:hintinline;type:varchar(254);not null"`
	Hint<PERSON><PERSON>ine  string `gorm:"column:hintnewline;type:varchar(254);not null"`
	Errormessage string `gorm:"column:errormessage;type:varchar(254);not null"`
	Prependtext  string `gorm:"column:prependtext;type:text;not null"`
	Appendtext   string `gorm:"column:appendtext;type:text;not null"`
	Hasother     int8   `gorm:"column:hasother;type:tinyint;not null"`
	Hasplease    int8   `gorm:"column:hasplease;type:tinyint;not null;default:1"`
	Fieldsort    int    `gorm:"index:fieldsort;column:fieldsort;type:int;not null"`
	Fieldtype    string `gorm:"column:fieldtype;type:varchar(254);not null"`
	Fieldoptions string `gorm:"column:fieldoptions;type:text;not null"`
	Defaultvalue string `gorm:"column:defaultvalue;type:varchar(254);not null"`
	Attrstyle    string `gorm:"column:attrstyle;type:text;not null"`
	Attrclass    string `gorm:"column:attrclass;type:text;not null"`
	Attrevent    string `gorm:"column:attrevent;type:text;not null"`
	Verification string `gorm:"column:verification;type:varchar(254);not null"`
	Childid      int    `gorm:"index:childid;column:childid;type:int;not null"`
	Infilter     int8   `gorm:"column:infilter;type:tinyint;not null"`
	Inlisting    int8   `gorm:"column:inlisting;type:tinyint;not null"`
	Isinline     int8   `gorm:"column:isinline;type:tinyint;not null;default:1"`
	Isindex      int8   `gorm:"column:isindex;type:tinyint;not null;default:0"`
	Isrequred    int8   `gorm:"column:isrequred;type:tinyint;not null"`
	Isenable     int8   `gorm:"index:isenable;column:isenable;type:tinyint;not null;default:1"`
	Cat          *Cat   `gorm:"foreignKey:catid" json:"advisor"`
	HasChild     bool   `gorm:"-" json:"has_child"`
}

func (CatField) TableName() string {
	return "hy_cat_field"
}
