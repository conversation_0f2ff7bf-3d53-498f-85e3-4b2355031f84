package models

import (
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"time"
)

type SystemUser struct {
	dbtypes.Base
	ID          uint64     `gorm:"primary_key" json:"id"`
	Name        string     `json:"name,editable"`
	Username    string     `gorm:"not null" json:"username,omitempty"`
	Password    string     `gorm:"not null"`
	Role        *string    `json:"role,editable"`
	Phone       *string    `json:"phone,editable"`
	Email       *string    `json:"email,editable"`
	LastLoginAt *time.Time `json:"last_login_at,omitempty"`
	Ban         bool       `json:"ban,editable"`

	Scopes dbtypes.JSONArray `gorm:"type:json;column:scopes" json:"scopes,editable"`
}

func (SystemUser) TableName() string {
	return "system_user"
}
