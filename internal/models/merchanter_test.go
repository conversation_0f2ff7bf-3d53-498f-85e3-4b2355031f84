package models

import (
	"regexp"
	"strings"
	"testing"
)

func TestGetMerchant(t *testing.T) {
	m1, _ := GetMerchant(PlatformHy88)
	m2, _ := GetMerchant(PlatformHy88)
	m1.UpdateAccount("name", 1)
	t.Log(m1.Account())
	t.Log(m2.Account())
}

func TestReplace(t *testing.T) {
	t.Run("with <p>", func(t *testing.T) {
		var source = "<p>hello,</p> <p>this is a table</p>"
		var text = "<hr/>"
		preg := regexp.MustCompile("</p>")
		result := ""
		matched := preg.FindAllStringSubmatchIndex(source, -1)
		var pre = 0
		for i, match := range matched {
			result += source[pre:match[0]] + text + source[match[0]:match[1]]
			pre = match[1]
			if i > 5 {
				result += source[pre:match[0]] + source[match[0]:match[1]]
			}
		}
		t.Log(result)
	})

	t.<PERSON>("no <p>", func(t *testing.T) {
		var source = "hello, this is a table"
		var text = "<hr/>"

		if !strings.Contains(source, "</p>") {
			target := text + "<br/>" + source
			t.Log(target)
		}
	})
}
