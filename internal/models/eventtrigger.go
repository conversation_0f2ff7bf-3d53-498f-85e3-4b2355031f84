package models

import (
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"gitlab.com/all_publish/api/internal/schemas"
)

/*
*
各个平台自己的触发事件
*/
type EventTrigger interface {
	OnCompanyEdit(c *Company)
	OnUserLogin(u *User, config publisher.PlatformConfig, client Merchanter)  // 用户登录时，user表已有数据
	OnFirstLogin(u *User, config publisher.PlatformConfig, client Merchanter) // 用户首次登陆
	AutoReg(u *User, merchant *Merchant)
	AutoFlow(merchant *Merchant, c *Company, userTrigger ...bool) bool // 状态自动流转
	ValidateCompany(c Company) error
	CalSteps(m *Merchant, c Company) schemas.MerchantStep
	FormatAccount(m *Merchant) map[string]interface{}
	BindUser(m *Merchant, mobile int, password string) error
}

var eventMap map[PlatForm]EventTrigger

func init() {
	eventMap = map[PlatForm]EventTrigger{}
}

func RegisterTrigger(name PlatForm, merchanter EventTrigger) bool {
	if _, ok := eventMap[name]; ok {
		return true
	}
	eventMap[name] = merchanter
	return true
}

/*
*
这里不能直接返回。。每个公司的账号不一样。需要clone一个。然后共享token
*/
func GetTrigger(name PlatForm) (EventTrigger, bool) {
	if m, ok := eventMap[name]; ok {
		return m, true
	}
	return nil, false
}
