package models

import (
	"gitlab.com/all_publish/api/pkg/dbtypes"
)

// 记录的全是推送成功的信息
type Infos struct {
	dbtypes.Base
	Id           uint64            `gorm:"primary_key" json:"id"`                               //
	Title        string            `gorm:"type:varchar(100);not null;default:''" json:"title"`  // 标题
	Keywords     dbtypes.JSONArray `gorm:"type:json;column:keywords" json:"keywords,omitempty"` // 关键词
	CompanyID    uint64            `gorm:"index:company_id;not null" json:"company_id"`         // 公司id
	Url          string            `gorm:"index:url_form;" json:"url"`                          // 第三方平台信息url
	Platform     PlatForm          `gorm:"index:url_form;" json:"platform"`                     // 哪个平台的信息
	PlatFormName string            `gorm:"-" json:"plat_form_name"`                             // 平台名称
	MID          uint64            `gorm:"index:m_id;" json:"mid"`                              //  第三方平台信息id
}
