package models

import (
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/datatypes"
	"time"
)

type CompanyProduct struct {
	dbtypes.Base
	ID      uint64            `gorm:"primary_key" json:"id"`
	Subject string            `gorm:"index:subject;not null" json:"subject"`
	Price   float32           `json:"price"`
	Unit    string            `json:"unit"`
	Pic     dbtypes.JSONArray `gorm:"type:json;column:pic" json:"pic,omitempty"`

	ProductID uint64  `gorm:"index:company_product_product_id" json:"product_id"` //为空时用来判断是否自动发布的。必须用指针
	Product   Product `json:"product,omitempty"`

	Content string `gorm:"type:text;" json:"content"`
	Status  int    `json:"status"`

	PubRes datatypes.JSONMap `gorm:"type:json;column:pub_res" json:"pub_res,omitempty"`

	CompanyID    uint64     `gorm:"index:company_product_company_id" json:"company_id"`
	Company      *Company   `json:"company,omitempty"`
	WillPubAt    *time.Time `json:"will_pub_at"`
	FailedReason string     `gorm:"column:failed_reason" json:"failed_reason"`
	FailedTimes  int        `gorm:"default:0" json:"failed_times"`
}

func (CompanyProduct) TableName() string {
	return "company_product"
}
