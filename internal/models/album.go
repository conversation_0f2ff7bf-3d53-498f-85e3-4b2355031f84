package models

import (
	"gitlab.com/all_publish/api/pkg/dbtypes"
)

const AlbumMax = 100

type Album struct {
	dbtypes.Base
	ID           uint64 `gorm:"primary_key" json:"id"`                  // 主键自增id
	Name         string `json:"name,editable"`                          // 相册名字
	Cover        string `json:"cover,editable"`                         // 封面
	IsOpen       uint8  `json:"is_open,editable"`                       // 是否公开，暂未启用。
	Sizes        int    `json:"sizes"`                                  // 文件大小综合。单位字节
	Total        int    `json:"total"`                                  // 文件数
	DisplaySizes int    `json:"display_sizes"`                          // 用户可见总大小
	DisplayTotal int    `json:"display_total"`                          // 用户可见文件数
	Description  string `gorm:"type:text;" json:"description,editable"` // 相册描述
	Status       int    `json:"status,editable"`                        // 相册状态, 暂未启用
	CompanyId    uint64 `gorm:"index" json:"company_id"`                // 公司id, 因为其他表和接口都是用的companyId, 统一使用这个。
	Main         bool   `json:"main"`                                   // 主相册，也是默认相册。名字不能修改
}
