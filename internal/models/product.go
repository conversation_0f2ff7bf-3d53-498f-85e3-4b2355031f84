package models

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"git.paihang8.com/lib/goutils"
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"github.com/mitchellh/mapstructure"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/utils"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const (
	AuditFieldCate     = "cate"
	ProductModeDefault = 0 // 默认模式
	ProductModeCustom  = 1 // 用户自定义模式
	// cache 的支持的key

	CacheKeyAlias     = "alias"
	CacheKeyKeywords  = "keywords"
	CacheKeySubtitles = "subtitles"
	// vars 支持的key

	VarsKeyAlias     = "别名组"
	VarsKeyKeywords  = "关键词组"
	VarsKeySubTitles = "副标题组"
	VarsKeyImages    = "图片组"
)

/*
Product
关键词 word
产品名称 name
副标题 title
产品别名 alias
品牌 brand
*/
type Product struct {
	dbtypes.Base
	ID    uint64            `gorm:"primary_key" json:"id"`
	Name  string            `json:"name,editable" gorm:"type:varchar(255)"`
	Brand string            `json:"brand,editable" gorm:"type:varchar(255)"` //必填项
	Price *float32          `json:"price,editable"`
	Unit  string            `json:"unit,editable" gorm:"type:varchar(255)"`
	Pic   dbtypes.JSONArray `gorm:"type:json;column:pic" json:"pic,editable"`
	//新增标题图片HY-5170
	TitlePic dbtypes.JSONArray `gorm:"type:json;column:titlepic" json:"titlepic,editable"`
	Videos   dbtypes.JSONArray `gorm:"type:json;column:videos" json:"videos,editable"` // 视频
	Word     dbtypes.JSONArray `gorm:"type:json;column:word" json:"word,editable"`

	Title       dbtypes.JSONArray `gorm:"type:json;column:title" json:"title,editable"`               // 副标题
	OptionTitle dbtypes.JSONArray `gorm:"type:json;column:option_title" json:"option_title,editable"` // 候选标题，需要对比usedTitle使用
	UsedTitle   dbtypes.JSONArray `gorm:"type:json;column:used_title" json:"used_title"`              // 已使用标题

	//Description dbtypes.JSONArray `gorm:"type:json;column:description" json:"description,editable"` 已合并到material

	Material dbtypes.JSONArray `gorm:"type:json;column:material" json:"material,editable"`

	CompanyID     uint64            `gorm:"index:company_id" json:"company_id"`
	Company       *Company          `json:"company,editable"`
	User          *User             `gorm:"-" json:"user"`
	Status        uint8             `json:"status"`
	LastAuditRes2 datatypes.JSONMap `gorm:"type:json;column:last_audit_res2" json:"last_audit_res2"`
	InnerStatus   int               `json:"-"` // 中间状态，程序用。标识还能不能往池子里生成信息。
	MinOrder      *int              `json:"min_order,editable"`
	Inventory     *int              `json:"inventory,editable"`
	Faq           string            `gorm:"type:text;" json:"faq,editable"`
	AuditRes      *string           `json:"audit_res" gorm:"type:varchar(255)"`
	AuditRes2     datatypes.JSONMap `gorm:"type:json;column:audit_res2" json:"audit_res2,editable"`

	ModifyFields dbtypes.JSONArray `gorm:"type:json;column:modify_fields" json:"modify_fields"`

	Alias    dbtypes.JSONArray `gorm:"type:json;column:alias" json:"alias,editable"`
	UseAlias bool              `gorm:"default:1" json:"use_alias,editable"`

	//管理员保存草稿 和 生成redis索引队列时设置
	HashTitle   string            `json:"-" gorm:"type:varchar(255)"` //用于对比，如果重新提交产品时变了，需要重新生成标题索引
	HashSubject string            `json:"-" gorm:"type:varchar(255)"` //用于对比，如果重新提交产品时变了，需要重新生成标题索引
	HashContent string            `json:"-" gorm:"type:varchar(255)"` //用于对比，如果重新提交产品时变了，需要重新生成内容索引
	Cate        dbtypes.JSONArray `gorm:"type:json;column:cate" json:"cate,omitempty,editable"`
	CateNames   dbtypes.JSONArray `gorm:"-" json:"cate_names"` // 分类名字，和 cate 一一对应

	//["省id","市id","县id"], 都填0，表示全国
	AreaIds   dbtypes.JSONArray `gorm:"type:json;column:areaids" json:"areaids,editable"`
	AreaNames dbtypes.JSONArray `gorm:"-" json:"area_names"` // 和 areaids 对应的名字

	Properties datatypes.JSONMap `gorm:"type:json;column:properties" json:"properties,omitempty,editable"`

	ParentId  uint64            `gorm:"index:parent_id" json:"-"` //parentId parentId=id表示主产品，否则为历史副本。
	CurrentId uint64            //CurrentId 当前使用的版本号
	Active    int               //是否活跃版本。已有改成active=1为活跃版本。
	Platforms datatypes.JSONMap `gorm:"type:json;column:platforms" json:"platforms,omitempty,editable"` // {'1':{name:'黄页88',on:true}, '2':{name:'八方资源',on:false}}
	BadWords  datatypes.JSONMap `gorm:"-" json:"bad_words"`                                             // 违禁词
	ChangedAt time.Time         `gorm:"type:datetime" json:"changed_at,omitempty"`                      // 内容改变时间

	// 支持用户自定义
	Mode        int               `gorm:"default:0" json:"mode"`                        // 产品模式 0:默认 1:用户自定义
	Cache       datatypes.JSONMap `gorm:"type:json;column:cache" json:"cache,editable"` // 页面缓存项目, keywords, alias, subtitles
	TempContent string            `gorm:"temp_content" json:"temp_content,editable"`    // 模板，占位符填充变量。
	Vars        datatypes.JSONMap `gorm:"type:json;column:vars" json:"vars,editable"`   // 变量。预定义：图片组,关键词组, 别名组, 附标题组

}

func (p *Product) AfterFind(tx *gorm.DB) error {
	if p.ChangedAt.IsZero() {
		p.ChangedAt = p.CreatedAt
	}
	return nil
}

type Platforms map[PlatForm]PlatformConfig

func (config PlatformConfig) ToMap() map[string]interface{} {
	var m map[string]interface{}
	mapstructure.Decode(config, &m)
	return m
}

func (p Platforms) ToMap() map[string]interface{} {
	m := make(map[string]interface{})
	for k, v := range p {
		kk := strconv.Itoa(int(k))
		m[kk] = v.ToMap()
	}
	return m
}

func (p *Platforms) Init(m map[string]interface{}) error {
	pp := make(Platforms)
	for k, v := range m {
		kk, err := strconv.Atoi(k)
		if err != nil {
			return err
		}
		vv := v.(map[string]interface{})
		if _, ok := vv["on"]; !ok {
			vv["on"] = true
		}
		pp[PlatForm(kk)] = PlatformConfig{
			Name: vv["name"].(string),
			On:   vv["on"].(bool),
		}

	}
	*p = pp
	return nil
}

type PlatformConfig struct {
	Name string `json:"name" mapstructure:"name"`
	On   bool   `json:"on" mapstructure:"on"`
}

func (p *Product) SetPlatformsByforms(pfs []PlatForm) {
	ps := make(Platforms)
	for _, item := range pfs {
		ps[item] = PlatformConfig{
			Name: PlatFormName(item),
			On:   true,
		}
	}
	var dbps Platforms
	dbps.Init(p.Platforms)
	for k, v := range dbps {
		ps[k] = v
	}
	p.Platforms = ps.ToMap()
}

func (Product) TableName() string {
	return "product"
}

func (p *Product) BeforeSave(tx *gorm.DB) error {
	//fix error data, which produced on 管理web
	if len(p.Alias) == 1 && p.Alias[0] == "" {
		p.Alias = nil
	}
	return nil
}

/*
 */
func (p *Product) IsValid() (bool, error) {
	if p.Mode == ProductModeDefault {
		if len(p.Material) < 15 && p.Status != dbtypes.ProductStatusPromote {
			return false, fmt.Errorf("素材段落个数需要大于15篇")
		}
	}

	if len(p.Cate) < 3 {
		return false, configs.ErrProductCate
	} else {
		for _, cate := range p.Cate {
			num, _ := strconv.Atoi(strings.TrimSpace(cate))
			if num <= 0 {
				return false, configs.ErrProductCate
			}
		}
	}

	return true, nil
}

func (c *Product) GetAreaID() int {
	if len(c.AreaIds) > 0 {
		for i := len(c.AreaIds) - 1; i >= 0; i-- {
			if c.AreaIds[i] != "0" {
				id, _ := strconv.Atoi(c.AreaIds[i])
				return id
			}
		}
	}
	return 0
}

func (c *Product) GetAreaIds() []int {
	ids := []int{}
	for _, id := range c.AreaIds {
		if id != "0" {
			id, _ := strconv.Atoi(id)
			ids = append(ids, id)
		} else {
			break
		}
	}
	return ids
}

func (c *Product) GetCatID() int {
	if len(c.Cate) > 0 {
		for i := len(c.Cate) - 1; i >= 0; i-- {
			if c.Cate[i] != "0" {
				id, _ := strconv.Atoi(c.Cate[i])
				return id
			}
		}
	}
	return 0
}

func (c *Product) SplitHy88Properties() (map[string]interface{}, map[string]interface{}) {
	other := make(map[string]interface{})
	if len(c.Properties) == 0 {
		return c.Properties, other
	}
	m := make(map[string]interface{})
	for k, v := range c.Properties {
		if goutils.IsNumeric(k) {
			m[k] = v
		} else {
			other[k] = v
		}
	}
	return m, other
}

func (c *Product) RandVideo() *publisher.Video {
	if len(c.Videos) == 0 {
		return nil
	}
	for i := len(c.Videos); i > 0; i-- {
		lastIdx := i - 1
		idx := rand.Intn(i)
		c.Videos[lastIdx], c.Videos[idx] = c.Videos[idx], c.Videos[lastIdx]
	}
	var video publisher.Video
	var videoData publisher.GetVideoResponseData
	err := json.Unmarshal([]byte(c.Videos[0]), &videoData)
	if err != nil {
		return nil
	}
	video.Title = videoData.Title
	video.Videoid = strconv.FormatInt(int64(videoData.ID), 10)
	video.Domain = videoData.Domain
	video.VideoPath = videoData.VideoPath
	return &video
}

func (p *Product) UrledPic() []string {
	urls := utils.UrlArr(p.Pic).ToUrls()
	return urls
}
func (p *Product) UrledTitlePic() []string {
	urls := utils.UrlArr(p.TitlePic).ToUrls()
	return urls
}

func (p *Product) RandPic(ms int) []string {
	urls := p.UrledPic()
	pickNum := len(urls)
	if ms < pickNum {
		pickNum = ms
	}

	shuffed := utils.Shuffle(urls)
	return shuffed[:pickNum]
}

func (p *Product) RandTitlePic() []string {
	urls := p.UrledTitlePic()
	if len(urls) < 6 {
		return urls
	}
	pickNum := 6
	shuffed := utils.Shuffle(urls)
	return shuffed[:pickNum]
}

// http://newjira.huangye88.net/browse/HY-4840
func (p *Product) RandWord(title string, allNames []string) []string {
	words, city := p.PickTags(title, allNames)
	var words2 []string
	for _, word := range p.Word {
		if strings.Contains(word, p.Name) {
			words1, city1 := p.PickTags(word, allNames)
			if city == "" && city1 != "" {
				city = city1
			}
			words2 = append(words2, words1...)
		} else {
			words2 = append(words2, word)
		}
	}
	if city != "" && p.tagOk(city+p.Name) {
		words = append(words, city+p.Name)
	}

	pickNum := 4 - len(words)
	//这个情况应该不存在
	if pickNum > len(words2) {
		pickNum = len(words2)
	}
	shuffed := utils.Shuffle(words2)
	t := shuffed[:pickNum]
	words = append(words, t...)
	return words
}

func (p *Product) PickTags(title string, allNames []string) ([]string, string) {
	var result []string
	var city string
	for _, w := range allNames {
		if strings.Contains(title, w) {
			city = w
		}
	}
	if strings.Contains(title, p.Name) {
		parts := strings.Split(title, p.Name)
		if len(parts) == 2 {
			if parts[0] != "" {
				if city != "" {
					parts[0] = strings.ReplaceAll(parts[0], city, "")
				}
				if parts[0] != "" && p.tagOk(parts[0]+p.Name) {
					//1.2.1
					result = append(result, parts[0]+p.Name)
				}
			}
			if parts[1] != "" && p.tagOk(p.Name+parts[1]) {
				//1.2.2
				result = append(result, p.Name+parts[1])
			}
		} else {
			if p.tagOk(parts[0] + p.Name) {
				result = append(result, parts[0]+p.Name) //1.3.1
			}
			if p.tagOk(p.Name + parts[len(parts)-1]) {
				result = append(result, p.Name+parts[len(parts)-1]) //1.3.2
			}
		}
	}
	return result, city
}

func (c *Product) tagOk(tag string) bool {
	return utf8.RuneCountInString(tag) <= 10
}
