package models

import (
	"gitlab.com/all_publish/api/pkg/dbtypes"
)

type ImageStatus uint8
type ImageUsing uint8

const (
	ImageStatusEnable  = 0 // 启用状态
	ImageStatusDisable = 1 // 禁用状态。。删除就是禁用
	ImageUsingFalse    = 0 // 未使用状态, 保留。如果是使用状态，是不能真正删除图片的。
	ImageUsingTrue     = 1 // 使用状态
)

type Image struct {
	dbtypes.Base
	ID        uint64 `gorm:"primary_key" json:"id"`              // 主键自增id
	Name      string `json:"name,editable"`                      // 文件名字, 支持修改
	Tag       string `json:"tag,editable"`                       // tag 自定义标签, 支持修改，暂时没有启用
	Url       string `gorm:"index" json:"url"`                   // 文件url
	Size      int    `json:"size"`                               // 文件大小。单位字节
	Hash      string `gorm:"type:char(32) not null" json:"hash"` // 文件hash, 用于滤重的
	CompanyId uint64 `gorm:"index" json:"company_id"`            // 公司id
	AlbumId   uint64 `gorm:"index" json:"album_id"`
	Status    uint8  `gorm:"default:0" json:"status"` // 状态。 0 为可见状态。1 为不可见状态。删除图片就是标记为不可见状态
	Using     uint8  `gorm:"default:0" json:"using"`  // 0 未使用 1 已使用。 保留用。状态为0的可以真实删除。
}
