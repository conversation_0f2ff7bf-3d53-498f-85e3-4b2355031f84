package models

import "gitlab.com/all_publish/api/pkg/dbtypes"

type UserLoginHistory struct {
	dbtypes.Base
	OemId       int    `gorm:"index;not null" json:"oem_id"`
	Phone       int    `json:"phone"`      // 手机号
	CompanyId   uint64 `json:"company_id"` // 公司id
	LastLoginIp string `json:"last_login_ip" gorm:"column:last_login_ip;type:char(15);not null;default:''"`
	Version     string `json:"version" gorm:"default:''"`
}
