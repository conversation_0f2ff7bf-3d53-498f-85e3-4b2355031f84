package models

import "gitlab.com/all_publish/api/pkg/dbtypes"

type AiType int

const (
	AiTypeGpt      = 0 // chatGPt
	AiTypeWenxin   = 1 // 文心一言
	AiTypeDoubao   = 2 //   豆包大模型
	AiTypeDeepSeek = 3 //   深度求索
)

type ChatSearchRecord struct {
	dbtypes.Base
	CompanyId uint64 `json:"company_id"`
	Keyword   string `json:"keyword"`
	KwHash    string `json:"kwHash"`
	Cnt       int    `json:"cnt"`
	AiType    AiType `json:"aiType"`
}
