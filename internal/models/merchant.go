package models

import (
	"sync"

	"git.paihang8.com/lib/goutils/sites/china"
	"git.paihang8.com/lib/goutils/sites/sole"
	"github.com/mitchellh/mapstructure"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

/*
*
商户表。对应多个平台。
*/
type Merchant struct {
	dbtypes.Base
	ID              uint64     `gorm:"primary_key" json:"id"`
	CompanyID       uint64     `gorm:"index:company_id" json:"company_id"` // 这里是指發發助手的公司ID
	TargetCompanyID int        `json:"target_company_id"`                  // 第三方公司ID
	RegLock         sync.Mutex `json:"-" gorm:"-"`                         // 锁住注册，防止并发。
	CompanySite     string     `json:"company_site"`                       // 第三方公司的商铺网址
	Company         *Company   `json:"company"`
	Name            *string    `gorm:"not null" json:"name"`
	PlatForm        PlatForm   `gorm:"default:0" json:"plat_form"`
	PlatFormName    string     `gorm:"-" json:"plat_form_name"` // 平台名称

	Account datatypes.JSONMap `gorm:"type:json;column:account" json:"account,omitempty,editable"`

	Contact      string `json:"contact"`
	ContactPhone string `json:"contact_phone"`
	Pause        bool   `json:"pause,editable"`
	//控制信息自动发布
	PubCount     int                `gorm:"default:10" json:"pub_count,editable"`     // 每日信息发布数上限
	PubPerCount  int                `gorm:"default:10" json:"pub_per_count,editable"` // 单个产品每日发布量
	Total        int                `json:"total"`                                    // 发布总量。平台配置。特殊情况：-2 标示黄页88没开通。
	BaseCnt      int                `json:"base_cnt"`                                 // 基础信息条数
	RenewAddCnt  int                `json:"renew_add_cnt"`                            // 续费一次增加的条数
	PublishedCnt int                `json:"published_cnt"`                            // 已发布数量
	AutoPub      bool               `json:"auto_pub,editable"`
	AutoPubTime  *dbtypes.MysqlTime `gorm:"type:time" json:"auto_pub_time,editable"`
	PauseReason  string             `json:"pause_reason,editable"`
	ReMark       string             `json:"re_mark,editable"` // 备注
	//控制产品自动发布
	EnablePostProduct  bool               `json:"enable_post_product,editable"`
	DailyPubProducts   int                `json:"daily_pub_products,editable"`
	ProductAutoPubTime *dbtypes.MysqlTime `gorm:"type:time" json:"product_auto_pub_time,editable"`
	ProductPauseReason string             `json:"product_pause_reason,editable"`
	RegFailedReason    string             `json:"-"`               // 注册失败原因
	CertStatus         dbtypes.CertStatus `json:"-"`               // 证书状态,0-未知状态(一般是还没注册成功) 1-需要营业执照 2-等待提交 3-审核中 4-审核通过 5-审核拒绝
	CertData           datatypes.JSONMap  `json:"-"`               // 证书数据, 是个字典。cert_data["id"] = id cert_data["check"]=状态，cert_data["reason"]=原因
	Status             string             `gorm:"-" json:"status"` // 当前状态。文本描述。内容为空为正常
	Reason             string             `gorm:"-" json:"reason"` // 原因。
	Action             dbtypes.Action     `gorm:"-" json:"action"` // 需要进一步处理 0-不需要处理 1-上传营业执照 2-上传企业声明 3-编辑爱采购
	Ext                datatypes.JSONMap  `json:"ext"`
	DeletedAt          gorm.DeletedAt     `gorm:"index"`
	RenewCount         int                `json:"renew_count"` // 续费次数
}

func (Merchant) TableName() string {
	return "merchant"
}

func (m *Merchant) SetAccount(account interface{}) {
	m.Account = dbtypes.Struct2Map(account)
}

func (m *Merchant) SetExt(ext interface{}) {
	m.Ext = dbtypes.Struct2Map(ext)
}

func (m *Merchant) SetCertData(certData interface{}) {
	m.CertData = dbtypes.Struct2Map(certData)
}

func (m *Merchant) CalStatus() {
	status := ""
	reason := ""
	action := dbtypes.ActionNone
	if m.TargetCompanyID == 0 && m.PlatForm != PlatformBaixing && m.PlatForm != PlatformHy88 {
		status = "等待注册"
	}
	if m.RegFailedReason != "" {
		status = "注册异常"
		reason = m.RegFailedReason
	}
	switch m.CertStatus {
	case dbtypes.CertStatusNeedRegNo:
		status = "需要补全资料"
		reason = "需要营业执照"
		action = dbtypes.ActionUploadLincense
	case dbtypes.CertStatusNeedSubmit:
		status = "正在提交认证"
	case dbtypes.CertStatusSubmitFailed:
		status = "提交认证出错"
	case dbtypes.CertStatusSubmitted:
		status = "企业认证等待审核"
	case dbtypes.CertStatusRejected:
		status = "企业认证审核拒绝"
		reason = m.CertData["reason"].(string)
	}
	if m.PlatForm == PlatformBafang {
		var ext merchants.BafangExt
		mapstructure.Decode(m.Ext, &ext)
		switch ext.Annonce.Status {
		case dbtypes.CertStatusNeedRegNo:
			status = "企业声明待上传"
			reason = "需要企业声明"
			action = dbtypes.ActionUploadAnnounce
		case dbtypes.CertStatusNeedSubmit:
			status = "正在提交企业声明"
		case dbtypes.CertStatusSubmitted:
			status = "企业声明等待审核"
		case dbtypes.CertStatusRejected:
			status = "企业声明审核拒绝"
			reason = ext.Annonce.Reason
			action = dbtypes.ActionUploadAnnounce
		}
	}
	if m.PlatForm == PlatformSoleExt {
		var account merchants.AicaigouAccount
		mapstructure.Decode(m.Account, &account)
		if m.TargetCompanyID == 0 {
			action = dbtypes.ActionUploadLincense
			status = "等待注册搜了网"
			reason = m.RegFailedReason
		} else {
			if account.SoleCertStatus == dbtypes.CertStatusRejected {
				action = dbtypes.ActionUploadLincense
				status = "搜了网审核拒绝"
				reason = account.SoleCertReason
			} else if account.SoleCertStatus == dbtypes.CertStatusPassed {
				if !account.Joined {
					action = dbtypes.ActionEditAicaigou
					status = "等待注册爱采购"
					reason = ""
				} else {
					switch account.Status {
					case sole.UserAuditStatusAuditing:
						status = "等待审核"
						reason = ""
					case sole.UserAuditStatusRejected:
						status = "审核拒绝"
						status = account.Reason
						action = dbtypes.ActionEditAicaigou
					}
				}
			} else {
				status = "审核中..."
			}

		}

	}
	if m.PlatForm == PlatformLiebiao {
		var account merchants.LiebiaoAccount
		mapstructure.Decode(m.Account, &account)
		if account.Id == 0 {
			action = dbtypes.ActionUploadLincense
			status = "等待注册账号"
		} else {
			if m.TargetCompanyID > 0 {
				action = dbtypes.ActionNone
				status = ""
				reason = ""
			} else {
				//switch account.CertStatus {
				//case dbtypes.CertStatusRejected:
				//	action = dbtypes.ActionUploadLincense
				//	status = "身份证认证：审核拒绝"
				//	reason = account.Reason
				//case dbtypes.CertStatusPassed:
				switch m.CertStatus {
				case dbtypes.CertStatusRejected:
					action = dbtypes.ActionUploadLincense
					status = "营业执照认证：审核拒绝"
					reason = m.CertData["reason"].(string)
				case dbtypes.CertStatusPassed:
					action = dbtypes.ActionNone
					// status = "营业执照认证：审核通过"
					status = ""
					reason = ""
					// if account.ApplyId > 0 {
					// 	status = "申请商铺中"
					// }
				case dbtypes.CertStatusNeedSubmit:
					action = dbtypes.ActionUploadLincense
					status = "营业执照认证：待提交"
					reason = ""
				default:
					status = "营业执照认证：等待审核"
				}
				//default:
				//	status = "身份证认证：等待审核"
				//}

			}
		}
	}
	if m.Pause {
		status = "已暂停"
		reason = m.PauseReason
	}
	if !m.AutoPub {
		status = "自动发布已关闭"
	}

	if m.PlatForm == PlatformChina {
		if v, ok := m.Account["status"]; ok {
			if v == china.AuditingsStatusRejected {
				status = "公司审核拒绝"
			}
		}
	}
	m.Status = status
	m.Reason = reason
	m.Action = action
}
