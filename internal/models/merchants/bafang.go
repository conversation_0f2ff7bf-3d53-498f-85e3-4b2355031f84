package merchants

import (
	"github.com/mitchellh/mapstructure"
	"gitlab.com/all_publish/api/pkg/dbtypes"
)

type Annonce struct {
	Pic    string             `json:"pic" mapstructure:"pic"`
	Status dbtypes.CertStatus `json:"status" mapstructure:"status"`
	Id     string             `json:"id" mapstructure:"id"`
	Check  string             `json:"check" mapstructure:"check"`
	Reason string             `json:"reason" mapstructure:"reason"`
}

type BafangExt struct {
	Annonce Annonce `json:"annonce" mapstructure:"annonce"`
}

func (account BafangExt) ToMap() map[string]interface{} {
	var m map[string]interface{}
	mapstructure.Decode(account, &m)
	return m
}

func (account *BafangExt) Init(m map[string]interface{}) error {
	err := mapstructure.Decode(m, account)
	return err
}

type BafangAccount struct {
	UserName string `json:"username" mapstructure:"username"`
	Password string `json:"password" mapstructure:"password"`
	Id       int    `json:"id" mapstructure:"id"`
	Url      string `json:"url" mapstructure:"url"`
}

func (account BafangAccount) ToMap() map[string]interface{} {
	var m map[string]interface{}
	mapstructure.Decode(account, &m)
	m["id"] = float64(m["id"].(int))
	return m
}

func (account *BafangAccount) Init(m map[string]interface{}) error {
	err := mapstructure.Decode(m, account)
	return err
}
