package merchants

import "github.com/mitchellh/mapstructure"

type mapobjconvert interface {
	ToMap() map[string]interface{}
	Init(m map[string]interface{}) error
}

type CertData struct {
	Id      int    `json:"id" mapstructure:"id"`
	License string `json:"license" mapstructure:"license"`
	Check   string `json:"check" mapstructure:"check"`
	Reason  string `json:"reason" mapstructure:"reason"`
}

func (account CertData) ToMap() map[string]interface{} {
	var m map[string]interface{}
	mapstructure.Decode(account, &m)
	return m
}

func (account *CertData) Init(m map[string]interface{}) error {
	err := mapstructure.Decode(m, account)
	return err
}
