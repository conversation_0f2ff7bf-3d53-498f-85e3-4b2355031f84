package merchants

import (
	"encoding/json"
	"testing"
)

func TestChina_ToMap(t *testing.T) {
	var ext ChinaAccount
	m := make(map[string]interface{})
	data := `{"id": **********, "status": "-1", "password": "9d50c9d6", "username": "u96966"}`

	json.Unmarshal([]byte(data), &m)
	ext.Init(m)
	t.Log(ext.ToMap())
}

func TestChina_ToStruct(t *testing.T) {
	var ext ChinaAccount
	m := make(map[string]interface{})
	data := `{"id": **********, "status": "-1", "password": "9d50c9d6", "username": "u96966"}`

	json.Unmarshal([]byte(data), &m)
	err := ext.Init(m)
	if err != nil {
		t.<PERSON><PERSON>(err)
	} else {
		t.Log(ext)
	}
}
