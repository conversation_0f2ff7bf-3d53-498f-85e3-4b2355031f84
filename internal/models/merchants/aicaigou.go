package merchants

import (
	"fmt"
	"git.paihang8.com/lib/goutils"
	"git.paihang8.com/lib/goutils/sites/sole"
	"github.com/mitchellh/mapstructure"
	"gitlab.com/all_publish/api/pkg/dbtypes"
)

/**
有3个状态：
1. 用户状态
2. 实名认证状态
3. 合同状态

mapstructure 为结构体转map,map转结构体
json 为数据库用的
*/
type AicaigouAccount struct {
	Id             int                  `json:"id" mapstructure:"id"`
	Site           string               `json:"site" mapstructure:"site"`
	UserName       string               `mapstructure:"username" json:"username"`
	Password       string               `mapstructure:"password" json:"password"`
	Joined         bool                 `mapstructure:"joined" json:"joined"`                     // 是否已注册爱采购
	Hash           string               `mapstructure:"hash" json:"hash"`                         // 判断是否修改过信息
	Status         sole.UserAuditStatus `mapstructure:"status" json:"status"`                     // 爱采购注册状态
	Reason         string               `mapstructure:"reason" json:"reason"`                     // 爱采购用户拒绝原因
	Ordered        bool                 `mapstructure:"ordered" json:"ordered"`                   //是否已经下订单
	SoleCertStatus dbtypes.CertStatus   `mapstructure:"sole_cert_status" json:"sole_cert_status"` //搜了网的认证状态
	SoleCertReason string               `mapstructure:"sole_cert_reason" json:"sole_cert_reason"` // 搜了网的证书拒绝原因
	SoleCertPic    string               `mapstructure:"sole_cert_pic" json:"sole_cert_pic"`       // 搜了网上次提交的证书url
	ContractHash   string               `mapstructure:"contract_hash" json:"contract_hash"`       // 搜了网上次提交的证书url
}

func (account *AicaigouAccount) GetContractHash(file, startDate, endDate string) string {
	return goutils.Md5str(fmt.Sprintf("%s#%s#%s", file, startDate, endDate))
}

func (account AicaigouAccount) ToMap() map[string]interface{} {
	var m map[string]interface{}
	mapstructure.Decode(account, &m)
	return m
}

func (account *AicaigouAccount) Init(m map[string]interface{}) error {
	err := mapstructure.Decode(m, account)
	return err
}
