package merchants

import (
	"encoding/json"
	"testing"
)

func TestHy88_ToMap(t *testing.T) {
	var ext Hy88Account
	m := make(map[string]interface{})
	data := `{"id": "3420894", "token": "**************-7cf93054d61cfdca42b5a6dbf23b5169731841c4", "mobile": "***********", "password": "kangkang881005"}`

	json.Unmarshal([]byte(data), &m)
	ext.Init(m)
	t.Log(ext.ToMap())
}

func TestHy88_ToStruct(t *testing.T) {
	var ext Hy88Account
	m := make(map[string]interface{})
	data := `{"id": "3420894", "token": "**************-7cf93054d61cfdca42b5a6dbf23b5169731841c4", "mobile": "***********", "password": "kangkang881005"}`

	json.Unmarshal([]byte(data), &m)
	err := ext.Init(m)
	if err != nil {
		t.<PERSON><PERSON>(err)
	} else {
		t.Log(ext)
	}
}
