package merchants

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestBafang_ToMap(t *testing.T) {
	var ext BafangAccount
	m := make(map[string]interface{})
	data := `{"id": ********, "url": "http://u642200.cn.b2b168.com/", "password": "4b3dca66", "username": "u642200"}`

	json.Unmarshal([]byte(data), &m)
	ext.Init(m)
	assert.Equal(t, m, ext.ToMap())
}

func TestBafang_ToStruct(t *testing.T) {
	var ext BafangAccount
	m := make(map[string]interface{})
	data := `{"id": ********, "url": "http://u642200.cn.b2b168.com/", "password": "4b3dca66", "username": "u642200"}`

	json.Unmarshal([]byte(data), &m)
	err := ext.Init(m)
	if err != nil {
		t.Fatal(err)
	} else {
		t.Log(ext)
	}
}

func TestBafangExt_ToMap(t *testing.T) {
	var ext BafangExt
	m := make(map[string]interface{})
	data := `{"annonce": {"id": "1", "pic": "http", "check": "通过", "reason": "不告诉你", "status": 2}}`

	json.Unmarshal([]byte(data), &m)
	ext.Init(m)
	t.Log(ext.ToMap())
}

func TestBafangExt_ToStruct(t *testing.T) {
	var ext BafangExt
	m := make(map[string]interface{})
	data := `{"annonce": {"id": "1", "pic": "http", "check": "通过", "reason": "不告诉你", "status": 2}}`

	json.Unmarshal([]byte(data), &m)
	err := ext.Init(m)
	if err != nil {
		t.Fatal(err)
	} else {
		t.Log(ext)
	}
}
