package merchants

import "github.com/mitchellh/mapstructure"

type BaixingAccount struct {
	Mobile string `json:"mobile" mapstructure:"mobile"`
}

func (account BaixingAccount) ToMap() map[string]interface{} {
	var m map[string]interface{}
	mapstructure.Decode(account, &m)
	return m
}

func (account *BaixingAccount) Init(m map[string]interface{}) error {
	err := mapstructure.Decode(m, account)
	return err
}
