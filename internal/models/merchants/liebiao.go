package merchants

import (
	"github.com/mitchellh/mapstructure"
	"gitlab.com/all_publish/api/pkg/dbtypes"
)

type LiebiaoAccount struct {
	Id       int    `json:"id" mapstructure:"id"` // userId
	Phone    string `json:"phone" mapstructure:"phone"`
	SourceID string `json:"source_id" mapstructure:"source_id"`

	Account    string             `json:"account" mapstructure:"account"`
	UserName   string             `json:"username" mapstructure:"username"`
	CertStatus dbtypes.CertStatus `json:"cert_status" mapstructure:"cert_status"` // 个人认证状态
	Reason     string             `json:"reason" mapstructure:"reason"`           // 认证失败原因
	ApplyId    int                `json:"apply_id" mapstructure:"apply_id"`
}

func (account LiebiaoAccount) ToMap() map[string]interface{} {
	var m map[string]interface{}
	mapstructure.Decode(account, &m)
	return m
}

func (account *LiebiaoAccount) Init(m map[string]interface{}) error {
	err := mapstructure.Decode(m, account)
	return err
}
