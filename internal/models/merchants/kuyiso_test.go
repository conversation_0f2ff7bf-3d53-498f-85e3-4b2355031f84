package merchants

import (
	"encoding/json"
	"testing"
)

func TestKuyiso_ToMap(t *testing.T) {
	var ext KuyisoAccount
	m := make(map[string]interface{})
	data := `{"id": 16, "uid": 11654, "mobile": "***********", "password": "2yA>~ngI","is_personal":true}`

	json.Unmarshal([]byte(data), &m)
	ext.Init(m)
	t.Log(ext.ToMap())
}

func TestKuyiso_ToStruct(t *testing.T) {
	var ext KuyisoAccount
	m := make(map[string]interface{})
	data := `{"id": 16, "uid": 11654, "mobile": "***********", "password": "2yA>~ngI","is_personal":true}`

	json.Unmarshal([]byte(data), &m)
	err := ext.Init(m)
	if err != nil {
		t.<PERSON><PERSON>(err)
	} else {
		t.Log(ext)
	}
}
