package merchants

import (
	"git.paihang8.com/lib/goutils/sites/china"
	"github.com/mitchellh/mapstructure"
)

type ChinaAccount struct {
	Id       int                   `json:"id" mapstructure:"id"`
	Status   china.AuditingsStatus `json:"status" mapstructure:"status"`
	UserName string                `json:"username" mapstructure:"username"`
	Password string                `json:"password" mapstructure:"password"`
	Reason   string                `json:"reason" mapstructure:"reason"`
}

func (account ChinaAccount) ToMap() map[string]interface{} {
	var m map[string]interface{}
	mapstructure.Decode(account, &m)
	return m
}

func (account *ChinaAccount) Init(m map[string]interface{}) error {
	err := mapstructure.Decode(m, account)
	return err
}
