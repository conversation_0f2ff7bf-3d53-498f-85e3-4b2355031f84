package merchants

import (
	"encoding/json"
	"testing"
)

func TestSouhaohuo_ToMap(t *testing.T) {
	var ext SouhaohuoAccount
	m := make(map[string]interface{})
	data := `{"id": 5607825, "site": "http://e.51sole.com/u79965/", "password": "9nUCZ0Gf", "username": "u79965"}`

	json.Unmarshal([]byte(data), &m)
	ext.Init(m)
	t.Log(ext.ToMap())
}

func TestSouhaohuo_ToStruct(t *testing.T) {
	var ext SouhaohuoAccount
	m := make(map[string]interface{})
	data := `{"id": 5607825, "site": "http://e.51sole.com/u79965/", "password": "9nUCZ0Gf", "username": "u79965"}`

	json.Unmarshal([]byte(data), &m)
	err := ext.Init(m)
	if err != nil {
		t.<PERSON><PERSON>(err)
	} else {
		t.Log(ext)
	}
}
