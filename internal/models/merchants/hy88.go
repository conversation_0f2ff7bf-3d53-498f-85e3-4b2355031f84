package merchants

import "github.com/mitchellh/mapstructure"

type Hy88Account struct {
	Id       string `json:"id" mapstructure:"id"`
	Token    string `json:"token" mapstructure:"token"`
	Mobile   string `json:"mobile" mapstructure:"mobile"`
	Password string `json:"password" mapstructure:"password"`
}

func (account Hy88Account) ToMap() map[string]interface{} {
	var m map[string]interface{}
	mapstructure.Decode(account, &m)
	return m
}

func (account *Hy88Account) Init(m map[string]interface{}) error {
	err := mapstructure.Decode(m, account)
	return err
}
