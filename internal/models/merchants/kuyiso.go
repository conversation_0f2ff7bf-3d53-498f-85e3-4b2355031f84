package merchants

import "github.com/mitchellh/mapstructure"

type KuyisoAccount struct {
	Id         int    `json:"id" mapstructure:"id"`
	Uid        int    `json:"uid" mapstructure:"uid"`
	Mobile     string `json:"mobile" mapstructure:"mobile"`
	Password   string `json:"password" mapstructure:"password"`
	IsPersonal bool   `json:"is_personal" mapstructure:"is_personal"` // 是否个人身份
}

func (account KuyisoAccount) ToMap() map[string]interface{} {
	var m map[string]interface{}
	mapstructure.Decode(account, &m)
	return m
}

func (account *KuyisoAccount) Init(m map[string]interface{}) error {
	err := mapstructure.Decode(m, account)
	return err
}
