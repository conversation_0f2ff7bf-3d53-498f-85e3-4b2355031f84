package merchants

import (
	"encoding/json"
	"testing"
)

func TestDefaultConverter_ToMap(t *testing.T) {
	var ext AicaigouAccount
	m := make(map[string]interface{})
	data := `{"hash": "af99c9ceb64fbb9c783edeb98a3fc692", "joined": true, "reason": "", "status": "2", "ordered": true, "password": "9iE1VzYy", "username": "u88126", "sole_cert_pic": "https://img.paihang8.com/11888/0/8888.jpg", "sole_cert_reason": "", "sole_cert_status": 3}`

	json.Unmarshal([]byte(data), &m)
	ext.Init(m)
	t.Log(ext.ToMap())
}

func TestDefaultConverter_ToStruct(t *testing.T) {
	var ext AicaigouAccount
	m := make(map[string]interface{})
	data := `{"hash": "af99c9ceb64fbb9c783edeb98a3fc692", "joined": true, "reason": "", "status": "2", "ordered": true, "password": "9iE1VzYy", "username": "u88126", "sole_cert_pic": "https://img.paihang8.com/11888/0/8888.jpg", "sole_cert_reason": "", "sole_cert_status": 3}`

	json.Unmarshal([]byte(data), &m)
	err := ext.Init(m)
	if err != nil {
		t.Fatal(err)
	} else {
		t.Log(ext)
	}
}
