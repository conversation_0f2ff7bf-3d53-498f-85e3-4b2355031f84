package merchants

import "github.com/mitchellh/mapstructure"

type SoleAccount struct {
	Id       int    `json:"id" mapstructure:"id"`
	Site     string `json:"site" mapstructure:"site"`
	UserName string `json:"username" mapstructure:"username"`
	Password string `json:"password" mapstructure:"password"`
}

func (account SoleAccount) ToMap() map[string]interface{} {
	var m map[string]interface{}
	mapstructure.Decode(account, &m)
	return m
}

func (account *SoleAccount) Init(m map[string]interface{}) error {
	err := mapstructure.Decode(m, account)
	return err
}
