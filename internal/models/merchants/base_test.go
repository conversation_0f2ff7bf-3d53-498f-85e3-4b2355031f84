package merchants

import (
	"encoding/json"
	"testing"
)

func TestCertData_Init(t *testing.T) {
	var ext CertData
	m := make(map[string]interface{})
	data := `{"id": 845835, "check": "审核通过", "reason": "", "license": "https://img.paihang8.com/11905/0/营业执照（副本）_看图王.jpg"}`

	json.Unmarshal([]byte(data), &m)
	err := ext.Init(m)
	if err != nil {
		t.Fatal(err)
	} else {
		t.Log(ext)
	}
}

func TestCertData_ToMap(t *testing.T) {
	var ext CertData
	m := make(map[string]interface{})
	data := `{"id": 845835, "check": "审核通过", "reason": "", "license": "https://img.paihang8.com/11905/0/营业执照（副本）_看图王.jpg"}`

	json.Unmarshal([]byte(data), &m)
	ext.Init(m)
	t.Log(ext.ToMap())
}
