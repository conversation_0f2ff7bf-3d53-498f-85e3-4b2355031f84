package models

import "gitlab.com/all_publish/api/pkg/dbtypes"

type Rank struct {
	dbtypes.Base
	ID           uint64   `gorm:"primary_key" json:"id"`
	Eg           string   `json:"eg"`                      // 搜索引擎
	Snap         string   `json:"snap"`                    // 快照
	CompanyID    uint64   `json:"company_id"`              // 哪个用户的
	Rank         int      `json:"rank"`                    // 排名 页码+排名
	Url          string   `json:"url"`                     // 链接url
	Keyword      string   `json:"keyword"`                 // 关键词
	Platform     PlatForm `json:"platform"`                // 哪个平台的信息
	PlatFormName string   `gorm:"-" json:"plat_form_name"` // 平台名称
}
