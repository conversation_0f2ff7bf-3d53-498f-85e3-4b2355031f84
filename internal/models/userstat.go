package models

import (
	"gitlab.com/all_publish/api/pkg/dbtypes"
)

const PhotoGift = 1000 // 默认可上传1000张

type UserStat struct {
	dbtypes.Base
	CompanyID         uint64 `gorm:"primary_key" json:"company_id"` // 公司id
	BuyPhoto          int    `gorm:"default:0" json:"buy_photos"`   // 购买的图片数
	AlbumCnt          int    `gorm:"default:0" json:"album_cnt"`    // 相册数
	PhotoCnt          int    `json:"photo_cnt"`                     // 图片总数, 可查看的图片数，不包括删除的
	UploadedCnt       int    `json:"uploaded_cnt"`                  // 上传的图片数，包括被删除的 uploaded_cnt >= photo_cnt
	CanUploaded       int    `gorm:"-" json:"can_uploaded"`         // 可以上传的图片数。。可计算得出， 2000+buy_photos-uploaded_cnt
	DigKeywordLimit   int    `json:"dig_keyword_limit"`             // 挖掘关键词 可用次数
	DigMaterialsLimit int    `json:"dig_materials_limit"`           // 挖掘物料 可用次数
	DigKeywordUsed    int    `json:"dig_keyword_used"`              // 挖掘关键词 已用次数
	DigMaterialsUsed  int    `json:"dig_materials_used"`            // 挖掘物料 已用次数
}
