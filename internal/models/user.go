package models

import (
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/gorm"
	"time"
)

type User struct {
	dbtypes.Base
	ID             uint64     `gorm:"primary_key" json:"id"`
	Username       string     `json:"username"`
	Password       string     `json:"-"`
	Phone          string     `gorm:"index:phone;not null" json:"phone,editable"`
	Email          string     `json:"email,editable"`
	LastLoginAt    time.Time  `json:"last_login_at"`
	Ban            *bool      `json:"ban,editable"`
	OemId          int        `gorm:"unique;not null" json:"oem_id"`
	OemClass       string     `json:"oem_class"`
	CompanyID      uint64     `json:"company_id"`
	Company        Company    `json:"company,editable"`
	TotalItems     int        `json:"total_items"`
	DialyItems     int        `json:"dialy_items"`
	ExpireTime     *time.Time `json:"expire_time"`
	PostExpireTime *time.Time `json:"post_expire_time"`
	CanPost        bool       `json:"can_post" gorm:"-"`
	SeekExpireTime *time.Time `json:"seek_expire_time"`
	RankExpireTime *time.Time `json:"rank_expire_time"`

	PostBuyTime *time.Time                          `json:"post_buy_time"`
	SeekBuyTime *time.Time                          `json:"seek_buy_time"`
	RankBuyTime *time.Time                          `json:"rank_buy_time"`
	PostsMap    map[string]publisher.PlatformConfig `gorm:"-" json:"-"`
	PostNames   []PostName                          `gorm:"-" json:"post_names"`

	MaxProducts       int  `json:"max_products,editable"`
	DigKeywordLimit   int  `gorm:"-" json:"-"` // 挖掘关键词 可用次数
	DigMaterialsLimit int  `gorm:"-" json:"-"` // 挖掘物料 可用次数
	FromKuyiso        bool `gorm:"-" json:"-"` // 是否来自酷易搜
}

type PostName struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

func (User) TableName() string {
	return "user"
}

func (u *User) AfterFind(tx *gorm.DB) error {
	u.CanPost = u.PostExpireTime != nil && u.PostExpireTime.After(time.Now())
	return nil
}
