package models

import (
	"errors"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/regutils"
	"gorm.io/gorm"
	"strconv"
	"time"
	"unicode/utf8"
)

type Updater interface {
	Update(i *interface{})
}

type Company struct {
	dbtypes.Base
	ID           uint64            `gorm:"primary_key" json:"id"`
	Name         string            `json:"name,editable" `                                        // 公司名称, 必填字段, 不超过50字
	ShortName    string            `json:"short_name,editable"`                                   // 公司简称，51搜了需要
	Introduce    string            `gorm:"type:mediumtext;"  json:"introduce,omitempty,editable"` // 公司介绍, 必填字段
	MainProduct  string            `json:"main_product,editable" `                                // 主营产品, 必填项
	MainBrand    string            `json:"main_brand,editable"`                                   // 主要品牌(51sole必填)
	CompanyType  string            `json:"company_type,editable"`                                 // 公司类型(51sole必填)， 枚举类型：私营企业，国有企业，集体所有制企业，合资企业，外资企业，股份企业，个体经营，事业单位，社会团体，个人，其他
	WorkingModel string            `json:"working_model,editable"`                                // 经营模式(51sole必填),枚举类型：生产型，贸易型，服务型，政府，其他机构
	Cate         dbtypes.JSONArray `gorm:"type:json;column:cate"  json:"cate,editable"`           // 公司行业, 必填字段, 需要选到最后一级（第三级或第四级）
	CateNames    dbtypes.JSONArray `gorm:"-" json:"cate_names"`                                   // 分类名字，和 cate 一一对应
	AreaIds      dbtypes.JSONArray `gorm:"type:json;column:areaids"  json:"areaids,editable"`     // 公司地址 必填字段 ["省id","市id","县id"], 至少选到第二级
	AreaNames    dbtypes.JSONArray `gorm:"-" json:"area_names"`                                   // 和 areaids 对应的名字

	License string `json:"license,editable"` // 营业执照, 必填字段

	AdvisorID  uint64                 `gorm:"not null;default:1" json:"advisor_id,omitempty,editable"` // 管理员id, 后台用
	OpEndTime  *dbtypes.MysqlDateTime `gorm:"type:datetime" json:"op_end_time,editable"`               // 运营到期时间，默认为空
	SystemUser SystemUser             `gorm:"foreignKey:advisor_id" json:"advisor"`                    // 管理员信息

	AuditingFields dbtypes.JSONArray `gorm:"type:json;column:auditing_fields" json:"auditing_fields"` // 修改的字段, 未启用

	AuditRes *string `json:"audit_res,omitempty"` // 拒绝原因, 未启用
	//AuditCompanyFields

	Site    string `json:"site,omitempty,editable"`    // 黄页88网站,其他的平台在商户表里
	Address string `json:"address,omitempty,editable"` // 公司详细地址，展示用, 51sole 为必填字段
	Logo    string `json:"logo,omitempty,editable"`    // 公司Logo, 选填
	Qrcode  string `json:"qrcode,omitempty,editable"`  // 公司二维码
	Email   string `json:"email,editable"`             // 公司邮箱（51sole必填)

	Qq *string `json:"qq,omitempty,editable"` // QQ, --51sole

	RegNo string `gorm:"type:varchar(255)" json:"reg_no,editable"` // 	营业执照信息:统一社会信用代码 ,reg_no,string,0,50 必填,440306111877015 --列表网
	Legal string `gorm:"type:varchar(255)" json:"legal,editable"`  // 	营业执照信息:公司法人(法定代表人),legal,string,0,20 必填,舒开勇 -- 列表网
	/**
	营业执照信息:公司类型,corp_type,string,0,50 必填,有限责任公司-
	枚举- 中国供应商是个枚举类型,需要转换下。
	*/
	CorpType string `gorm:"type:varchar(255)" json:"corp_type,editable"` //

	RegMoney     string `gorm:"type:varchar(255)" json:"reg_money,editable"`     // 	营业执照信息:注册资金,reg_money,string,0,50 必填,10万人民币
	RegDate      string `gorm:"type:varchar(16)" json:"reg_date,editable"`       // 	营业执照信息:注册时间(成立时间),reg_date,string,0,15 必填,2014-12-19
	ValidPeriod  string `gorm:"type:varchar(32)" json:"valid_period,editable"`   // 	营业执照信息:有效时间(营业期限),valid_period,string,0,50 必填,2014-12-19 - 永续经营
	Business     string `gorm:"type:varchar(255)" json:"business,editable"`      // 营业执照信息:经营范围,business,string,0,1000 必填,数据库管理及技术开发；计算机系统分析；计算机技术咨询；计算机软、硬件的技术开发、系统集成及销售；电子商务的技术开发；经营电子商务；计算机网络技术的研发。(法律、行政法规、国务院决定规定在经营前须经批准的项目除外）
	RegAddr      string `gorm:"type:varchar(255)" json:"reg_addr,editable"`      // 	营业执照信息:注册地址(住所),reg_addr,string,0,100 必填,广东深圳
	RegAuthority string `gorm:"type:varchar(255)" json:"reg_authority,editable"` // 	营业执照信息:注册机构,reg_authority,string,0,100 必填,深圳市市场监督管理局宝安局
	CanEdit      bool   `gorm:"-" json:"can_edit"`                               // 是否能编辑
	Reason       string `gorm:"-" json:"reason"`                                 // 不能编辑的原因

	IDCardNo    string            `gorm:"type:varchar(18)" json:"id_card_no,editable"`            // 身份证号码 --列表网需求
	Gender      *int              `json:"gender,omitempty,editable"`                              // 性别 1男，2女 --列表网需求
	ContactName string            `json:"contact_name,omitempty,editable"`                        // 公司联系人, 必填字段
	Phone       dbtypes.JSONArray `gorm:"type:json;column:phone" json:"phone,omitempty,editable"` // 联系电话, 必填字段 [手机,公司座机，座机]
	FrontImage  string            `json:"frontimage,editable"`                                    //  身份证正面照 --列表网需求
	HandImage   string            `json:"handimage,editable"`                                     //  手持身份证照  --列表网需求
	BackImage   string            `json:"back_image,editable"`                                    // 身份证背面，---酷易搜 个人认证需要
}

func (Company) TableName() string {
	return "company"
}

func (c *Company) BeforeSave(tx *gorm.DB) error {
	if c.ID == 0 {
		c.CreatedAt = time.Now()
	}
	c.UpdatedAt = time.Now()
	if c.IDCardNo != "" {
		if !regutils.IsIdCardNumber(c.IDCardNo) {
			return errors.New("请输入18位数字或数字和英文字母组合的身份证号码")
		}
	}
	if c.Business != "" {
		if utf8.RuneCountInString(c.Business) > 200 {
			return errors.New("经营范围不能超过200个字")
		}
	}
	return nil
}

func (c *Company) GetAreaID() int {
	if len(c.AreaIds) > 0 {
		for i := len(c.AreaIds) - 1; i >= 0; i-- {
			if c.AreaIds[i] != "0" {
				id, _ := strconv.Atoi(c.AreaIds[i])
				return id
			}
		}
	}
	return 0
}

func (c *Company) GetCatID() int {
	if len(c.Cate) > 0 {
		for i := len(c.Cate) - 1; i >= 0; i-- {
			if c.Cate[i] != "0" {
				id, _ := strconv.Atoi(c.Cate[i])
				return id
			}
		}
	}
	return 0
}

/**
公司类型：
企业经济性质
1	外资企业
2	中外合资经营企业
3	私营企业
4	国有企业
5	政府机构
6	非盈利组织
7	个体经营
8	内资企业
9	集体企业
10	股份合作企业
31	外商投资企业
32	中外合作经营企业
33	外商投资股份有限公司
34	其他外商投资企业
35	法人分支机构
36	农民专业合作经济组织
11	联营企业
12	国有联营企业
13	集体联营企业
14	国有与集体联营企业
15	其他联营企业
16	有限责任公司
17	国有独资公司
18	其他有限责任公司
19	股份有限公司
20	私营独资企业
21	私营合伙企业
22	私营有限责任公司
23	私营股份有限公司
24	其他企业
25	港、澳、台商投资企业
26	合资经营企业（港或澳、台资）
27	合作经营企业（港或澳、台资）
28	港、澳、台商独资经营企业
29	港、澳、台商投资股份有限公司
30	其他港、澳、台商投资企业
*/
