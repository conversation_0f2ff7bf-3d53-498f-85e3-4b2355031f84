package models

type Cat struct {
	ID                     int    `gorm:"primaryKey;column:id;type:int unsigned;not null"`
	Pr                     int8   `gorm:"column:pr;type:tinyint;not null;default:100"`
	CatName                string `gorm:"column:cat_name;type:varchar(64);not null"`
	Shorttitle             string `gorm:"column:shorttitle;type:varchar(100);not null;default:''"`
	Searchword             string `gorm:"column:searchword;type:varchar(128)"`
	Type                   uint16 `gorm:"column:type;type:smallint unsigned;not null;default:0"`
	ParentID               int    `gorm:"index:parent_id;index:caten_level_p;index:parent_id_2;column:parent_id;type:int unsigned;not null;default:0"`
	OrderID                uint   `gorm:"index:order_id;column:order_id;type:int unsigned;not null;default:0"`
	Description            string `gorm:"column:description;type:varchar(254);not null;default:''"`
	Level                  int    `gorm:"index:level;index:caten_level_p;column:level;type:tinyint unsigned;not null;default:0"`
	CatEn                  string `gorm:"index:caten_level_p;column:cat_en;type:varchar(255);not null"`
	CatTrace               string `gorm:"index:cat_trace;column:cat_trace;type:varchar(250);not null"`
	CatTraceEn             string `gorm:"index:cat_trace_en;column:cat_trace_en;type:varchar(250);not null"`
	IsHot                  bool   `gorm:"index:is_hot;column:is_hot;type:tinyint(1);not null;default:0"`
	HotTitle               string `gorm:"column:hot_title;type:varchar(50);not null;default:''"`
	Friend                 string `gorm:"column:friend;type:varchar(100);not null;default:''"`
	FriendName             string `gorm:"column:friend_name;type:varchar(100);not null;default:''"`
	Subdomain              uint8  `gorm:"column:subdomain;type:tinyint unsigned;not null;default:0"`
	Active                 uint   `gorm:"index:parent_id;index:level;index:parent_id_2;column:active;type:int unsigned;not null;default:1"`
	Messages               uint64 `gorm:"column:messages;type:bigint unsigned;not null;default:0"`
	Buymessages            int64  `gorm:"column:buymessages;type:bigint;not null;default:0"`
	Baike                  int    `gorm:"column:baike;type:int;not null;default:0"`
	Module                 string `gorm:"column:module;type:varchar(50);not null;default:default"`
	SeoTitle               string `gorm:"column:seo_title;type:varchar(254)"`
	SeoKeyword             string `gorm:"column:seo_keyword;type:varchar(254)"`
	SeoDescription         string `gorm:"column:seo_description;type:varchar(254)"`
	Templete               int    `gorm:"column:templete;type:int;not null;default:0"`
	ItemTemplate           string `gorm:"column:item_template;type:varchar(10);not null;default:0"`
	MitemTemplate          string `gorm:"column:mitem_template;type:varchar(10);not null;default:0"`
	Validate               bool   `gorm:"column:validate;type:tinyint(1);not null;default:0"`
	AfsChannel             string `gorm:"column:afs_channel;type:varchar(250)"`
	AfsKey                 string `gorm:"column:afs_key;type:varchar(255)"`
	Bkurl                  string `gorm:"column:bkurl;type:varchar(100);not null"`
	Bk                     string `gorm:"column:bk;type:varchar(255);not null"`
	Dname                  string `gorm:"column:dname;type:varchar(66);not null;default:''"`
	Dtitle                 string `gorm:"column:dtitle;type:varchar(254);not null;default:''"`
	Dkeyword               string `gorm:"column:dkeyword;type:varchar(254);not null;default:''"`
	Ddescription           string `gorm:"column:ddescription;type:varchar(254);not null;default:''"`
	PageTitle              string `gorm:"column:page_title;type:varchar(254);not null;default:''"`
	PageTitleQg            string `gorm:"column:page_title_qg;type:varchar(254);not null;default:''"`
	PageKeywordQg          string `gorm:"column:page_keyword_qg;type:varchar(254);not null;default:''"`
	PageDescriptionQg      string `gorm:"column:page_description_qg;type:varchar(254);not null;default:''"`
	DescriptionQg          string `gorm:"column:description_qg;type:varchar(254);not null;default:''"`
	PageTitleSpecial       string `gorm:"column:page_title_special;type:varchar(254);not null;default:''"`
	PageKeywordSpecial     string `gorm:"column:page_keyword_special;type:varchar(254);not null;default:''"`
	PageDescriptionSpecial string `gorm:"column:page_description_special;type:varchar(254);not null;default:''"`
	DescriptionSpecial     string `gorm:"column:description_special;type:varchar(254);not null;default:''"`
	PageTitleB2blist       string `gorm:"column:page_title_b2blist;type:varchar(254);not null;default:''"`
	PageKeywordB2blist     string `gorm:"column:page_keyword_b2blist;type:varchar(254);not null;default:''"`
	PageDescriptionB2blist string `gorm:"column:page_description_b2blist;type:varchar(254);not null;default:''"`
	Video                  string `gorm:"column:video;type:text;not null"`
	Replacekeyword         string `gorm:"column:replacekeyword;type:varchar(128)"`
	Replaceproduct         string `gorm:"column:replaceproduct;type:varchar(128)"`
	Jpmark                 int    `gorm:"index:parent_id_2;column:jpmark;type:int;not null;default:0"`
}
