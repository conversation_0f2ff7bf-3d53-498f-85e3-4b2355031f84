package models

import (
	"fmt"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/pkg/dbtypes"
)

type InfoExt struct {
	dbtypes.Base
	MID         uint64            `gorm:"primary_key" json:"m_id"`
	Pic         dbtypes.JSONArray `gorm:"type:json;column:pic" json:"pic,omitempty"`
	TitlePic    dbtypes.JSONArray `gorm:"type:json;column:titlepic" json:"titlepic,editable"`
	Description string            `gorm:"type:text;" json:"description"`
}

func (InfoExt) TableName() string {
	return "info_ext"
}

func (InfoExt) TableNameById(id uint64) string {
	if id <= configs.ApiConfig.Web.MaxId {
		return "info_ext"
	}
	return fmt.Sprintf("info_ext_%02d", id%10)
}
