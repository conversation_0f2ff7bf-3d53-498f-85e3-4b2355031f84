package publisher

import (
	"gitlab.com/all_publish/api/internal/models"
	"strconv"
)

/**
关键词 word
产品名称 name
副标题 title
产品别名 alias
品牌 brand
*/
var allowed = "wntab"

type SymbolIndex struct {
	symbol string
	index  int
}

func NewSymbolIndex(symbol string, index int) SymbolIndex {
	return SymbolIndex{
		symbol: symbol,
		index:  index,
	}
}

func (s *SymbolIndex) Value() string {
	return s.symbol + strconv.Itoa(s.index)
}

func (s SymbolIndex) IsValid() bool {
	if len(s.symbol) != 1 {
		return false
	}
	if s.index < 0 {
		return false
	}
	for _, c := range allowed {
		if s.symbol == string(c) {
			return true
		}
	}
	return false
}

func (s SymbolIndex) Equal(other SymbolIndex) bool {
	return s.symbol == other.symbol && s.index == other.index
}

func (s SymbolIndex) ValueByProduct(product *models.Product) string {
	switch s.symbol {
	case "n":
		return product.Name
	case "w":
		if s.index < len(product.Word) {
			return product.Word[s.index]
		}
	case "t":
		if s.index < len(product.Title) {
			return product.Title[s.index]
		}
	case "a":
		if s.index < len(product.Alias) {
			return product.Alias[s.index]
		}
	case "b":
		return product.Brand
	}
	return ""
}
