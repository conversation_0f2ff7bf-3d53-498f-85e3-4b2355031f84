package publisher

import (
	"fmt"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"time"
)

/**
文章一次性生成好，设置开始标志位。
结束删除，如果一开始有开始标志位，说明上次异常结束。
*/
type Publisher interface {
	GenInfoBy(companyIds []uint64, todayStart *time.Time) (int, error)
}

type MyPublisher struct {
	db     *gorm.DB
	logger *zap.SugaredLogger
}

func (m *MyPublisher) ResetTxtCache(productId uint64) {
	m.logger.Info("清除产品伪原创缓存", zap.Uint64("productId", productId))
	rds := db.GetRedisConn()
	rds.Do("DEL", fmt.Sprintf("product_txt_%d", productId))
	rds.Do("DEL", fmt.Sprintf("product_txt_list_index_%d", productId))
}

func (m *MyPublisher) GetTxt(product models.Product) (string, error) {
	panic("implement me")
}

/**
只为推广中的产品生成信息
*/
func (m *MyPublisher) GenInfoBy(companyIds []uint64, todayStart *time.Time) (cnt int, err error) {
	if todayStart == nil {
		start := TodayStart()
		todayStart = &start
	}
	defer m.logger.Sync()
	m.logger.Info("生成 Info Entry ",
		zap.Time("start_time", *todayStart),
		zap.String("companys", fmt.Sprintf("%v", companyIds)))
	var allCompanies []models.Company
	// step1 获取公司列表
	if len(companyIds) == 0 {
		m.db.Find(&allCompanies)
		for _, c := range allCompanies {
			companyIds = append(companyIds, c.ID)
		}
	} else {
		db := m.db.Where("id in (?)", companyIds)
		db.Find(&allCompanies)
	}
	m.logger.Info("fixed companys:%v", zap.String("companys", fmt.Sprintf("%v", companyIds)))

	for _, company := range allCompanies {
		helper := NewSimpleCompanyHelper(&company, false)
		m.logger.Info("开始为公司生成信息", zap.Uint64("id", company.ID),
			zap.String("name", company.Name))
		count, err := helper.GenInfo(todayStart)
		if err != nil {
			return cnt, err
		}
		cnt += count

	}
	return cnt, err
}

func New(pdb *gorm.DB) Publisher {
	if pdb == nil {
		pdb = db.Instance().Get()
	}
	return &MyPublisher{db: pdb, logger: log.AppLogger()}
}

func TodayStart() time.Time {
	var cstZone = time.FixedZone("CST", 8*3600)
	var now = dbtypes.SHNow()
	return time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, cstZone)
}

type InfoGroupByProduct struct {
	ProductID uint64 `json:"product_id"`
	Count     int    `json:"count"`
}
