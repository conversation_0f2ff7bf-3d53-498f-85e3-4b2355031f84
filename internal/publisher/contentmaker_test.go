package publisher

import (
	"fmt"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"strconv"
	"testing"
)

func TestNewSimpleContentMaker(t *testing.T) {
	t.Run("combinations", func(t *testing.T) {
		a := []int{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10}
		picCnt := 4
		lenA := len(a)
		result := Combinations(a, picCnt)
		expected := lenA * (lenA - 1) * (lenA - 2) * (lenA - 3) / 24
		if len(result) != expected {
			for _, v := range result {
				t.Log(v)
			}
			t.Fatalf("expected:%d, real:%d", expected, len(result))
		}
	})
	t.Run("numbers may produce", func(t *testing.T) {
		materials := 10
		materialsAtLeast := 4
		materialsProduced := 0
		for i := materialsAtLeast; i <= materials; i++ {
			materialsProduced += cNums(materials, i)
		}
		descs := 3
		descsAtLeast := 2
		descsProduced := 0
		for i := descsAtLeast; i <= descs; i++ {
			descsProduced += cNums(descs, i)
		}

		t.Log("may product", materialsProduced*descsProduced)
	})

	t.Run("Combinations", func(t *testing.T) {
		var numbers = []int{1, 2, 3}
		result := Combinations(numbers, 1)
		t.Log(result)
	})
}

func TestParseContentIndex(t *testing.T) {
	tests := []struct {
		Raw       string
		Exptected map[string][]int
	}{
		{"m-3-2-19-3d-2-3-4", map[string][]int{
			"m": []int{3, 2, 19, 3},
			"d": []int{2, 3, 4},
		}},
		{"h-3-2-19-3d-2-3-4", map[string][]int{
			"h": []int{3, 2, 19, 3},
			"d": []int{2, 3, 4},
		}},
		{"m-3_2-19-3d-2-3-4", map[string][]int{
			"m": []int{3},
			"d": []int{2, 3, 4},
			"2": []int{19, 3},
		}},
		{"aabb33-3_2-19-3ddeeaa-2-3-4", map[string][]int{
			"aabb33": []int{3},
			"ddeeaa": []int{2, 3, 4},
			"2":      []int{19, 3},
		}},
	}
	for _, test := range tests {
		indexs := ParseContentIndex(test.Raw)
		if fmt.Sprintf("%v", test.Exptected) != fmt.Sprintf("%v", indexs) {
			t.Fatalf("expected:%v, real:%v", test.Exptected, indexs)
		}
	}
}

func TestSimpleContentMaker_GetTxt(t *testing.T) {
	initTest(t)
	t.Run("with db product", func(t *testing.T) {
		var product models.Product
		db.Instance().Get().Find(&product, 86)
		maker := NewSimpleContentMaker(&product)
		for i := 0; i < 100; i++ {
			t.Log(maker.GetTxt())
		}
	})
}

// func Test_MockTxtFromApi(t *testing.T) {
// 	initTest(t)
// 	text := `每个方法执行都会创建一个栈帧，用于存放局部变量表，操作栈，动态链接，方法出口等。每个方法从被调用，直到被执行完。对应着一个栈帧在虚拟机中从入栈到出栈的过程。
// 	通常说的栈就是指局部变量表部分，存放编译期间可知的8种基本数据类型及对象引用和指令地址。局部变量表是在编译期间完成分配，当进入一个方法时，这个栈中的局部变量分配内存大小是确定的。
// 	————————————————
// 	版权声明：本文为CSDN博主「全菜工程师小辉」的原创文章，遵循 CC 4.0 BY-SA 版权协议，转载请附上原文出处链接及本声明。
// 	原文链接：https://blog.csdn.net/y277an/article/details/93815647`
// 	t.Run("test ok", func(t *testing.T) {
// 		if _, err := MockTxtFromApi(text, nil); err != nil {
// 			t.Fatal(err)
// 		} else {
// 			t.Log("ok")
// 		}
// 	})
// 	t.Run("bad key", func(t *testing.T) {
// 		key := configs.ApiConfig.Web.KEY_5188
// 		configs.ApiConfig.Web.KEY_5188 = "3232"
// 		defer func() {
// 			configs.ApiConfig.Web.KEY_5188 = key
// 		}()
// 		if _, err := MockTxtFromApi(text, nil); err == nil {
// 			t.Fatal("expected error, but nil")
// 		} else {
// 			t.Log("ok", err)
// 		}

// 	})

// }

func TestFindNum(t *testing.T) {
	materials := 16
	materialsProduced := 0
	for i := materialsAtLeast; i <= materials; i++ {
		materialsProduced += cNums(materials, i)
	}
	//descs := 4
	//descsProduced := 0
	//for i := descsAtLeast; i <= descs; i++ {
	//	descsProduced += cNums(descs, i)
	//}

	t.Log(materialsProduced)
}

func Test_randPick(t *testing.T) {
	result := randPick([]string{
		"1", "2", "3", "4", "5", "6", "7",
	}, 3)
	t.Log(result)
}

func TestMergeMatirialAndDEsc(t *testing.T) {
	initTest(t)
	var products []models.Product
	db.Instance().Get().Find(&products)
	for _, p := range products {
		var material []string
		if valid, _ := p.IsValid(); !valid {
			continue
		}
		material = append(material, p.Material...)
		material = append(material, p.Material...)

		p.Material = material
		var maker = NewSimpleContentMaker(&p)
		if err := db.Instance().Get().Model(p).UpdateColumns(models.Product{
			Material:    p.Material,
			HashContent: maker.Hash(),
			Base: dbtypes.Base{
				UpdatedAt: dbtypes.SHNow(),
			},
		}).Error; err == nil {
			t.Log("updated", p.Name, p.ID)
		}
	}
}

func TestNoDuplicated(t *testing.T) {

	seqM := seqSlice(14)
	c := [][]int{}
	picNum := 3
	for picNum < 10 {
		c = append(c, Combinations(seqM, picNum)...)
		picNum++
	}
	t.Log("before filter:", len(c))

	var results [][]int
	str := "{"
	for _, item := range c {
		valid := true
		for _, exist := range results {
			var holes [14]int
			for _, v := range exist {
				holes[v] += 1
			}
			for _, v := range item {
				holes[v] += 1
			}
			num1 := 0.0
			num2 := 0.0
			for _, v := range holes {
				if v == 1 {
					num1++
				} else if v == 2 {
					num2++
				}
			}
			if num2/(num2+num1) >= 0.7 {
				valid = false
				break
				//t.Log("invalid:", item, exist, holes, num1, num2)
			}
		}
		if valid {
			if len(results) > 0 {
				str += ","
			}
			results = append(results, item)
			str += "{"
			for i, v := range item {
				if i > 0 {
					str += ","
				}
				str += strconv.Itoa(v)
			}
			str += "}"
		}
	}
	str += "}"
	t.Log("after filter:", len(results))
	t.Log(str)
}

func TestNoDuplicatedWithDetect(t *testing.T) {
	initTest(t)
	var product models.Product
	db.Instance().Get().Find(&product, 3625)
	seqM := seqSlice(len(product.Material))
	c := [][]int{}
	picNum := 3
	for picNum < 8 {
		c = append(c, Combinations(seqM, picNum)...)
		picNum++
	}
	t.Log("before filter:", len(c), "materials:", len(product.Material))
	var results [][]int
	for _, item := range c {
		canUse := true
		text := ""
		for _, idx := range item {
			text += product.Material[idx]
		}
		for _, info := range results {
			tartget := ""
			for _, idx := range info {
				tartget += product.Material[idx]
			}

			similar := NewCosineDetecor(text, tartget).Detect()
			if similar > 0.9 {
				canUse = false
				break
			}
		}
		if canUse {
			results = append(results, item)
		}

	}
	t.Log("after filter:", len(results))
}

func TestNoDuplicatedWithDoubleDetect(t *testing.T) {
	size := 13
	seqM := seqSlice(size)
	c := [][]int{}
	picNum := 3
	for picNum < size {
		c = append(c, Combinations(seqM, picNum)...)
		picNum++
	}
	t.Log("before filter:", len(c))

	var results [][]int
	for _, item := range c {
		valid := true
		for _, exist := range results {
			var holes = make([]int, size, size)
			for _, v := range exist {
				holes[v] += 1
			}
			for _, v := range item {
				holes[v] += 1
			}
			var num1 float32
			var num2 float32
			for _, v := range holes {
				if v == 1 {
					num1++
				} else if v == 2 {
					num2++
				}
			}
			if num2/(num1+num2) > SimilarCompose {
				valid = false
				break
				//t.Log("invalid:", item, exist, holes, num1, num2)
			}
		}
		if valid {
			results = append(results, item)
			//t.Log(item)
		}
	}
	t.Log("after filter:", len(results))
	//initTest(t)
	//var product models.Product
	//db.Instance().Get().Find(&product, 3625)
	//var results2 [][]int
	//for _, item := range results {
	//	canUse := true
	//	text := ""
	//	for _, idx := range item {
	//		text += product.Material[idx]
	//	}
	//	for _, info := range results2 {
	//		tartget := ""
	//		for _, idx := range info {
	//			tartget += product.Material[idx]
	//		}
	//
	//		similar := NewCosineDetecor(text, tartget).Detect()
	//		if similar > 0.9 {
	//			//s.logger.Info("生成索引", zap.Float64("过于相似", similar), zap.Uint64("info.id", info.ID), zap.String("text", text), zap.String("info.desc", info.Description))
	//			canUse = false
	//			break
	//		}
	//	}
	//	if canUse {
	//		results2 = append(results2, item)
	//	}
	//
	//}
	//t.Log("after filter by 黄页88算法:", len(results2))
}
