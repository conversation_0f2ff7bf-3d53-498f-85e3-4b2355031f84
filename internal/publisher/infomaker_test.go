package publisher

import (
	"testing"

	"github.com/gomodule/redigo/redis"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/utils"
)

func TestNewSimpleTitleMaker(t *testing.T) {
	config, err := configs.NewConfig("../../config.ini")
	if err != nil {
		t.<PERSON>(err)
	}
	db.InitRedis(*config)
	rds := db.GetRedisConn()
	t.Run("redis with no key", func(t *testing.T) {
		rds.Do("DEL", "test_no_key")
		if v, err := redis.String(rds.Do("GET", "LPOP")); err != nil {
			t.Log(v, err)
			v, err := redis.Bool(rds.Do("EXISTS", "test_no_key"))
			t.Log(v, err)
		}
	})
	t.Run("redis with key, no value", func(t *testing.T) {
		rds.Do("DEL", "test_no_key")
		rds.Do("RPUSH", "test_no_key", "1")
		rds.Do("LPOP", "test_no_key")
		if v, err := redis.String(rds.Do("LPOP", "test_no_key")); err != nil {
			if err == redis.ErrNil {
				t.Log("nil")
				v, err := redis.Bool(rds.Do("EXISTS", "test_no_key"))
				t.Log(v, err)
			} else {
				t.Log(v, err)
			}
		}
	})

	t.Run("redis set with muliple value", func(t *testing.T) {
		key := "redissetwithmuliplevalue"
		data := []string{"a想批量向redis list中批量push", "b想批量向redis list中批量push",
			"c想批量向redis list中批量push",
			"d想批量向redis list中批量push",
			"e想批量向redis list中批量push",
			"hello想批量向redis list中批量push",
			"world想批量向redis list中批量push",
			"bjmayor想批量向redis list中批量push",
			"maynard想批量向redis list中批量push",
			"刘顺想批量向redis list中批量push",
			"1想批量向redis list中批量push"}
		rds.Do("RPUSH", redis.Args{}.Add(key).AddFlat(data)...)
		for _, expected := range data {
			if v, err := redis.String(rds.Do("LPOP", key)); err != nil {
				t.Fatal(err)
			} else if expected == v {
				t.Log("ok")
			} else {
				t.Fatal("not equal")
			}
		}

	})

}

func TestCombineArray(t *testing.T) {
	titles := symChoices("t", 20)
	words := symChoices("w", 300)
	alias := symChoices("a", 2)
	names := []string{"n0"}
	brands := []string{"b0"}
	candinates := []string{}
	candinates = append(candinates, CombineArray(words, titles)...)
	candinates = append(candinates, CombineArray(words, alias)...)
	candinates = append(candinates, CombineArray(alias, titles)...)
	candinates = append(candinates, CombineArray(words, titles, alias)...)
	candinates = append(candinates, CombineArray(words, alias, titles)...)

	candinates = append(candinates, CombineArray(brands, names, titles)...)
	candinates = append(candinates, CombineArray(brands, names, alias)...)
	candinates = append(candinates, CombineArray(brands, names, titles, alias)...)
	candinates = append(candinates, CombineArray(brands, names, alias, titles)...)
	exptected := 300*20 + 300*2 + 2*20 + 300*20*2 + 300*2*20 + 20 + 2 + 20*2 + 20*2
	if len(candinates) != exptected {
		t.Fatalf("combine error, expected:%d, real:%d", exptected, len(candinates))
	} else {
		t.Logf("ok, %d", exptected)
	}

	shuffled := utils.Shuffle(candinates)
	if len(shuffled) <= exptected {
		t.Logf("ok, after shuffle:%d", len(shuffled))
		//for _, v := range shuffled {
		//	t.Log(v)
		//}
	}
}
