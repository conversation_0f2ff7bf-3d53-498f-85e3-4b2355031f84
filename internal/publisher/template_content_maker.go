package publisher

import (
	"encoding/base32"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"regexp"
	"runtime"
	"sort"
	"strings"

	"git.paihang8.com/lib/goutils"
	"github.com/gomodule/redigo/redis"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"gitlab.com/all_publish/api/pkg/utils"
	"go.uber.org/zap"
)

// TemplateContentMaker 模板内容生成器
// 遍历模板变量，生成所有可能的组合
type TemplateContentMaker struct {
	product        *models.Product
	vars           map[string][]string
	rds            redis.Conn
	logger         *zap.SugaredLogger
	indexKey       string
	contentIndexes []string
	position       int
}

func (s *TemplateContentMaker) CloseDB() {
	s.rds.Close()
}

// 是否二次编辑, 二次编辑需要和已有的数据做对比
func (s *TemplateContentMaker) isEditAgain() bool {
	if s.product.HashContent == "" {
		return false
	} else {
		return s.Hash() != s.product.HashContent
	}
}

func (s *TemplateContentMaker) ResetTxtCache() {
	defer s.logger.Sync()
	s.logger.Info("清除产品伪原创缓存", zap.Uint64("id", s.product.ID))
	s.rds.Do("DEL", s.indexKey)

}

func (s *TemplateContentMaker) Hash() string {
	str := s.product.TempContent
	keys := []string{}
	for k, _ := range s.product.Vars {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		str += k + fmt.Sprintf("%v", s.product.Vars[k])
	}
	return goutils.Md5str(str)
}

func (s *TemplateContentMaker) CanProduce() int {
	result := 1
	for _, v := range s.vars {
		varLen := len(v)

		if varLen > 0 {
			result *= varLen
		}
	}
	return result
}

func (s *TemplateContentMaker) GenerateContentQueue() error {
	if valid, err := s.product.IsValid(); !valid {
		return err
	}
	s.logger.Info("生成索引 ", zap.Int("vars length", len(s.vars)))
	var err error
	indexProduced := 0
	for {
		data := s.nextIndex(10)
		if data == nil {
			break
		}
		indexProduced += len(data)
		if _, err = s.rds.Do("RPUSH", redis.Args{}.Add(s.indexKey).AddFlat(data)...); err != nil {
			break
		}
	}
	if indexProduced == 0 {
		db.Instance().Get().Model(s.product).UpdateColumns(models.Product{
			HashContent: s.product.HashContent,
			InnerStatus: dbtypes.ProductStatusContentOut,
			Base: dbtypes.Base{
				UpdatedAt: dbtypes.SHNow(),
			},
		})
		return configs.ErrContentFinished
	}
	if err == nil {
		s.product.HashContent = s.Hash()
		if err := db.Instance().Get().Model(s.product).UpdateColumns(models.Product{
			HashContent: s.product.HashContent,
			Base: dbtypes.Base{
				UpdatedAt: dbtypes.SHNow(),
			},
		}).Error; err != nil {
			return err
		} else {
			return nil
		}
	} else {
		return err
	}
}

func (s *TemplateContentMaker) nextIndex(number int) []string {
	s.makeContents()
	if (s.position + number) < len(s.contentIndexes) {
		start := s.position
		s.position += number
		return s.contentIndexes[start : start+number]
	} else if s.position < len(s.contentIndexes) {
		start := s.position
		s.position = len(s.contentIndexes)
		return s.contentIndexes[start:]
	} else {
		return nil
	}
}
func wrapKey(k string) string {
	return fmt.Sprintf("【%s】", k)
}
func unWrapKey(k string) string {
	k = strings.TrimPrefix(k, "【")
	k = strings.TrimSuffix(k, "】")
	return k
}

func (s *TemplateContentMaker) makeContents() {
	if s.contentIndexes != nil {
		return
	}
	var result []string
	for k, v := range s.vars {
		k = wrapKey(k)

		k = base32.HexEncoding.EncodeToString([]byte(k))
		choices := [][]int{}
		// 限制下，要不然可以把服务器内存都给用完了
		if len(v) > 5 {
			v = v[:5]
		}
		for i := 0; i < len(v); i++ {
			choices = append(choices, []int{i})
		}
		if len(result) == 0 {
			result = symContentChoices(k, &choices)
		} else {
			resultAppend := []string{}
			for _, v := range result {
				newcontent := symContentChoices(v+k, &choices)
				resultAppend = append(resultAppend, newcontent...)
			}
			result = resultAppend
		}

	}
	result = utils.Shuffle(result)
	s.logger.Info("生成索引[自定义模板] 结束", zap.Int("result length", sizeOfStringSliceContent(result)), zap.Uint64("product.id", s.product.ID))
	// 设置上限1000
	if len(result) > 1000 {
		result = result[:1000]
	}
	s.logger.Info("生成索引[自定义模板] 压缩", zap.Int("result length", len(result)), zap.Uint64("product.id", s.product.ID))
	totalCount := len(result)
	s.logger.Info("生成索引", zap.Int("索引个数", totalCount), zap.Int("有效个数", len(result)), zap.Uint64("product.id", s.product.ID))
	s.contentIndexes = result
	runtime.GC()
}

/*
GetTxt 生成文本内容
*/
func (s *TemplateContentMaker) GetTxt() (string, error) {
	// 1. 首次生成。 2. 超级管理员审核通过 都会导致不相同, 标题生成也是类似的做法
	if s.product.HashContent != s.Hash() {
		s.ResetTxtCache()

	}
	if idx, err := redis.String(s.rds.Do("LPOP", s.indexKey)); errors.Is(err, redis.ErrNil) {
		if s.product.HashContent != s.Hash() {
			if err := s.GenerateContentQueue(); err != nil {
				return "", err
			}
			return s.GetTxt()
		} else {
			if s.product.InnerStatus == dbtypes.ProductStatusContentOut {
				return "", configs.ErrContentFinished
			}
			if err := s.GenerateContentQueue(); err != nil {
				return "", err
			}
			return s.GetTxt()
		}

	} else if err != nil {
		return "", err
	} else {
		if txt, err := s.ParseIndex(idx); err != nil {
			s.logger.Info("老的格式不支持，重新生成 ", zap.String("idx", idx))
			//老的格式不支持，重新生成
			s.ResetTxtCache()
			if err := s.GenerateContentQueue(); err != nil {
				return "", err
			}
			return s.GetTxt()
		} else {
			// 如果包含了图片变量，需要随机替换图片
			if strings.Contains(txt, wrapKey(models.VarsKeyImages)) {
				//
				imgs := s.product.Vars[models.VarsKeyImages].([]interface{})
				if len(imgs) > 0 {
					// 随机选图
					// 正则表达式匹配【图片】
					pattern := wrapKey(models.VarsKeyImages)
					re := regexp.MustCompile(pattern)

					// 自定义替换函数
					// txt = re.ReplaceAllStringFunc(txt, func(match string) string {
					// 	// 在这里实现每个图片标记的替换逻辑，例如替换为不同的内容
					// 	return `<br/><img src="` + img["url"].(string) + `@4e_1c_750w_750h_90Q" alt="` + img["name"].(string) + `" />`
					// })
					// 只替换一次
					// 查找第一个匹配项并替换
					idx := rand.Intn(len(imgs))
					for {
						if !strings.Contains(txt, pattern) {
							break
						}
						img := imgs[idx].(map[string]interface{})
						idx = (idx + 1) % len(imgs)
						loc := re.FindStringIndex(txt)
						if loc != nil {
							replacement := `<br/><img src="` + img["url"].(string) + `@4e_1c_750w_750h_90Q" alt="` + img["name"].(string) + `" />`
							txt = txt[:loc[0]] + replacement + txt[loc[1]:]
						}
					}
				}
			}
			return txt, nil
		}
	}
}

type Img struct {
	Url  string `json:"url"`
	Name string `json:"name"`
}

type imgString string

func (s imgString) toImg() Img {
	// string 转 结构体
	// "{\"id\":706,\"albumId\":174,\"name\":\"微信图片_20230323185931.jpg\",\"createtime\":\"2023-10-31 10:28:40\",\"status\":0,\"sizes\":139204,\"url\":\"https://img0.paihang8.com/436/200/weixintupian20230323185931.jpg\"}",
	// parse string to struct
	str := string(s)
	var img Img
	err := json.Unmarshal([]byte(str), &img)
	if err != nil {
		return Img{}
	}
	return img

}

func (s *TemplateContentMaker) ParseIndex(index string) (string, error) {
	indexes := ParseContentIndex(index)
	if len(indexes) == 0 {
		return "", errors.New("invalid")
	}
	data := s.product.TempContent
	for k, idx := range indexes {
		decoded, err := base32.HexEncoding.DecodeString(k)
		if err != nil {
			return "", err
		}
		k = string(decoded)
		for _, v := range idx {
			vars := s.vars[unWrapKey(k)]
			if v < len(vars) {
				data = strings.Replace(data, k, vars[v], 1)
				// 如果有重复的就随机替换，避免重复
				used := map[string]bool{}
				used[vars[v]] = true
				maxtry := 3
				try := 0
				for strings.Contains(data, k) && try < maxtry {
					try++
					v = rand.Intn(len(vars))
					if used[vars[v]] {
						continue
					}
					data = strings.Replace(data, k, vars[v], 1)
				}
			}
		}
	}
	if len(data) == 0 {
		return "", fmt.Errorf("parse index err:%s", index)
	}

	return data, nil
}

func NewTemplateContentMaker(p *models.Product) ContentMaker {
	// 处理p.Vars, 忽略空行。v是[]interface{}类型, 实际类型是[]string
	vars := make(map[string][]string)
	for k, v := range p.Vars {
		if len(v.([]interface{})) == 0 {
			delete(p.Vars, k)
		}
		if k == models.VarsKeyImages {
			continue
		}
		// 没用到的变量组要忽略
		if !strings.Contains(p.TempContent, wrapKey(k)) {
			continue
		}

		tmp := []string{}
		for _, vv := range v.([]interface{}) {
			if vv == nil || vv.(string) == "" {
				continue
			}
			tmp = append(tmp, vv.(string))
		}
		vars[k] = tmp
	}

	return &TemplateContentMaker{
		product:  p,
		vars:     vars,
		indexKey: fmt.Sprintf("product_txt_list_index2_%d", p.ID),
		rds:      db.GetRedisConn(),
		logger:   log.AppLogger(),
	}
}
