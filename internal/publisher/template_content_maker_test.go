package publisher

import (
	"reflect"
	"testing"

	"gitlab.com/all_publish/api/internal/models"
)

func TestNewTemplateContentMaker(t *testing.T) {
	initTest(t)
	p := models.Product{ID: 2,
		Mode:        models.ProductModeCustom,
		Cate:        []string{"1", "2", "3"},
		TempContent: "测试项目：【语言组】，成绩:【成绩组】, 【成绩组】,【图片组】,【图片组】",
		Vars:        map[string]interface{}{"语言组": []interface{}{"chinese", "english"}, "成绩组": []interface{}{"90", "80", "70", "60", "50", "40", "30", "20", "10", "0"}, "图片组": []interface{}{map[string]interface{}{"url": "https://img0.paihang8.com/436/200/weixintupian20230323185931.jpg", "name": "微信图片_20230323185931.jpg"}, map[string]interface{}{"url": "https://img0.paihang8.com/436/200/weixintupian20230323185932.jpg", "name": "微信图片_20230323185932.jpg"}}},
	}
	maker := NewTemplateContentMaker(&p)
	for true {
		if txt, err := maker.GetTxt(); err == nil {
			t.Log(txt)
		} else {
			t.Log(err)
			break
		}
	}
}

func Test_imgString_toImg(t *testing.T) {
	tests := []struct {
		name string
		s    imgString
		want Img
	}{
		{
			name: "test1",
			s:    imgString("{\"url\":\"https://img0.paihang8.com/436/200/weixintupian20230323185931.jpg\",\"name\":\"微信图片_20230323185931.jpg\"}"),
			want: Img{Url: "https://img0.paihang8.com/436/200/weixintupian20230323185931.jpg", Name: "微信图片_20230323185931.jpg"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.s.toImg(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("imgString.toImg() = %v, want %v", got, tt.want)
			}
		})
	}
}
