package publisher

import (
	"errors"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/business/merchanter"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services/product"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

/*
*
驱动者为公司。
公司提供产品
根据产品原信息生成N多信息，信息和商户(b2b网站)绑定
把信息发布到对应的商户(b2b网站)
每个商户又限制了每日的发布条数。
*/
type ProductPublisher interface {
	PubProduct() (int, error)
	PubProductBy(companyProduct *models.CompanyProduct) error
	GenProduct(todayStart *time.Time) (int, error)
}

type SimpleProductPublisher struct {
	company                   *models.Company
	merchantWrapMap           map[uint64]*MerchantWrap
	pubCountOfMerchantOnToday map[uint64]int
	logger                    *zap.SugaredLogger
	merchants                 []models.Merchant
	force                     bool
}

func NewSimpleProductPublisher(company *models.Company, force bool) ProductPublisher {
	return &SimpleProductPublisher{company: company,
		merchantWrapMap:           make(map[uint64]*MerchantWrap),
		pubCountOfMerchantOnToday: make(map[uint64]int),
		force:                     force,
		logger:                    log.AppLogger(),
	}
}

func (publisher *SimpleProductPublisher) GenProduct(todayStart *time.Time) (cnt int, err error) {
	defer func() {
		publisher.logger.Sync()
		if rval := recover(); rval != nil {
			err = fmt.Errorf("inner err :%s", rval)
		}
	}()
	if todayStart == nil {
		start := TodayStart()
		todayStart = &start
	}
	status := []int{dbtypes.ProductStatusPromote}
	if products, err := product.NewProductService().GetProductsFilterByStatusOfCompany(status, publisher.company.ID); err != nil {
		return 0, err
	} else {
		var productIds []uint64
		for _, p := range products {
			productIds = append(productIds, p.ID)
		}
		if len(productIds) == 0 {
			return 0, nil
		}
		var infoGroupByMerchants []models.InfoGroupByMerchant
		// 查询每个商户今日已经发布的条数
		db.Instance().Get().Raw("select pub_res->'$.merchant_id' as merchant, count(*) as count from company_product where product_id in (?)"+
			" and created_at > ? group by merchant ", productIds, todayStart).Scan(&infoGroupByMerchants)
		publisher.logger.Info("生成 CompanyProduct merchant_today_pub_count", zap.Int("row", len(infoGroupByMerchants)))
		for _, infoGroupByMerchant := range infoGroupByMerchants {
			publisher.pubCountOfMerchantOnToday[infoGroupByMerchant.Merchant] = infoGroupByMerchant.Count
		}
		if publisher.merchants == nil {
			if err := db.Instance().Get().Where("company_id = ? and pause = ?", publisher.company.ID, false).Find(&publisher.merchants).Error; err != nil {
				return 0, err
			}
		}
		for _, m := range publisher.merchants {
			if !m.EnablePostProduct {
				continue
			}
			pubCount := m.DailyPubProducts
			if publisher.pubCountOfMerchantOnToday[m.ID] >= pubCount {
				continue
			}
			var infoGroupByProducts []InfoGroupByProduct
			db.Instance().Get().Raw("select  count(*) as count, product_id from company_product where created_at>=? and pub_res->'$.merchant_id'=? and product_id in (?) group by product_id", TodayStart(), m.ID, productIds).Scan(&infoGroupByProducts)
			productPubed := map[uint64]int{}
			productFinished := map[uint64]bool{}
			for _, v := range infoGroupByProducts {
				productPubed[v.ProductID] = v.Count
			}

			publisher.logger.Info("商户信息",
				zap.Uint64("merchant", m.ID),
				zap.Int("今天剩余生成公司产品数", pubCount-publisher.pubCountOfMerchantOnToday[m.ID]))
			avg := pubCount / len(productIds)
			if pubCount%len(productIds) != 0 {
				avg += 1
			}
			i := rand.Intn(len(products)) //随机开始

			for {
				if i >= len(products) {
					i = 0
				}
				p := &products[i]
				i++
				if len(productFinished) >= len(products) {
					break
				}
				if publisher.pubCountOfMerchantOnToday[m.ID] >= pubCount {
					break
				}
				if (!publisher.force && (p.Status == dbtypes.ProductStatusContentOut || p.Status == dbtypes.ProductStatusSubjectOut)) || productPubed[p.ID] >= avg {
					productFinished[p.ID] = true
					continue
				}
				if valid, err := p.IsValid(); !valid {
					productFinished[p.ID] = true
					publisher.logger.Info("product not valid", zap.Uint64("productId:", p.ID), zap.Error(err))
					product.NewProductService().UpdateStatusToNotPass(p, err.Error(), nil)
					continue
				}

				if productFinished[p.ID] {
					continue
				}
				err := publisher.genCompanyProductOfProduct(p, &m)
				if err != nil {
					productFinished[p.ID] = true
					publisher.logger.Info("商户信息",
						zap.Uint64("merchant", m.ID),
						zap.Uint64("p.Id", p.ID),
						zap.Error(err))
				} else {
					productPubed[p.ID] += 1
					publisher.pubCountOfMerchantOnToday[m.ID] += 1
					cnt += 1
				}
			}
		}
	}
	return cnt, err
}

func (publisher *SimpleProductPublisher) PubProduct() (cnt int, err error) {
	defer func() {
		if rval := recover(); rval != nil {
			err = fmt.Errorf("inner err :%s", rval)
		}
	}()
	if products, err := product.NewProductService().GetProductsFilterByStatusOfCompany([]int{dbtypes.ProductStatusPromote, dbtypes.ProductStatusTitleout}, publisher.company.ID); err == nil {
		for _, p := range products {
			//老数据
			if len(p.Cate) == 0 {
				product.NewProductService().UpdateStatusToNotPass(&p, "分类没有设置，分类需要选择到最后一级", map[string]interface{}{
					models.AuditFieldCate: "分类没有设置，分类需要选择到最后一级",
				})
				continue
			}
			if count, err := publisher.pubProductOfProduct(&p); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return cnt, err
			} else {
				cnt += count
			}
		}
	}
	return cnt, nil
}

func (publisher *SimpleProductPublisher) getMerchantWrap(id uint64) (*MerchantWrap, error) {
	if m, ok := publisher.merchantWrapMap[id]; ok {
		return m, nil
	} else {
		var m MerchantWrap
		if err := db.Instance().Get().Find(&m.model, id).Error; errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("指定商户的商户不存在")
		}
		if client, ok := models.GetMerchant(models.PlatForm(m.model.PlatForm)); ok {
			if _, err := client.LoginBy(m.model.Account); err != nil {
				if client.IsErrorShouldStopAutoPub(err.Error()) {
					db.Instance().Get().Model(&m.model).UpdateColumns(models.Merchant{
						Pause:       true,
						PauseReason: fmt.Sprintf("自动推送失败:%s", err.Error()),
						Base: dbtypes.Base{
							UpdatedAt: dbtypes.SHNow(),
						},
					})
				}
				return nil, err
			}
			m.client = client
			publisher.merchantWrapMap[id] = &m
			return &m, nil
		} else {
			return nil, fmt.Errorf("no client found:%d", m.model.PlatForm)
		}
	}
}

func (publisher *SimpleProductPublisher) genCompanyProductOfProduct(product *models.Product, m *models.Merchant) (err error) {
	contentMaker := NewContentMaker(product)
	defer func() {
		contentMaker.CloseDB()
		publisher.logger.Sync()
	}()
	txt, err := contentMaker.GetTxt()
	if err != nil {
		if errors.Is(err, configs.ErrContentFinished) {
			// 修改Product重置title
			log.Info("no content to use now", zap.Uint64("productId:", product.ID), zap.Error(err))
			db.Instance().Get().Model(&product).UpdateColumns(models.Product{
				Status: dbtypes.ProductStatusContentOut,
				Base: dbtypes.Base{
					UpdatedAt: dbtypes.SHNow(),
				},
			})
		}
		return fmt.Errorf("生成内容失败:%w", err)
	}
	if title, err := publisher.getTitle(product); err != nil {
		if errors.Is(err, configs.ErrSubjectFinished) {
			// 修改Product重置title
			publisher.logger.Info("no subject to use now", zap.Uint64("productId:", product.ID), zap.Error(err))
			product.Status = dbtypes.ProductStatusSubjectOut
			db.Instance().Get().Model(&product).UpdateColumns(models.Product{
				Status: product.Status,
				Base: dbtypes.Base{
					UpdatedAt: dbtypes.SHNow(),
				},
			})
		}
		return err
	} else {
		return publisher.genProduct(title, txt, product, m)
	}
	return
}

/*
*
http://newjira.huangye88.net/browse/HY-4987
*/
func (publisher *SimpleProductPublisher) getTitle(product *models.Product) (string, error) {
	goodKw := []string{}
	for _, kw := range product.Word {
		if strings.Contains(kw, product.Name) {
			goodKw = append(goodKw, kw)
		}
	}
	if len(goodKw) < 5 {
		return "", configs.ErrSubjectFinished
	}
	idx := rand.Intn(len(goodKw))
	title := goodKw[idx]
	parts := strings.Split(title, product.Name)
	return parts[0] + product.Name, nil
}

func (publisher *SimpleProductPublisher) pubProductOfProduct(product *models.Product) (int, error) {
	var products []models.CompanyProduct
	dbHelper := db.Instance().Get().Where("company_id = ?", publisher.company.ID)
	dbHelper = dbHelper.Where("product_id =  ?", product.ID)
	dbHelper = dbHelper.Where("will_pub_at < ?", dbtypes.SHNow())
	dbHelper = dbHelper.Where("will_pub_at is not Null")
	dbHelper = dbHelper.Where("status = ?", dbtypes.CompanyProductStatusWattingpublish)
	if err := dbHelper.Find(&products).Error; err != nil {
		return 0, err
	} else {
		cnt := 0
		for _, item := range products {
			item.Company = publisher.company
			item.Product = *product
			if err := publisher.PubProductBy(&item); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return cnt, err
			} else {
				cnt += 1
			}
		}
		return cnt, nil
	}
}

func (publisher *SimpleProductPublisher) genProduct(title, content string, p *models.Product, merchant *models.Merchant) error {
	var price float32
	if p.Price != nil {
		price = *p.Price
	}
	companyproduct := models.CompanyProduct{
		Subject:   title,
		Price:     price,
		Unit:      p.Unit,
		Pic:       p.RandPic(len(strings.Split(content, "<br/>")) - 1),
		ProductID: p.ID,
		Content:   content,
		Status:    dbtypes.CompanyProductStatusWattingpublish,
		PubRes: map[string]interface{}{
			"merchant_id": merchant.ID,
			"platform":    merchant.PlatForm,
		},
		CompanyID: p.CompanyID,
	}
	if merchant.EnablePostProduct && merchant.ProductAutoPubTime != nil {
		willPubAt := TodayStart()
		autoPubTime := (*merchant.ProductAutoPubTime).Time
		willPubAt = willPubAt.Add(time.Duration(autoPubTime.Hour()) * time.Hour)
		willPubAt = willPubAt.Add(time.Duration(autoPubTime.Minute()) * time.Minute)
		companyproduct.WillPubAt = &willPubAt
	}
	return db.Instance().Get().Create(&companyproduct).Error
}

func (publisher *SimpleProductPublisher) PubProductBy(companyProduct *models.CompanyProduct) error {
	if companyProduct.Company == nil {
		companyProduct.Company = publisher.company
	}
	if companyProduct.Status == dbtypes.CompanyProductStatusPublishSuccessed {
		return errors.New("重复发布")
	}
	if merchantId, ok := companyProduct.PubRes["merchant_id"]; !ok {
		return errors.New("产品没有指定商户")
	} else {
		if companyProduct.ProductID > 0 {
			var product models.Product
			if err := db.Instance().Get().Find(&product, companyProduct.ProductID).Error; err != nil {
				return errors.New("信息关联的产品已经删除")
			} else {
				companyProduct.Product = product
			}
		}
		wraper, err := publisher.getMerchantWrap(uint64(merchantId.(float64)))
		if err != nil {

			return err
		}
		if (!wraper.model.EnablePostProduct || wraper.model.Pause) && !publisher.force {
			return errors.New("商铺未开发自动发布产品或者已暂停")
		}
		if simpleProduct, err := wraper.client.(*merchanter.Hy88Merchant).PublishProduct(*companyProduct); err != nil {
			db.Instance().Get().Model(&companyProduct).UpdateColumns(models.CompanyProduct{
				Status:       dbtypes.CompanyProductStatusPublishFailed,
				FailedReason: err.Error(),
				FailedTimes:  companyProduct.FailedTimes + 1,
				Base: dbtypes.Base{
					UpdatedAt: dbtypes.SHNow(),
				},
			})
			if wraper.client.IsErrorShouldStopAutoPub(err.Error()) {
				db.Instance().Get().Model(&wraper.model).UpdateColumns(models.Merchant{
					EnablePostProduct:  false,
					ProductPauseReason: fmt.Sprintf("自动推送失败:%s", err.Error()),
					Base: dbtypes.Base{
						UpdatedAt: dbtypes.SHNow(),
					},
				})
			}

			return fmt.Errorf("company_product id %d, %w", companyProduct.ID, err)
		} else {
			companyProduct.PubRes["res_url"] = simpleProduct.Url
			companyProduct.PubRes["res_id"] = simpleProduct.ID
			if err = db.Instance().Get().Model(&companyProduct).UpdateColumns(models.CompanyProduct{
				Status: dbtypes.CompanyProductStatusPublishSuccessed,
				PubRes: companyProduct.PubRes,
				Base: dbtypes.Base{
					UpdatedAt: dbtypes.SHNow(),
				},
			}).Error; err != nil {
				return err
			} else {
				return nil
			}
		}
	}
}
