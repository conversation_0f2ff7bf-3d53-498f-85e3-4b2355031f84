package publisher

import (
	"testing"

	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/pkg/db"
	_ "gitlab.com/all_publish/api/pkg/log"
)

func initTest(t *testing.T) {
	config, err := configs.NewConfig("../../config.dev.ini")
	// config, err := configs.NewConfig("../../config.ini")
	if err != nil {
		t.Fatal(err)
	}
	db.InitRedis(*config)
	db.Init(*config, db.ConnectorTypeMysql)
	//db.Init(*config, db.ConnectorTypePq)
}
