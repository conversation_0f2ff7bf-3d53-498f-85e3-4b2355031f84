package publisher

import (
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"git.paihang8.com/lib/goutils"
	"github.com/gomodule/redigo/redis"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/utils"
)

/*
*
标题生成器。功能：
1. 从redis中解出title
2. 生成索引存到redis中

（1）关键词+副标题+产品别名
可以有以下组合：
关键词+副标题
关键词+产品别名
产品别名+副标题
关键词+副标题+产品别名
关键词+产品别名+副标题
（2）（品牌）产品名称+副标题+产品别名
可以有以下组合：
（品牌）产品名称+副标题
（品牌）产品名称+产品别名

	产品别名+副标题（与上面（1）中重复去掉）

（品牌）产品名称+副标题+产品别名
（品牌）产品名称+产品别名+副标题
(3)
关键词
（品牌）产品别名+副标题 //老数据 才有副标题
（品牌）产品别名+产品名
（品牌）产品别名+关键词

改版去掉副标题HY-4435
（1）关键词+产品别名
可以有以下组合：
关键词
关键词+产品别名
产品别名
（2）（品牌）产品名称+产品别名
可以有以下组合：
（品牌）产品名称
（品牌）产品名称+产品别名
(3)
关键词
（品牌）产品别名+产品名称
（品牌）产品别名+关键词
20个副标题 300个关键字 2个别名 产生 30742 多个组合
*/
type TitleMaker interface {
	GetTitle() (string, error)
	ResetTitleQueue()
	CanProduce() int
	Hash() string
	CloseDB()

	GetSubject() (string, error)
}

type SimpleTitleMaker struct {
	product          *models.Product
	hasInfoGenerated bool
	usedTitles       map[string]struct{}
	rds              redis.Conn
	key              string
	subject          string
	titles           []string
	subjecs          []string
	position         int
}

func (s *SimpleTitleMaker) CloseDB() {
	s.rds.Close()
}

// 是否二次编辑, 二次编辑需要和已有的数据做对比
func (s *SimpleTitleMaker) isEditAgain() bool {
	if s.product.HashTitle == "" {
		return false
	} else {
		s.initUsedTitles()
		return true
	}
}

func (s *SimpleTitleMaker) initUsedTitles() {
	var infos []models.Info
	if s.usedTitles == nil {
		db.Instance().Get().Where("product_id = ?", s.product.ID).Find(&infos)
		s.usedTitles = make(map[string]struct{}, len(infos))
		for _, info := range infos {
			s.usedTitles[*info.Title] = struct{}{}
		}
	}
}
func (s *SimpleTitleMaker) GetTitle() (string, error) {
	// 新产品
	if len(s.product.OptionTitle) > 0 {
		for _, title := range s.product.OptionTitle {
			if s.canUse(title) {
				s.usedTitles[title] = struct{}{}
				return title, nil
			}
		}
		return "", configs.ErrTitleFinished
	} else {
		// 1. 首次生成。 2. 审核通过 都会导致不相同, 标题生成也是类似的做法
		if s.product.HashTitle != s.Hash() {
			s.ResetTitleQueue()
		}
		if titleIndex, err := redis.String(s.rds.Do("LPOP", s.key)); err == redis.ErrNil {
			if s.product.HashTitle == s.Hash() {
				return "", configs.ErrTitleFinished
			} else {
				//第一次生成
				//s.rds.Do("RPUSH",  args)
				if err := s.GenerateTitleQueue(); err != nil {
					return "", err
				}
				return s.GetTitle()
			}
		} else if err != nil {
			return "", err
		} else {
			if titleIndex == "Finish" {
				// 标题关键词用完
				return "", configs.ErrTitleFinished
			}
			title, err := s.parseIndex(titleIndex)
			if err != nil {
				if err := s.GenerateTitleQueue(); err != nil {
					return "", err
				}
				return s.GetTitle()
			}
			if !s.valid(title) {
				return s.GetTitle()
			}
			return title, nil
		}
	}

}
func (s *SimpleTitleMaker) GetSubject() (string, error) {
	var idx = 0
	// 1. 首次生成。 2. 审核通过 都会导致不相同, 标题生成也是类似的做法
	if s.product.HashSubject != s.HashSubject() {
		idx = 0
	} else if titleIndex, err := redis.String(s.rds.Do("GET", s.subject)); err != nil {
		return "", err
	} else {
		if titleIndex == "Finish" {
			// 标题关键词用完
			return "", configs.ErrSubjectFinished
		}
		idx, err = strconv.Atoi(titleIndex)
		if err != nil {
			return "", err
		}
	}
	var nextKey = ""
	if idx < len(s.subjecs) {
		nextKey = strconv.Itoa(idx + 1)
	} else {
		nextKey = "Finish"
	}
	s.rds.Do("SET", s.subject, nextKey)
	return s.subjecs[idx], nil
}

func (s *SimpleTitleMaker) ResetTitleQueue() {
	s.rds.Do("DEL", s.key)
}

func (s *SimpleTitleMaker) Hash() string {
	str := ""
	str += strings.Join(s.product.Word, "")
	str += strings.Join(s.product.Alias, "")
	str += s.product.Name
	str += s.product.Brand

	return goutils.Md5str(str)
}

func (s *SimpleTitleMaker) HashSubject() string {
	str := ""
	str += strings.Join(s.subjecs, "")
	return goutils.Md5str(str)
}

func (s *SimpleTitleMaker) CanProduce() int {
	return len(s.getCandinates())
}

// 生成队列
func (s *SimpleTitleMaker) GenerateTitleQueue() error {
	if s.product.HashTitle == s.Hash() {
		return errors.New("title has already generate index")
	}
	var err error
	for {
		data := s.nextIndex(10)
		if data == nil {
			break
		}
		if _, err = s.rds.Do("RPUSH", redis.Args{}.Add(s.key).AddFlat(data)...); err != nil {
			break
		}
	}
	if err == nil {
		s.rds.Do("RPUSH", s.key, "Finish")
		if err := db.Instance().Get().Model(s.product).UpdateColumns(models.Product{
			HashTitle: s.Hash(),
			Base: dbtypes.Base{
				UpdatedAt: dbtypes.SHNow(),
			},
		}).Error; err != nil {
			s.ResetTitleQueue()
			return err
		}
		return nil
	} else {
		return err
	}
}

// 生成队列
func (s *SimpleTitleMaker) GenerateSubjectQueue() error {
	if s.product.HashSubject == s.HashSubject() {
		return errors.New("subject has already generate index")
	}
	var err error
	for {
		data := s.nextIndex(10)
		if data == nil {
			break
		}
		if _, err = s.rds.Do("RPUSH", redis.Args{}.Add(s.key).AddFlat(data)...); err != nil {
			break
		}
	}
	if err == nil {
		s.rds.Do("RPUSH", s.key, "Finish")
		if err := db.Instance().Get().Model(s.product).UpdateColumns(models.Product{
			HashTitle: s.Hash(),
			Base: dbtypes.Base{
				UpdatedAt: dbtypes.SHNow(),
			},
		}).Error; err != nil {
			s.ResetTitleQueue()
			return err
		}
		return nil
	} else {
		return err
	}
}

func (s *SimpleTitleMaker) parseIndex(data string) (string, error) {
	indexes := Parse(data)
	if len(indexes) == 0 {
		return "", errors.New("invalid")
	} else {
		data := make([]string, 0, len(indexes))
		var preIdx SymbolIndex
		for _, idx := range indexes {
			//HY-4435
			if (preIdx.symbol == "a" && idx.symbol == "w") || (preIdx.symbol == "w" && idx.symbol == "a") {
				data = append(data, ",")
			}
			data = append(data, idx.ValueByProduct(s.product))
			preIdx = idx
		}
		return strings.Join(data, ""), nil
	}
}

func (s *SimpleTitleMaker) nextIndex(number int) []string {
	s.makeTitles()
	if (s.position + number) < len(s.titles) {
		start := s.position
		s.position += number
		return s.titles[start : start+number]
	} else if s.position < len(s.titles) {
		start := s.position
		s.position = len(s.titles)
		return s.titles[start:]
	} else {
		return nil
	}
}

/*
关键词 word
产品名称 name
副标题 title
产品别名 alias
品牌 brand
*/
func (s *SimpleTitleMaker) makeTitles() {
	if s.titles != nil {
		return
	}

	s.titles = utils.Shuffle(s.getCandinates())
	if len(s.titles) > dbtypes.MaxInfosPerProduct {
		s.titles = s.titles[:dbtypes.MaxInfosPerProduct]
	}

	if s.isEditAgain() {
		var tmp []string
		for _, v := range s.titles {
			txt, _ := s.parseIndex(v)
			if s.canUse(txt) {
				tmp = append(tmp, v)
			}
		}
		s.titles = tmp
	}
}

func (s *SimpleTitleMaker) getCandinates() []string {
	words := symChoices("w", len(s.product.Word))
	alias := symChoices("a", len(s.product.Alias))
	names := []string{"n0"}
	brands := []string{"b0"}
	candinates := []string{}
	candinates = append(candinates, CombineArray(words)...)
	if len(s.product.Brand) > 0 {
		candinates = append(candinates, CombineArray(brands, names)...)
	}
	candinates = append(candinates, words...)

	if s.product.UseAlias {
		candinates = append(candinates, CombineArray(words, alias)...)
		candinates = append(candinates, CombineArray(alias)...)
		if len(s.product.Brand) > 0 {
			candinates = append(candinates, CombineArray(brands, names, alias)...)
			candinates = append(candinates, CombineArray(brands, alias)...)
			candinates = append(candinates, CombineArray(brands, alias, names)...)
			candinates = append(candinates, CombineArray(brands, alias, words)...)
		}

	}

	filtered := []string{}
	used := map[string]struct{}{}
	for _, s := range candinates {
		if _, ok := used[s]; !ok {
			filtered = append(filtered, s)
			used[s] = struct{}{}
		}
	}
	return filtered
}

// 每个商家的规则不一样，这里不应该处理。直接调api就是了
func (s *SimpleTitleMaker) valid(title string) bool {
	//我们规定标题为3-40个汉字,
	//if utf8.RuneCountInString(title) < 3 || utf8.RuneCountInString(title) > 40 {
	//	return false
	//}
	return true
}

func (s *SimpleTitleMaker) canUse(title string) bool {
	if !s.valid(title) {
		return false
	}
	if _, ok := s.usedTitles[title]; ok {
		return false
	}
	return true
}

func NewSimpleTitleMaker(product *models.Product) TitleMaker {

	companyProducts := []models.CompanyProduct{}
	db.Instance().Get().Where("product_id = ?", product.ID).Find(&companyProducts)
	usedSubjects := []string{}
	for _, cp := range companyProducts {
		usedSubjects = append(usedSubjects, cp.Subject)
	}
	s := &SimpleTitleMaker{
		product: product,
		rds:     db.GetRedisConn(),
		key:     fmt.Sprintf("product_title_list_index_%d", product.ID),
		subject: fmt.Sprintf("product_subject_list_index_%d", product.ID),
		subjecs: TrimWord(product.Word, usedSubjects),
	}
	s.initUsedTitles()
	return s
}

func TrimWord(words []string, filter []string) []string {
	subjectsMap := make(map[string]struct{})
	for _, item := range filter {
		subjectsMap[item] = struct{}{}
	}
	subjects := []string{}
	for _, word := range words {
		for _, item := range dbtypes.KW_Sufix {
			if strings.HasSuffix(word, item) {
				word = strings.TrimRight(word, item)
			}
		}
		if _, ok := subjectsMap[word]; !ok {
			subjects = append(subjects, word)
			subjectsMap[word] = struct{}{}
		}
	}

	sort.Sort(sort.StringSlice(subjects))
	return subjects
}
