package publisher

import (
	"encoding/gob"
	"os"
	"regexp"
	"strconv"
)

var indexPools = map[int][][]int{}

const (
	SimilarProductM    = 0.7
	SimilarInfoContent = 0.79
	SimilarCompose     = 0.51
)

func getPool(num int) [][]int {
	if _, ok := indexPools[num]; !ok {
		indexPools[num] = genSeq(num)
	}
	return indexPools[num]
}

func init() {
	load()
}

func load() {
	file, err := os.Open("indexpool.dat")
	if err != nil {
		return
	}
	cached := map[int][][]int{}
	enc := gob.NewDecoder(file)
	err2 := enc.Decode(&cached)
	if err2 == nil {
		indexPools = cached
	}
}

func store() {
	file, err := os.Create("indexpool.dat")
	if err != nil {
		return
	}
	cached := map[int][][]int{}
	for i := MIN_MATERIALS; i <= MAX_MATERIALS; i++ {
		cached[i] = genSeq(i)
		println(i, ":", len(cached[i]))
	}
	enc := gob.NewEncoder(file)
	err2 := enc.Encode(cached)
	if err2 != nil {
		println(err2)
	}
}

/*
13:80
14:111
15:149
16:268
17:402
18:483
19:588
20:686
21:820
22:962
*/
func genSeq(num int) [][]int {
	seqM := seqSlice(num)
	c := [][]int{}
	picNum := 3
	for picNum < (num+1)/2 {
		c = append(c, Combinations(seqM, picNum)...)
		picNum++
	}

	var results [][]int
	for _, item := range c {
		valid := true
		for _, exist := range results {
			var holes = make([]int, num, num)
			for _, v := range exist {
				holes[v] += 1
			}
			for _, v := range item {
				holes[v] += 1
			}
			var num1 float32
			var num2 float32
			for _, v := range holes {
				if v == 1 {
					num1++
				} else if v == 2 {
					num2++
				}
			}
			if num2/(num1+num2) >= SimilarCompose {
				valid = false
				break
				//t.Log("invalid:", item, exist, holes, num1, num2)
			}
		}
		if valid {
			results = append(results, item)
		}
	}
	return results
}

const MAX_MATERIALS = 21
const MIN_MATERIALS = 13

func cNums(base, pick int) int {
	var result = 1
	for i := 0; i < pick; i++ {
		result *= (base - i)
	}
	anum := Anums(pick)
	return result / anum
}

func Anums(base int) int {
	if base <= 1 {
		return base
	}
	return base * Anums(base-1)
}

func Parse(data string) []SymbolIndex {
	var indexs []SymbolIndex
	reg := regexp.MustCompile("([" + allowed + "][0-9]+)")
	matches := reg.FindAllString(data, -1)
	for _, match := range matches {
		if len(match) > 1 {
			if idx, err := strconv.Atoi(match[1:]); err == nil {
				symbolIndex := NewSymbolIndex(string(match[0]), idx)
				if symbolIndex.IsValid() {
					indexs = append(indexs, symbolIndex)
				}
			}
		}
	}
	return indexs
}

func symChoices(sym string, numbers int) []string {
	result := make([]string, 0, numbers)
	for i := 0; i < numbers; i++ {
		result = append(result, sym+strconv.Itoa(i))
	}
	return result
}

func CombineArray(args ...[]string) []string {
	if len(args) == 0 {
		return nil
	}
	if len(args) == 1 {
		return args[0]
	}
	for _, arg := range args {
		if len(arg) == 0 {
			return []string{}
		}
	}
	first := args[0]
	left := CombineArray(args[1:]...)
	var result = make([]string, 0, len(first)*len(left))
	for _, v := range first {
		for _, vv := range left {
			result = append(result, v+vv)
		}
	}
	return result
}

func Combinations(numbers []int, pickCnt int) [][]int {
	var result [][]int
	if pickCnt <= 0 {
		return nil
	}
	if pickCnt == 1 {
		for _, n := range numbers {
			result = append(result, []int{n})
		}
		return result
	}
	if vlen := len(numbers); vlen == pickCnt {
		result = append(result, numbers)
		return result
	} else if vlen < pickCnt {
		return nil
	} else {
		choose := Combinations(numbers[1:], pickCnt-1)
		notChoose := Combinations(numbers[1:], pickCnt)
		for _, c := range choose {
			tmp := make([]int, 0, len(c)+1)
			tmp = append(tmp, numbers[0])
			tmp = append(tmp, c...)
			result = append(result, tmp)
		}
		result = append(result, notChoose...)
		return result
	}
}

func seqSlice(num int) []int {
	var s = make([]int, 0, num)
	for i := 0; i < num; i++ {
		s = append(s, i)
	}
	return s
}

type MockResponse struct {
	Errcode         interface{} `json:"errcode"` //正常为字符串，不正常为整形
	Errmsg          string      `json:"errmsg"`
	Corewordsstatus string      `json:"corewordsstatus,omitempty"`
	Corewords       string      `json:"corewords,omitempty"`
	Data            string      `json:"data,omitempty"`
}

// func MockTxtFromApi(raw string, filter []string) (string, error) {
// 	// 最大个数为1000
// 	if len(filter) > 950 {
// 		filter = filter[:950]
// 	}
// 	//# 进行伪原创
// 	filter = append(filter, desc_split, comm_split, material_split)
// 	url := "http://apis.5118.com/wyc/akey"
// 	data := map[string]string{
// 		"txt":    raw,
// 		"th":     "3",
// 		"filter": strings.Join(filter, "|"),
// 	}

// 	headers := map[string]string{
// 		"Authorization": "APIKEY " + configs.ApiConfig.Web.KEY_5188,
// 	}
// 	if body, err := brower.NewHttpBrower(false, false).WithHeader(headers).Post(url, data); err != nil {
// 		return "", err
// 	} else {
// 		var response MockResponse
// 		if err := json.Unmarshal([]byte(body), &response); err != nil {
// 			return "", fmt.Errorf("call api error:%w", err)
// 		} else {
// 			if response.Data != "" {
// 				return strings.TrimSpace(response.Data), nil
// 			}
// 			return "", fmt.Errorf("call 5188 api error:%s", response.Errmsg)
// 		}
// 	}
// }
