package publisher

import (
	"math"
	"testing"

	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
)

func TestNewCosineDetecor(t *testing.T) {
	t.Run("no similar", func(t *testing.T) {
		source := "中国建筑，具有悠久的历史传统和光辉的成就。从陕西半坡遗址发掘的方形或圆形浅穴式房屋发展到如今，已有六、七千年的历史。修建在崇山峻岭之上、蜿蜒万里的长城，是人类建筑史上的奇迹；建于隋代的河北赵县的安济桥，在科学技术同艺术的完美结合上，早已走在世界桥梁科学的前列；现存的高达67.1米的山西应县佛宫寺木塔，是世界现存最高的木结构建筑；北京明、清两代的故宫，则是世界上现存规模最大、建筑精美、保存完整的大规模建筑群。至于中国的古典园林，它的独特的艺术风格，使它成为中国民族文化遗产中的一颗明珠。这一系列现存的技术高超、艺术精湛、风格独特的建筑，在世界建筑史上自成系统，独树一帜，是中国古代灿烂文化的重要组成部分，是全人类宝贵的历史文化遗产。"
		targget := "庭院不论大小，道路的铺装是必不可少的，一般的庭院小径可用天然石材或者各色地砖、黑白相间的鹅卵石铺就。夏季是一个开花植物种类繁多的季节，因此，可以进行多样化的色彩组合，用充满野趣的多年生草花来点缀。在夏季即使用色彩明度高的多种花色组合也不会有杂乱之感。例如可以用艳丽的，不同色系的金鱼草配成多个活泼的色块，这其间可以点缀一些银叶植物或白花香雪球等加以中和。"
		detector := NewCosineDetecor(source, targget)
		similar := detector.Detect()
		t.Log(similar)
	})

	t.Run("equeal rand sequnce", func(t *testing.T) {
		source := "中国人好样的"
		targget := "好样的中国人"
		detector := NewCosineDetecor(source, targget)
		similar := detector.Detect()
		t.Log(similar)
	})

	t.Run("no equeal", func(t *testing.T) {
		source := "旧衣回收箱材质一般都是选用镀锌板为主料，材质的厚度常规是在0.9—1.2mm之间，采用二保焊机焊接而成，焊点均匀无毛刺。"
		targget := `1、为保证混凝土修补部分与原混凝土结构形成整体并共同作用，对原混凝土基层表面处理的好坏上混凝土修补料能否充分发挥性能，达到其修补效果的关键所在。
		首先确定混凝土修补区域的边缘轮廓。其修补处理范围应当比实际破损范围大一些，至少向外扩大100mm。
		2、切割或剔凿出混凝土修补区域的垂直边缘，其深度应≥10mm，以避免修补区域边缘成薄片状产生羽状边缘。
		3、剔除修补区域内的所有疏松混凝土，直至露出坚实的混凝土基层。剔凿深度应以露混凝土骨料颗粒为准。
		4、对破损混凝土中裸露的钢筋应彻底清除其表面所有的锈皮及其它杂质，直至露出金属光泽。
		5、用水将处理过的混凝土基层表面彻底冲洗干净并使其充分润湿。在进行下一道工序时应保证混凝土基层表面不得有明水存在。
		6、在处理好的混凝土基层表面均匀地涂刷YJ-302界面剂以提高修补料与原混凝土结构的粘接强度。
		7、按照规定的用水量加水搅拌修补料。采用机械搅拌有利于保证修补料的拌和质量。人工搅拌应特别注意要搅拌均匀。
		8、趁涂刷在混凝土基层表面的YJ-302界面剂未干时，采用抹刀用力将修补料涂抹在混凝土修补区域内。涂抹顺序应从区域中心开始向修补区域边缘进行。每次涂抹厚度不宜超过20mm。（如果涂刷在混凝土基层表面的YJ-302界面剂干燥会导致修补料的粘接强度下降）。
		9、多次抹面时应注意：每一层都用木抹子抹过，最好是横向抹以便为下一层提供良好的粘接表面。`
		detector := NewCosineDetecor(source, targget)
		similar := detector.Detect()
		t.Log(similar)
	})

	t.Run("rand sequnce with html tags", func(t *testing.T) {
		source := "中国人"
		targget := "中国人中国人中国人中国人中国人中国人"
		detector := NewCosineDetecor(source, targget)
		similar := detector.Detect()
		t.Log(similar)
	})

	t.Run("rand sequnce with ", func(t *testing.T) {
		source := "中国人好样的<a href=''>good</a>"
		targget := "好样的中国人好样的中国人好样的中国人好样的中国人好样的中国人好样的中国人"
		detector := NewCosineDetecor(source, targget)
		similar := detector.Detect()
		t.Log(similar)
	})

	t.Run("with product", func(t *testing.T) {
		initTest(t)
		var products []models.Product
		scope := db.Instance().Get().Where("company_id = ?", 900)
		scope.Find(&products)
		material := []string{}
		m := []int{}
		for _, p := range products {
			material = append(material, p.Material...)
		}
		for i := 0; i < len(material); i++ {
			m = append(m, i)
		}
		choices := Combinations(m, 2)
		goods := 0
		notgoods := 0
		for _, c := range choices {
			detector := NewCosineDetecor(material[c[0]], material[c[1]])
			if similar := detector.Detect(); similar > 0.9 {
				t.Log("compare not good ", similar, c)
				notgoods++
			} else {
				t.Log("compare good ", similar, c)
				goods++
			}
		}
		t.Log("good:", goods, "not goods:", notgoods)

	})
}

func TestNewCosineDetecorManul(t *testing.T) {
	// 定义两个素材
	source := "行吊式自动化立体库-普宇货架最新研发产品 自动化立体仓库,现在比较主流的智能立体仓库。利用立体仓库设备可实现仓库高层合理化、存取自动化、操作简便化;自动化立体仓库是当前技术水平较高的形式。 行吊式自动化立体库，固名思议，比过去的堆垛机式自动化立体库，更简洁，成本更低。同样的功能与效果。一键实现存取货物，大量地节省人工和时间，减少呆料积压，电脑操作，一键式实现智能仓储管理系统，减少劳动强度，降低仓储管理成本，提升仓储作业效率，有利于加快货物周转效率，已经申请专利，由佛山普宇货架设备有限公司研发并生产制造--行吊式自动化立体库。\n"
	targget := "有手动，电动和自动化系统储存三种，自动化立体仓库货架，自动化立体仓库也叫自动化立体仓储,大批量，高科技产生的物流仓储概念，利用立体仓库设备可实现仓库高层合理化，存取自动化,操作简便化；自动化立体仓库，是当前技术水平较高的仓储货架设备。"
	t.Run("default", func(t *testing.T) {
		detector := NewCosineDetecor(source, targget)
		similar := detector.Detect()
		t.Log(similar)
	})
	t.Run("word", func(t *testing.T) {
		detector := NewCosineDetecor(source, targget, WithWord, WithNoSymbol)
		similar := detector.Detect()
		t.Log(similar)
	})
	t.Run("word,size=2", func(t *testing.T) {
		detector := NewCosineDetecor(source, targget, WithWord, WithNoSymbol, WithWordSize(2))
		similar := detector.Detect()
		t.Log(similar)
	})

}

func cosineSimilarity(s1 []string, s2 []string) float64 {
	// 统计词频
	freq1 := make(map[string]int)
	freq2 := make(map[string]int)
	for _, w := range s1 {
		freq1[w]++
	}
	for _, w := range s2 {
		freq2[w]++
	}

	// 建立词库
	wordSet := make(map[string]bool)
	for _, w := range s1 {
		wordSet[w] = true
	}
	for _, w := range s2 {
		wordSet[w] = true
	}

	// 把分词后的文本转换成向量
	vec1 := make([]int, len(wordSet))
	vec2 := make([]int, len(wordSet))
	i := 0
	for w := range wordSet {
		vec1[i] = freq1[w]
		vec2[i] = freq2[w]
		i++
	}

	// 计算余弦相似度
	dotProduct := 0
	norm1 := 0
	norm2 := 0
	for i, v := range vec1 {
		dotProduct += v * vec2[i]
		norm1 += v * v
		norm2 += vec2[i] * vec2[i]
	}
	if norm1 == 0 || norm2 == 0 {
		return 0.0
	}
	return float64(dotProduct) / (math.Sqrt(float64(norm1)) * math.Sqrt(float64(norm2)))
}
