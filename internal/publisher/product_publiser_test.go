package publisher

import (
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/log"
	"reflect"
	"testing"
)

func TestNewSimpleProductPublisher(t *testing.T) {
	type args struct {
		company *models.Company
		force   bool
	}
	tests := []struct {
		name string
		args args
		want ProductPublisher
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewSimpleProductPublisher(tt.args.company, tt.args.force); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>rf("NewSimpleProductPublisher() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSimpleProductPublisher_GenProduct(t *testing.T) {
	initTest(t)
	var company models.Company
	db.Instance().Get().Find(&company, 12141)
	publisher := NewSimpleProductPublisher(&company, false)
	gotCnt, err := publisher.GenProduct(nil)
	t.Log(gotCnt, err)
}

func TestSimpleProductPublisher_PubProduct(t *testing.T) {
	initTest(t)
	var company models.Company
	db.Instance().Get().Find(&company, 4867888)
	publisher := NewSimpleProductPublisher(
		&company,
		false,
	)
	gotCnt, err := publisher.PubProduct()
	t.Log(gotCnt, err)
}

func TestGetTitle(t *testing.T) {
	initTest(t)
	logger := log.AppLogger()
	var company models.Company
	db.Instance().Get().Find(&company, 877)
	pub := SimpleProductPublisher{company: &company,
		merchantWrapMap:           make(map[uint64]*MerchantWrap),
		pubCountOfMerchantOnToday: make(map[uint64]int),
		logger:                    logger,
		force:                     false,
	}
	var product models.Product
	db.Instance().Get().Find(&product, 3058)
	pub.getTitle(&product)
}
