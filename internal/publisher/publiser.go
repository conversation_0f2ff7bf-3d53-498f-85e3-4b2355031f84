package publisher

import (
	"errors"
	"fmt"
	"math"
	"math/rand"
	"runtime"
	"strconv"
	"strings"
	"time"

	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/product"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

/*
*
驱动者为公司。
公司提供产品
根据产品原信息生成N多信息，信息和商户(b2b网站)绑定
把信息发布到对应的商户(b2b网站)
每个商户又限制了每日的发布条数。
*/
type CompanyHelper interface {
	PubInfo(tx *gorm.DB) (int, error)
	GenInfo(todayStart *time.Time) (int, error)
	PubInfoBy(tx *gorm.DB, info *models.Info) error
}

type MerchantWrap struct {
	client models.Merchanter
	model  models.Merchant
}
type SimpleCompanyHelper struct {
	company                   *models.Company
	merchantWrapMap           map[uint64]*MerchantWrap
	pubCountOfMerchantOnToday map[uint64]int
	merchants                 []models.Merchant
	force                     bool
	index                     int //第几条信息，为了打散发布时间
}

func NewSimpleCompanyHelper(company *models.Company, force bool) CompanyHelper {
	return &SimpleCompanyHelper{company: company,
		merchantWrapMap:           make(map[uint64]*MerchantWrap),
		pubCountOfMerchantOnToday: make(map[uint64]int),
		force:                     force,
	}
}

func (helper *SimpleCompanyHelper) GenInfo(todayStart *time.Time) (cnt int, err error) {
	defer func() {
		if rval := recover(); rval != nil {
			err = fmt.Errorf("inner err :%s", dbtypes.GetStackTrace(rval))
		}
	}()
	if todayStart == nil {
		start := TodayStart()
		todayStart = &start
	}
	status := []int{dbtypes.ProductStatusPromote, dbtypes.ProductStatusSubjectOut}
	//	if helper.force {
	//status := []int{dbtypes.ProductStatusPromote, dbtypes.ProductStatusTitleout, dbtypes.ProductStatusContentOut}
	//	}
	if products, err := product.NewProductService().GetProductsFilterByStatusOfCompany(status, helper.company.ID); err != nil {
		return 0, err
	} else {
		var productIds []uint64
		for _, p := range products {
			productIds = append(productIds, p.ID)
		}
		if len(productIds) == 0 {
			return 0, nil
		}
		var infoGroupByMerchants []models.InfoGroupByMerchant
		// 查询每个商户今日已经发布的条数
		if data, err := services.NewInfoService().GroupByMerchant(productIds, todayStart); err != nil {
			return 0, err
		} else {
			infoGroupByMerchants = data
			log.Info("生成 Info merchant_today_pub_count", zap.Int("row", len(infoGroupByMerchants)))
		}
		for _, infoGroupByMerchant := range infoGroupByMerchants {
			helper.pubCountOfMerchantOnToday[infoGroupByMerchant.Merchant] = infoGroupByMerchant.Count
		}
		if helper.merchants == nil {
			if merchants, err := services.Merchant.GetEnabled(helper.company.ID, false, false); err != nil {
				return 0, err
			} else {
				helper.merchants = merchants
			}
		}
		for _, m := range helper.merchants {
			if !m.AutoPub {
				continue
			}
			m.CalStatus()
			// 状态不正常，譬如注册没通过，营业执照审核没过等
			if m.Status != "" {
				continue
			}

			pubCount := m.PubCount
			if pubCount > len(products)*m.PubPerCount {
				pubCount = len(products) * m.PubPerCount
			}
			if m.Total > 0 {
				left := m.Total - m.PublishedCnt
				if left == 0 { //暂停，已发布完
					services.Merchant.Stop(&m, "已发完"+strconv.Itoa(m.Total)+"条")
				}
				if pubCount > left {
					pubCount = left
				}
			}
			if time.Now().In(dbtypes.SHLocation).After(time.Date(2025, 1, 25, 0, 0, 0, 0, dbtypes.SHLocation)) && time.Now().In(dbtypes.SHLocation).Before(time.Date(2025, 2, 5, 0, 0, 0, 0, dbtypes.SHLocation)) {
				pubCount = int(math.Max(1, float64(pubCount)/2.0)) // 春节期间减半
			}

			if helper.pubCountOfMerchantOnToday[m.ID] >= pubCount {
				continue
			}
			var infoGroupByProducts []InfoGroupByProduct
			db.Instance().Get().Raw("select  count(*) as count, product_id from info where created_at>=? and pub_res->'$.merchant_id'=? and product_id in (?) group by product_id", TodayStart(), m.ID, productIds).Scan(&infoGroupByProducts)
			productPubed := map[uint64]int{}     //统计产品今日已发布信息量
			productFinished := map[uint64]bool{} //统计产品是否已完成今日的发布配额
			for _, v := range infoGroupByProducts {
				productPubed[v.ProductID] = v.Count
			}

			log.Info("商户信息",
				zap.Uint64("merchant", m.ID),
				zap.Int("今天剩余信息数", pubCount-helper.pubCountOfMerchantOnToday[m.ID]))
			i := 0
			for {
				if i >= len(products) {
					i = 0
				}
				p := &products[i]
				i++
				if len(productFinished) >= len(products) {
					break
				}
				if helper.pubCountOfMerchantOnToday[m.ID] >= pubCount {
					break
				}
				if !helper.force && (p.Status == dbtypes.ProductStatusContentOut || p.Status == dbtypes.ProductStatusTitleout) {
					productFinished[p.ID] = true
					continue
				}
				if valid, err := p.IsValid(); !valid {
					productFinished[p.ID] = true
					log.Info("product not valid", zap.Uint64("productId:", p.ID), zap.Error(err))
					product.NewProductService().UpdateStatusToNotPass(p, err.Error(), nil)
					continue
				}

				if ok, err := services.Merchant.CanPublish(p, m.PlatForm); !ok {
					productFinished[p.ID] = true
					log.Info("product can't publish for "+models.PlatFormName(m.PlatForm), zap.Uint64("productId:", p.ID), zap.Error(err))
					continue
				}

				if productFinished[p.ID] {
					continue
				}
				if pubCount < len(products) {
					//如果每日发布量  不够单个产品发布1条，就随机跳过。
					if rand.Intn(1000) > 500 {
						continue
					}
				}
				err := helper.genInfosOfProduct(p, &m)
				if err != nil {
					productFinished[p.ID] = true
					log.Info("商户信息",
						zap.Uint64("merchant", m.ID),
						zap.Uint64("p.Id", p.ID),
						zap.Error(err))
				} else {
					productPubed[p.ID] += 1
					helper.pubCountOfMerchantOnToday[m.ID] += 1
					cnt += 1
				}
			}
		}
	}
	return cnt, err
}

func (helper *SimpleCompanyHelper) PubInfo(tx *gorm.DB) (cnt int, err error) {
	defer func() {
		if rval := recover(); rval != nil {
			err = fmt.Errorf("inner err :%s", dbtypes.GetStackTrace(rval))
		}
	}()
	if products, err := product.NewProductService().GetProductsFilterByStatusOfCompany([]int{dbtypes.ProductStatusPromote, dbtypes.ProductStatusSubjectOut, dbtypes.ProductStatusContentOut}, helper.company.ID); err == nil {
		for _, p := range products {
			//老数据
			if len(p.Cate) == 0 {
				product.NewProductService().UpdateStatusToNotPass(&p, "分类没有设置，分类需要选择到最后一级", map[string]interface{}{
					models.AuditFieldCate: "分类没有设置，分类需要选择到最后一级",
				})
				continue
			}
			if count, err := helper.pubInfoOfProduct(tx, &p); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return cnt, err
			} else {

				cnt += count
			}
		}
	}
	return cnt, nil
}

func (helper *SimpleCompanyHelper) getMerchantWrap(id uint64) (*MerchantWrap, error) {
	if m, ok := helper.merchantWrapMap[id]; ok {
		return m, nil
	} else {
		var m MerchantWrap
		if err := db.Instance().Get().Find(&m.model, id).Error; errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("指定商户的商户不存在")
		}
		if client, ok := models.GetMerchant(m.model.PlatForm); ok {
			if m.model.Account == nil || len(m.model.Account) == 0 {
				services.Merchant.Stop(&m.model, "自动推送失败: 缺少账号信息")
				return nil, errors.New("缺少账号信息")
			}
			if _, err := client.LoginBy(m.model.Account); err != nil {
				if client.IsErrorShouldStopAutoPub(err.Error()) {
					services.Merchant.Stop(&m.model, fmt.Sprintf("自动推送失败:%s", err.Error()))
					return nil, configs.ErrMerchantStopped
				}
				return nil, err
			}
			m.client = client
			helper.merchantWrapMap[id] = &m
			return &m, nil
		} else {
			return nil, fmt.Errorf("no client found:%d", m.model.PlatForm)
		}
	}
}

func (helper *SimpleCompanyHelper) genInfosOfProduct(product *models.Product, m *models.Merchant) (err error) {
	titleMaker := NewSimpleTitleMaker(product)
	contentMaker := NewContentMaker(product)
	defer func() {
		titleMaker.CloseDB()
		contentMaker.CloseDB()
		runtime.GC()
	}()
	txt, err := contentMaker.GetTxt()
	if err != nil {
		if errors.Is(err, configs.ErrContentFinished) {
			// 修改Product重置title
			log.Info("no content to use now", zap.Uint64("productId:", product.ID), zap.Error(err))
			db.Instance().Get().Model(&product).UpdateColumns(models.Product{
				Status: dbtypes.ProductStatusContentOut,
				Base: dbtypes.Base{
					UpdatedAt: dbtypes.SHNow(),
				},
			})
		}
		return fmt.Errorf("生成内容失败:%w", err)
	}
	if title, err := titleMaker.GetTitle(); err != nil {
		if errors.Is(err, configs.ErrTitleFinished) {
			// 修改Product重置title
			log.Info("no title to use now", zap.Uint64("productId:", product.ID), zap.Error(err))
			db.Instance().Get().Model(&product).UpdateColumns(models.Product{
				Status: dbtypes.ProductStatusTitleout,
				Base: dbtypes.Base{
					UpdatedAt: dbtypes.SHNow(),
				},
			})
		}
		return err
	} else {
		return helper.genInfo(title, txt, product, m)
	}
	return
}

func (helper *SimpleCompanyHelper) pubInfoOfProduct(tx *gorm.DB, product *models.Product) (int, error) {
	if infos, err := services.NewInfoService().GetItemsOfProduct(product.ID, dbtypes.InfoStatusWattingpublish); err != nil {
		return 0, err
	} else {
		cnt := 0
		stopped := make(map[models.PlatForm]bool)
		for _, info := range infos {
			if stopped[info.Platform] {
				continue
			}
			info.Company = helper.company
			info.Product = product
			if err := helper.PubInfoBy(tx, &info); err != nil && errors.Is(err, configs.ErrMerchantStopped) {
				stopped[info.Platform] = true
				continue
			} else {

				cnt += 1
			}
		}
		if cnt < len(infos) {
			for k, _ := range stopped {
				log.Info("some merchant stoped", zap.Int("row", int(k)))
			}
		}
		return cnt, nil
	}
}

// HY-4413, 更新成了HY-4840
func (helper *SimpleCompanyHelper) filterWord(p *models.Product) []string {
	filterWords := make(map[string]bool, 0)
	for _, word := range p.Word {
		hasPrefix := false
		hasCity := false
		hasSufix := false
		//prefix := ""
		//city := ""
		sufix := ""
		matchTypeNums := 0
		for _, w := range dbtypes.KW_Sufix {
			if strings.Contains(word, w) {
				hasSufix = true
				matchTypeNums++
				sufix = w
				break
			}
		}

		for _, w := range dbtypes.KW_Prefix {
			if strings.Contains(word, w) {
				hasPrefix = true
				matchTypeNums++
				//prefix = w
				break
			}
		}

		for _, w := range services.Area.AllNames() {
			if strings.Contains(word, w) {
				hasCity = true
				//city = w
				matchTypeNums++
				break
			}
		}
		//a
		if matchTypeNums <= 1 {
			filterWords[word] = true
		}
		//b
		if hasCity && hasPrefix && !hasSufix {
			filterWords[word] = true
		}
		//c,e
		if hasCity && hasSufix {
			i := strings.Index(word, sufix)
			filterWords[word[:i]] = true
		}
	}
	words := []string{}

	for k, _ := range filterWords {
		words = append(words, k)
	}
	return words
}

func (helper *SimpleCompanyHelper) genInfo(title, content string, p *models.Product, merchant *models.Merchant) error {
	var price float32
	if p.Price != nil {
		price = *p.Price
	}
	historyId := p.CurrentId
	if historyId == 0 {
		historyId = p.ID
	}

	info := models.Info{
		Title:            &title,
		Price:            price,
		Unit:             p.Unit,
		Pic:              p.RandPic(len(strings.Split(content, "<br/>")) - 1),
		TitlePic:         p.RandTitlePic(),
		Word:             p.RandWord(title, services.Area.AllNames()),
		ProductID:        &p.ID,
		Description:      content,
		ProductHistoryID: historyId,
		Status:           dbtypes.InfoStatusWattingpublish,
		PubRes: map[string]interface{}{
			"merchant_id": merchant.ID,
			"platform":    merchant.PlatForm,
		},
		Platform:  merchant.PlatForm,
		CompanyID: p.CompanyID,
	}
	if merchant.AutoPub && merchant.AutoPubTime != nil {
		willPubAt := TodayStart()
		autoPubTime := (*merchant.AutoPubTime).Time
		willPubAt = willPubAt.Add(time.Duration(autoPubTime.Hour()) * time.Hour)
		willPubAt = willPubAt.Add(time.Duration(autoPubTime.Minute()) * time.Minute)
		willPubAt = willPubAt.Add(time.Duration(helper.index) * time.Minute)
		helper.index++
		info.WillPubAt = &willPubAt
	}
	return services.NewInfoService().Create(&info)
}

func (helper *SimpleCompanyHelper) PubInfoBy(tx *gorm.DB, info *models.Info) error {
	if info.Company == nil {
		info.Company = helper.company
	}
	if info.Status == dbtypes.InfoStatusPublishSuccessed {
		return configs.ErrDupPub
	}
	if merchantId, ok := info.PubRes["merchant_id"]; !ok {
		return configs.ErrNoMerchant
	} else {
		if info.ProductID != nil && *info.ProductID > 0 && info.Product == nil {
			var product models.Product
			if err := db.Instance().Get().Find(&product, *info.ProductID).Error; err != nil {
				return errors.New("信息关联的产品已经删除")
			} else {
				info.Product = &product
			}
		}
		wraper, err := helper.getMerchantWrap(uint64(merchantId.(float64)))
		if err != nil {
			return err
		}
		if wraper.model.Pause && !helper.force {
			return fmt.Errorf("%w:%s", configs.ErrMerchantStopped, wraper.model.PauseReason)
		}
		if !wraper.model.AutoPub && !helper.force {
			return fmt.Errorf("%w", configs.ErrMerchantStopped)
		}
		if url, err := wraper.client.PublishInfo(*info); err != nil {
			reason := err.Error()
			if len(reason) > 2048 {
				reason = reason[:2048]
			}

			db.Instance().Get().Model(&info).UpdateColumns(models.Info{
				Status:       dbtypes.InfoStatusPublishFailed,
				FailedReason: reason,
				FailedTimes:  info.FailedTimes + 1,
				Base: dbtypes.Base{
					UpdatedAt: dbtypes.SHNow(),
				},
			})
			if wraper.client.IsErrorShouldStopAutoPub(err.Error()) {
				services.Merchant.Stop(&wraper.model, fmt.Sprintf("自动推送失败:%s", err.Error()))
			}

			return fmt.Errorf("info id %d, %w", info.ID, err)
		} else {
			info.PubRes["res_url"] = url
			info.Status = dbtypes.InfoStatusPublishSuccessed
			if err = db.Instance().Get().Model(&info).Select("status", "pub_res", "updatedAt").Updates(models.Info{
				Status: info.Status,
				PubRes: info.PubRes,
				Base: dbtypes.Base{
					UpdatedAt: dbtypes.SHNow(),
				},
			}).Error; err != nil {
				return err
			} else {
				services.Merchant.IncreasePubbed(&wraper.model, 1)
				return nil
			}
		}
	}
}
