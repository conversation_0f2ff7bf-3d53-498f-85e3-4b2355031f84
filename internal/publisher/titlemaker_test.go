package publisher

import (
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
	"testing"
)

func TestParse(t *testing.T) {
	tests := []struct {
		Raw       string
		Exptected string
	}{
		{"n3w15t8a3b5", "n3w15t8a3b5"},
		{"n3w15t8a3h5", "n3w15t8a3"},
		{"n-3w15t8a3b5", "w15t8a3b5"},
		{"(1,2,3,4)", ""},
	}
	for _, test := range tests {
		indexs := Parse(test.Raw)
		result := ""
		for _, idx := range indexs {
			result += idx.Value()
		}
		if test.Exptected != result {
			t.Fatalf("expected:%s, real:%s", test.Exptected, result)
		}
	}
}

func TestSimpleTitleMaker_GetTitle(t *testing.T) {
	initTest(t)
	t.Run("with db product", func(t *testing.T) {
		var product models.Product
		db.Instance().Get().Find(&product, 1324)
		maker := NewSimpleTitleMaker(&product)
		t.Log("can product titles:", maker.CanProduce())
		maker.CloseDB()
		content := NewSimpleContentMaker(&product)
		t.Log("can produce contents:", content.CanProduce())
		var infos []models.Info
		db.Instance().Get().Where("product_id = 1324").Find(&infos)
		t.Log("already produce infos:", len(infos))
		content.CloseDB()
	})
}

func TestTrimWord(t *testing.T) {
	initTest(t)
	var product models.Product
	db.Instance().Get().Find(&product, 1324)
	for i := 0; i < 5; i++ {
		subjects := TrimWord(product.Word, []string{"专业制造按键薄膜"})
		t.Log(len(product.Word), len(subjects), subjects)
	}
}
