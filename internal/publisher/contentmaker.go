package publisher

import (
	"errors"
	"fmt"
	"math/rand"
	"regexp"
	"runtime"
	"strconv"
	"strings"

	"git.paihang8.com/lib/goutils"
	"github.com/gomodule/redigo/redis"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"gitlab.com/all_publish/api/pkg/utils"
	"go.uber.org/zap"
)

const (
	materialsAtLeast = 4
	contentAllowed   = "0-9a-zA-Z="
	MAX_COMPAIRE     = 400
)

func init() {
	rand.Seed(dbtypes.SHNow().UnixNano())
}

/*
	ContentMaker

内容生成器。功能：
保证一定数量的信息数的同时，避免内容过于重复。
*/
type ContentMaker interface {
	ResetTxtCache()
	GetTxt() (string, error)
	CanProduce() int
	Hash() string

	CloseDB()
}

type SimpleContentMaker struct {
	product        *models.Product
	infos          []models.Info
	rds            redis.Conn
	logger         *zap.SugaredLogger
	txtKey         string
	indexKey       string
	contentIndexes []string
	position       int
	materials      []string //可能是伪原创过的
	descs          []string //可能是伪原创过的
}

func (s *SimpleContentMaker) CloseDB() {
	s.rds.Close()
}

// 是否二次编辑, 二次编辑需要和已有的数据做对比
func (s *SimpleContentMaker) isEditAgain() bool {
	if s.product.HashContent == "" {
		return false
	} else {
		//
		if s.infos == nil {
			db.Instance().Get().Where("product_id = ?", s.product.ID).Order("id desc").Find(&s.infos)
			if len(s.infos) > MAX_COMPAIRE {
				s.infos = s.infos[:MAX_COMPAIRE]
			}
		}
		return true
	}
}

func (s *SimpleContentMaker) ResetTxtCache() {
	defer s.logger.Sync()
	s.logger.Info("清除产品伪原创缓存", zap.Uint64("id", s.product.ID))
	s.rds.Do("DEL", s.txtKey)
	s.rds.Do("DEL", s.indexKey)
}

func (s *SimpleContentMaker) Hash() string {
	str := ""
	str += strings.Join(s.product.Material, "")
	// str += strings.Join(s.product.Description, "")

	return goutils.Md5str(str)

}

/*
CanProduce 能够生成的内容数
*/
func (s *SimpleContentMaker) CanProduce() int {
	materials := len(s.materials)
	return len(getPool(materials))
}

/*
GenerateContentQueue 生成队列
*/
func (s *SimpleContentMaker) GenerateContentQueue() error {
	if valid, err := s.product.IsValid(); !valid {
		return err
	}
	s.logger.Info("生成索引 ",
		zap.Int("material length", len(s.materials)),
	)
	var err error
	indexProduced := 0
	for {
		data := s.nextIndex(10)
		if data == nil {
			break
		}
		indexProduced += len(data)
		if _, err = s.rds.Do("RPUSH", redis.Args{}.Add(s.indexKey).AddFlat(data)...); err != nil {
			break
		}
	}
	if indexProduced == 0 {
		db.Instance().Get().Model(s.product).UpdateColumns(models.Product{
			HashContent: s.product.HashContent,
			InnerStatus: dbtypes.ProductStatusContentOut,
			Base: dbtypes.Base{
				UpdatedAt: dbtypes.SHNow(),
			},
		})
		return configs.ErrContentFinished
	}
	if err == nil {
		s.product.HashContent = s.Hash()
		if err := db.Instance().Get().Model(s.product).UpdateColumns(models.Product{
			HashContent: s.product.HashContent,
			Base: dbtypes.Base{
				UpdatedAt: dbtypes.SHNow(),
			},
		}).Error; err != nil {
			s.ResetTxtCache()
			return err
		} else {
			return nil
		}
	} else {
		return err
	}
}

func (s *SimpleContentMaker) nextIndex(number int) []string {
	s.makeContents()
	if (s.position + number) < len(s.contentIndexes) {
		start := s.position
		s.position += number
		return s.contentIndexes[start : start+number]
	} else if s.position < len(s.contentIndexes) {
		start := s.position
		s.position = len(s.contentIndexes)
		return s.contentIndexes[start:]
	} else {
		return nil
	}
}

/*
makeContents 从物料中取4篇, 从描述中取2篇,再组合
*/
func (s *SimpleContentMaker) makeContents() {
	if s.contentIndexes != nil {
		return
	}
	l := len(s.materials)
	c1 := getPool(l)
	result := symContentChoices("m", &c1)
	result = utils.Shuffle(result)

	totalCount := len(result)
	if s.isEditAgain() {
		var tmp []string
		for _, v := range result {
			txt, _ := s.ParseIndex(v)
			if s.canUse(txt) {
				tmp = append(tmp, v)
			}
		}
		result = tmp
	}
	s.logger.Info("生成索引", zap.Int("索引个数", totalCount), zap.Int("有效个数", len(result)), zap.Uint64("product.id", s.product.ID))
	s.contentIndexes = result
	runtime.GC()
}

func (s *SimpleContentMaker) canUse(text string) bool {
	canUse := true
	for i, _ := range s.infos {
		services.NewInfoService().PatchExt(&s.infos[i])
		similar := NewCosineDetecor(text, s.infos[i].Description).Detect()
		if similar > SimilarInfoContent {
			//s.logger.Info("生成索引", zap.Float64("过于相似", similar), zap.Uint64("info.id", info.ID), zap.String("text", text), zap.String("info.desc", info.Description))
			canUse = false
			break
		}
	}
	return canUse
}

/*
GetTxt 生成文本内容
*/
func (s *SimpleContentMaker) GetTxt() (string, error) {
	// 1. 首次生成。 2. 超级管理员审核通过 都会导致不相同, 标题生成也是类似的做法
	if s.product.HashContent != s.Hash() {
		s.ResetTxtCache()
	}

	if idx, err := redis.String(s.rds.Do("LPOP", s.indexKey)); err == redis.ErrNil {
		if s.product.HashContent != s.Hash() {
			if err := s.GenerateContentQueue(); err != nil {
				return "", err
			}
			return s.GetTxt()
		} else {
			if s.product.InnerStatus == dbtypes.ProductStatusContentOut {
				return "", configs.ErrContentFinished
			}
			if err := s.GenerateContentQueue(); err != nil {
				return "", err
			}
			return s.GetTxt()
		}

	} else if err != nil {
		return "", err
	} else {
		if txt, err := s.ParseIndex(idx); err != nil {
			s.logger.Info("老的格式不支持，重新生成 ", zap.String("idx", idx))
			//老的格式不支持，重新生成
			s.ResetTxtCache()
			if err := s.GenerateContentQueue(); err != nil {
				return "", err
			}
			return s.GetTxt()
		} else {
			return txt, nil
		}
	}
}

/*
ParseIndex 解析索引
*/
func (s *SimpleContentMaker) ParseIndex(index string) (string, error) {
	indexes := ParseContentIndex(index)
	if len(indexes) == 0 {
		return "", errors.New("invalid")
	}
	data := make([]string, 0, len(indexes))
	for k, idx := range indexes {
		switch k {
		case "m":
			for _, v := range idx {
				if v < len(s.materials) {
					data = append(data, s.materials[v])
				}
			}
		case "d":
			for _, v := range idx {
				if v < len(s.descs) {
					data = append(data, s.descs[v])
				}
			}
		}
	}
	if len(data) == 0 {
		return "", fmt.Errorf("parse index err:%s", index)
	}
	if s.product.Faq != "" && rand.Int31n(10) >= 9 {
		data = append(data, s.product.Faq)
	}

	return strings.Join(data, "<br/>"), nil
}

/*
NewSimpleContentMaker 返回内容生成器
*/
func NewSimpleContentMaker(p *models.Product) ContentMaker {
	materials := p.Material
	if len(p.Material) > MAX_MATERIALS {
		materials = p.Material[len(p.Material)-MAX_MATERIALS:]
	}
	return &SimpleContentMaker{
		product:   p,
		txtKey:    fmt.Sprintf("product_txt_%d", p.ID),
		indexKey:  fmt.Sprintf("product_txt_list_index_%d", p.ID),
		rds:       db.GetRedisConn(),
		materials: materials,
		logger:    log.AppLogger(),
	}
}

/*
ParseContentIndex 解析索引
m-3-d-3之列的。合并之后只剩下m-5
*/
func ParseContentIndex(data string) map[string][]int {
	var indexs = make(map[string][]int)
	reg := regexp.MustCompile("([" + contentAllowed + "]+(-[0-9]+)+)")
	matches := reg.FindAllString(data, -1)
	for _, match := range matches {
		parts := strings.Split(match, "-")
		if len(parts) > 1 {
			for i := 1; i < len(parts); i++ {
				if idx, err := strconv.Atoi(parts[i]); err == nil {
					indexs[parts[0]] = append(indexs[parts[0]], idx)
				}
			}

		}
	}
	return indexs
}

func sizeOfStringSliceContent(slice []string) int {
	var totalSize int
	for _, s := range slice {
		totalSize += int(len(s))
	}
	return totalSize
}

/*
这种格式
e-2-3-123
e, [[2],[3],[123]] 就会生成 e-2-3-123
e, [[2],[3],[123,124]] 就会生成 e-2-3-123,124
*/
func symContentChoices(sym string, choices *[][]int) []string {
	result := make([]string, 0, len(*choices))
	for _, c := range *choices {
		var builder strings.Builder
		builder.WriteString(sym)

		for _, v := range c {
			builder.WriteString("-")
			builder.WriteString(strconv.Itoa(v))
		}
		result = append(result, builder.String())
	}
	return result
}

func randPick(topick []string, pickNum int) []string {
	if len(topick) < pickNum {
		return topick
	}
	pickIndex := make([]bool, len(topick), len(topick))
	picked := 0
	for picked < pickNum {
		for i, v := range pickIndex {
			if !v {
				if rand.Intn(2) == 1 {
					pickIndex[i] = true
					picked++
					if picked >= pickNum {
						break
					}
				}
			}
		}
	}
	log.Info("randpick", zap.Any("", pickIndex))
	ret := make([]string, 0, pickNum)
	for i, v := range pickIndex {
		if v {
			ret = append(ret, topick[i])
		}
	}
	return ret
}
