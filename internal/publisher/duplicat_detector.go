package publisher

import (
	"math"
	"os"
	"regexp"
	"strings"
	"unicode/utf8"

	"git.paihang8.com/lib/goutils"
	"github.com/yanyiwu/gojieba"
)

var jieba *gojieba.Jieba

// 检测文件夹路径时候存在
func pathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

func init() {
	if exist, _ := pathExists("./assets/dict/jieba.dict.utf8"); exist {
		jieba = gojieba.NewJieba("./assets/dict/jieba.dict.utf8", "./assets/dict/hmm_model.utf8", "./assets/dict/user.dict.utf8", "./assets/dict/idf.utf8", "./assets/dict/stop_words.utf8")
	} else {
		jieba = gojieba.NewJieba()
	}
}

type DuplicateDetector interface {
	Detect() float64
}

type DetectOption struct {
	detectByWord    bool // 是否按分词统计，
	onlyChinaWord   bool
	wordSize        int // 词长
	keepEnglishWord bool
	noHtml          bool
	noSymbol        bool
}
type CosineDetect struct {
	source string
	target string
	option DetectOption
}

type WordCounter struct {
	Keys   []string
	Values map[string]int
}

func NewWordCounter() WordCounter {
	return WordCounter{
		Values: make(map[string]int, 0),
	}
}

func (c *WordCounter) IncreaseKey(key string) {
	if _, ok := c.Values[key]; !ok {
		c.Values[key] = 1
		c.Keys = append(c.Keys, key)
	} else {
		c.Values[key] += 1
	}
}

type OptFunc func(opt *DetectOption)

func WithOnlyChinaWord(opt *DetectOption) {
	opt.onlyChinaWord = true
}
func WithWordSize(size int) OptFunc {
	return func(opt *DetectOption) {
		opt.wordSize = size
	}
}

func WithKeepEnglishWord(opt *DetectOption) {
	opt.keepEnglishWord = true
}

func WithNoHtml(opt *DetectOption) {
	opt.noHtml = true
}

func WithNoSymbol(opt *DetectOption) {
	opt.noSymbol = true
}
func WithWord(opt *DetectOption) {
	opt.detectByWord = true
}

func (c *CosineDetect) Detect() float64 {
	sourceWords := c.parseToWords(c.source)
	targetWords := c.parseToWords(c.target)
	for _, k := range targetWords.Keys {
		if _, ok := sourceWords.Values[k]; !ok {
			sourceWords.Keys = append(sourceWords.Keys, k)
		}
	}
	var arg1, arg2, arg3 float64
	for _, k := range sourceWords.Keys {
		v := sourceWords.Values[k]
		tv := targetWords.Values[k]
		arg1 += float64(v * tv)
		arg2 += math.Pow(float64(v), 2)
		arg3 += math.Pow(float64(tv), 2)
	}
	return arg1 / (math.Sqrt(arg2) * math.Sqrt(arg3))

}

func removePunctuation(s string) string {
	return strings.Map(func(r rune) rune {
		if strings.ContainsRune("!\n\r\"#$%&'()*+,-./:，,。,！,；？、;<=>?@[\\]^_`{|}~", r) {
			return -1
		}
		return r
	}, s)
}

func (c *CosineDetect) parseToWords(text string) WordCounter {
	var counter = NewWordCounter()
	if c.option.noHtml {
		text = goutils.TrimHtml(text, "")
	}

	if c.option.keepEnglishWord {
		wordReg := regexp.MustCompile(`[a-z0-9.\-_@/]{4,}`)
		matches := wordReg.FindAllStringSubmatch(text, -1)
		for _, word := range matches {
			counter.IncreaseKey(word[0])
		}
	}
	if c.option.noSymbol {
		text = removePunctuation(text)
	}
	if c.option.detectByWord {
		w1 := jieba.Cut(text, true)
		for _, ch := range w1 {
			if c.option.wordSize > 0 && utf8.RuneCountInString(ch) < c.option.wordSize {
				continue
			}
			counter.IncreaseKey(ch)
		}
	} else {
		chinaReg := regexp.MustCompile("^[\u4e00-\u9fa5]$")
		for _, ch := range text {
			if chinaReg.MatchString(string(ch)) {
				counter.IncreaseKey(string(ch))
			}
		}
	}

	return counter
}

func NewCosineDetecor(source, target string, optFuncs ...OptFunc) DuplicateDetector {
	opt := DetectOption{noHtml: true, noSymbol: true, onlyChinaWord: true}
	for _, f := range optFuncs {
		f(&opt)
	}
	if len(source) < len(target) {
		return &CosineDetect{source: target, target: source, option: opt}
	}
	return &CosineDetect{source: source, target: target, option: opt}
}
