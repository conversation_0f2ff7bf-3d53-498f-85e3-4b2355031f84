package publisher

import (
	"errors"
	"testing"

	"github.com/magiconair/properties/assert"
	_ "gitlab.com/all_publish/api/internal/business/merchanter"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/product"

	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/gorm"
)

func TestLimitOfGenInfo(t *testing.T) {
	initTest(t)
	var merchants []models.Merchant
	db.Instance().Get().Find(&merchants)
	total := 0
	for _, m := range merchants {
		if !m.Pause {
			total += m.PubCount
		}
	}

	t.Log(total)
}

func TestMyPublisher_GenInfoBy(t *testing.T) {
	initTest(t)
	var company models.Company
	db.Instance().Get().Find(&company, 4902095)
	helper := NewSimpleCompanyHelper(&company, false)
	cnt, err := helper.GenInfo(nil)
	t.Logf("生成了%d, err:%v", cnt, err)
	assert.Equal(t, nil, err)
}

func TestMyPublisher_PubInfoByCompany(t *testing.T) {
	initTest(t)
	var company models.Company
	db.Instance().Get().Find(&company, 4248837)
	helper := NewSimpleCompanyHelper(&company, true)
	cnt, err := helper.PubInfo(db.Instance().Get())
	t.Logf("发布了%d, err:%v", cnt, err)
	assert.Equal(t, nil, err)
}

func TestMyPublisher_PubInfo(t *testing.T) {
	initTest(t)
	var info models.Info
	var product models.Product
	var company models.Company
	db.Instance().Get().Find(&info, 273723)
	db.Instance().Get().Find(&company, info.CompanyID)
	db.Instance().Get().Find(&product, *info.ProductID)
	m, err := services.Merchant.GetByCompanyId(info.CompanyID, models.PlatformHy88)
	if err != nil {
		t.Error(err)
	}
	info.Product = &product
	info.Company = &company
	mClient, _ := models.GetMerchant(models.PlatformHy88)
	mClient.LoginBy(m.Account)
	url, err := mClient.PublishInfo(info)
	if err != nil {
		t.Error(err)
	} else {
		t.Log(url)
	}
}

func TestMyPublisher_FixInfo(t *testing.T) {
	initTest(t)
	var infos []models.Info
	db.Instance().Get().Select("id, pub_res, titlepic").Joins("left join info_ext on info.id = info_ext.m_id").Where("status=2 and info.updated_at>'2021-06-17 09:20:00' and info.updated_at<'2021-06-21 03:00:00'").Order("id asc").Find(&infos)
	mClient, _ := models.GetMerchant(models.PlatformHy88)
	mClient.UpdateAccount("id", "7670")

	for _, info := range infos {
		url, err := mClient.PublishInfo(info)
		if err != nil {
			t.Error(err)
		} else {
			t.Log(url)
		}
	}
}

func TestMyPublisher_GenInfoCountOfUser(t *testing.T) {
	initTest(t)
	var user models.User
	db.Instance().Get().Where("phone=?", ***********).Find(&user)
	var products []models.Product
	db.Instance().Get().Where("company_id = ?", user.CompanyID).Find(&products)
	for _, p := range products {
		maker := NewSimpleTitleMaker(&p)
		t.Log(p.ID, " can product titles:", maker.CanProduce())
		maker.CloseDB()
		content := NewSimpleContentMaker(&p)
		t.Log(p.ID, " can produce contents:", content.CanProduce())
		var infos []models.Info
		db.Instance().Get().Where("product_id = ?", p.ID).Find(&infos)
		t.Log(p.ID, " already produce infos:", len(infos))
		if len(infos) == 0 && p.Status == dbtypes.ProductStatusTitleout {
			var user models.User
			db.Instance().Get().Where("company_id = ?", p.CompanyID).Find(&user)
			t.Log("user with phone:", user.Phone, " has title used product, which is not impossible")
			p.HashTitle = ""
			//p.HashContent = ""
			p.Status = dbtypes.ProductStatusPromote
			db.Instance().Get().Save(&p)
		}
		content.CloseDB()
	}
}

func TestFix(t *testing.T) {
	initTest(t)
	//phones := []string{
	//	"16738208444",
	//	"17630952407",
	//	"18930314920",
	//	"13861909388",
	//	"13383678835",
	//	"13916398388",
	//	"18857877664",
	//"15872032899",
	//"13761896558",
	//"18977615444",
	//"13128222811",
	//"13794902191",
	//}
	whites := []string{"13826470076", "13533028884"}
	var phones []string
	db.Instance().Get().Raw(`select phone from "user" where company_id in (select id from company where name in (select  name from company group by name having count(*) > 1))`).Pluck("phone", &phones)
	t.Log(phones)
	for _, phone := range phones {
		inWhite := false
		for _, w := range whites {
			if phone == w {
				inWhite = true
				break
			}
		}
		if inWhite {
			continue
		}
		var user models.User
		if err := db.Instance().Get().Where("phone = ?", phone).Find(&user).Error; err == nil {
			var currentCompany models.Company
			if err := db.Instance().Get().Where("id = ?", user.CompanyID).Find(&currentCompany).Error; err == nil {
				name := currentCompany.Name
				t.Log("company name is ", name)
				var companies []models.Company
				db.Instance().Get().Where("name = ? ", name).Find(&companies)
				if len(companies) == 1 {
					t.Log("user with phone ", phone, " is ok")
				} else {
					t.Log(phone, "user with phone has more than 1 company, current bind", user.CompanyID)
					ids := []uint64{}
					for _, compay := range companies {
						t.Log("company id :", compay.ID)
						ids = append(ids, compay.ID)
					}
					noUse := 0
					var merchants []models.Merchant
					if err := db.Instance().Get().Where("company_id in (?)", ids).Find(&merchants).Error; err != nil {
						noUse += 1
						t.Log(phone, " has no company match merchant", err)
					} else {
						if len(merchants) == 0 {
							noUse += 1
							t.Log(phone, " has no company match merchant", err)
						} else {
							for _, info := range merchants {
								t.Log("merchant ", info.CompanyID)
							}
						}
					}
					var products []models.Product
					if err := db.Instance().Get().Where("company_id in (?)", ids).Find(&products).Error; err != nil {
						noUse += 1
						t.Log(phone, " has no company match products")
					} else {
						if len(products) == 0 {
							noUse += 1
							t.Log(phone, " has no company match products", err)
						} else {
							for _, info := range products {
								t.Log("product", info.CompanyID)
							}
						}
					}
					var infos []models.Info
					if err := db.Instance().Get().Where("company_id in (?)", ids).Find(&infos).Error; err != nil {
						noUse += 1
						t.Log(phone, " has no company match infos")
					} else {
						if len(infos) == 0 {
							noUse += 1
							t.Log(phone, " has no company match infos", err)
						} else {
							for _, info := range infos {
								t.Log("info:", info.CompanyID)
							}
						}
					}
					if noUse == 3 {
						t.Log("can delete the companes item")
						//db.Instance().Get().Delete(&user)
						//db.Instance().Get().Where("id in (?)", ids).Delete(&models.Company{})
					}
				}
			}
		} else {
			t.Log(phone, " not found user")
		}

	}
}

func TestFix2(t *testing.T) {
	initTest(t)
	//phones := []string{
	//	"16738208444",
	//	"17630952407",
	//	"18930314920",
	//	"13861909388",
	//	"13383678835",
	//	"13916398388",
	//	"18857877664",
	//}
	whites := []uint64{14, 23}
	var companiesId []uint64
	db.Instance().Get().Raw(`select id from company where name in (select  name from company group by name having count(*) > 1)`).Pluck("id", &companiesId)
	t.Log(companiesId)
	for _, cid := range companiesId {
		inWhite := false
		for _, w := range whites {
			if cid == w {
				inWhite = true
				break
			}
		}
		if inWhite {
			continue
		}
		noUse := 0
		var merchants []models.Merchant
		if err := db.Instance().Get().Where("company_id in (?)", cid).Find(&merchants).Error; err != nil {
			noUse += 1
			t.Log(cid, " has no company match merchant", err)
		} else {
			if len(merchants) == 0 {
				noUse += 1
				t.Log(cid, " has no company match merchant", err)
			}

		}
		var products []models.Product
		if err := db.Instance().Get().Where("company_id in (?)", cid).Find(&products).Error; err != nil {
			noUse += 1
			t.Log(cid, " has no company match products")
		} else {
			if len(products) == 0 {
				noUse += 1
				t.Log(cid, " has no company match products", err)
			}

		}
		var infos []models.Info
		if err := db.Instance().Get().Where("company_id in (?)", cid).Find(&infos).Error; err != nil {
			noUse += 1
			t.Log(cid, " has no company match infos")
		} else {
			if len(infos) == 0 {
				noUse += 1
				t.Log(cid, " has no company match infos", err)
			}

		}
		if noUse == 3 {
			t.Log("can delete the companes item", cid)
			db.Instance().Get().Where("id  = ? ", cid).Delete(&models.Company{})
		}

	}
}

func TestFixTitleFinished(t *testing.T) {
	initTest(t)
	var products []models.Product
	db.Instance().Get().Where("status = ?", dbtypes.ProductStatusTitleout).Find(&products)
	for _, p := range products {
		maker := NewSimpleTitleMaker(&p)
		t.Log(p.ID, " can product titles:", maker.CanProduce())
		maker.CloseDB()
		content := NewSimpleContentMaker(&p)
		t.Log(p.ID, " can produce contents:", content.CanProduce())
		var infos []models.Info
		db.Instance().Get().Where("product_id = ?", p.ID).Find(&infos)
		t.Log(p.ID, " already produce infos:", len(infos))
		if len(infos) == 0 {
			var user models.User
			db.Instance().Get().Where("company_id = ?", p.CompanyID).Find(&user)
			t.Log("user with phone:", user.Phone, " has title used product, which is not impossible")
			p.HashTitle = ""
			p.HashContent = ""
			p.Status = dbtypes.ProductStatusPromote
			db.Instance().Get().Save(&p)
		}
		content.CloseDB()
	}
}

func TestFixNoMsgGenereated(t *testing.T) {
	initTest(t)
	var products []models.Product
	db.Instance().Get().Where("status = ?", dbtypes.ProductStatusPromote).Find(&products)
	for _, p := range products {
		maker := NewSimpleTitleMaker(&p)
		t.Log(p.ID, " can product titles:", maker.CanProduce())
		maker.CloseDB()
		content := NewSimpleContentMaker(&p)
		t.Log(p.ID, " can produce contents:", content.CanProduce())
		var infos []models.Info
		db.Instance().Get().Where("product_id = ?", p.ID).Find(&infos)
		t.Log(p.ID, " already produce infos:", len(infos))
		if len(infos) == 0 {
			var user models.User
			db.Instance().Get().Where("company_id = ?", p.CompanyID).Find(&user)
			t.Log("user with phone:", user.Phone, " has title used product, which is not impossible")
			p.HashTitle = ""
			p.HashContent = ""
			p.Status = dbtypes.ProductStatusPromote
			db.Instance().Get().Save(&p)
		}
		content.CloseDB()
	}
}

func TestFixSite(t *testing.T) {
	initTest(t)
	var merchants []models.Merchant
	db.Instance().Get().Find(&merchants)
	for _, m := range merchants {
		var c models.Company
		db.Instance().Get().Find(&c, m.CompanyID)
		if c.Site == "" {
			m.CompanySite = c.Site
			db.Instance().Get().Save(&m)
		}
	}
}

func TestFindErrOfProduct(t *testing.T) {
	initTest(t)
	limit := 1
	for offset := 0; offset < 1612; offset++ {
		var p []models.Product
		if err := db.Instance().Get().Order("id").Offset(offset).Limit(limit).Find(&p).Error; err != nil {
			t.Log("product id error ", offset)
		}
	}

}

func TestFindErrOfInfo(t *testing.T) {
	initTest(t)
	limit := 400
	for offset := 190000; offset < 190400; offset += limit {
		var infos []models.Info
		if err := db.Instance().Get().Where("id>=?", offset).Select("id").Order("id").Limit(limit).Find(&infos).Error; err != nil {
			t.Log("info offset error ", offset)
		} else {
			for _, item := range infos {
				var info models.Info
				if err := db.Instance().Get().Find(&info, item.ID).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
					t.Log("delete info:", item.ID)
					db.Instance().Get().Delete(item)
				}
			}
		}
	}

}

func TestFindMax(t *testing.T) {
	initTest(t)
	var productsAdded int64
	db.Instance().Get().Model(models.Product{}).Where("company_id = ?", 61).Count(&productsAdded)
	t.Log(productsAdded)
}

func TestErrProduct(t *testing.T) {
	initTest(t)
	var products []models.Product
	db.Instance().Get().Find(&products)
	for _, p := range products {
		if _, err := p.IsValid(); err == configs.ErrProductCate {
			t.Log(p.Name, " is invalid, err:", err)
			product.NewProductService().TryFixRootCat(&p, true)
			t.Log(p.Name, " after fix cate:", p.Cate, p.Status)
		}
	}
}

func TestGenInfo(t *testing.T) {
	initTest(t)
	var info models.Info
	var p models.Product
	db.Instance().Get().Find(&info, 4203473)
	db.Instance().Get().Find(&p, *info.ProductID)
	helper := SimpleCompanyHelper{company: nil,
		merchantWrapMap:           make(map[uint64]*MerchantWrap),
		pubCountOfMerchantOnToday: make(map[uint64]int),
	}
	t.Log(helper.genInfo(*info.Title, info.Description, &p, nil))
}

func TestRandVideo(t *testing.T) {
	initTest(t)
	var product models.Product
	db.Instance().Get().Where("id=?", 1351).First(&product)
	t.Log(product.RandVideo())
}

func TestRemoveQQ(t *testing.T) {
	initTest(t)
	var products []models.Product
	q := db.Instance().Get()
	if err := q.Where("status=4 and company_id in (?)", q.Model(&models.Merchant{}).Where("plat_form = 8 AND cert_status = 4").Select("distinct company_id")).Find(&products).Error; err != nil {
		t.Fatal(err)
	} else {
		for _, p := range products {
			delete(p.Properties, "微信号")
			delete(p.Properties, "QQ号")
			if err := db.Instance().Get().Save(&p).Error; err != nil {
				t.Fatal(err)
			}
		}
	}
}

func TestFindBafang(t *testing.T) {
	initTest(t)
	var items []models.Merchant
	db.Instance().Get().Model(models.Merchant{}).Where("id=22464").Find(&items)
	for _, item := range items {
		if _, ok := item.Account["id"].(int); ok {
			t.Error(item.ID, item.Account)
		}
	}
}
func TestProductCompacity(t *testing.T) {
	initTest(t)
	t.Run("old format", func(t *testing.T) {
		var product models.Product
		db.Instance().Get().Find(&product, 1399)
		t.Log(product.RandPic(3))
		t.Log(product.RandTitlePic())
	})

	t.Run("new format", func(t *testing.T) {
		var product models.Product
		db.Instance().Get().Find(&product, 1398)
		t.Log(product.RandPic(3))
		t.Log(product.RandTitlePic())
	})
}
