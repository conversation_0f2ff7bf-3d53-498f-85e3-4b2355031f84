package controllers

import (
	_ "git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/business/merchanter"
)

type VideoController struct {
	Ctx context.Context
}

func HandleVideo(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(VideoController))
}

// Get Video godoc
// @Summary 获取视频列表
// @Description 获取视频列表
// @Tags client
// @Produce  json
// @Param offset query int true "int"
// @Param limit query int true "int"
// @Success 200 {object} publisher.RanksData
// @Security ApiKeyAuth
// @Router /videos [get]
func (c *VideoController) Get() interface{} {
	limit := c.Ctx.URLParamIntDefault("limit", 1)
	offset := c.Ctx.URLParamIntDefault("offset", 1)
	mClient := RetriveBindedMerchantClient(c.Ctx)
	if data, err := mClient.(*merchanter.Hy88Merchant).GetVideos(limit, offset); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx, true)
	} else {
		return utils.NewApiOkWrapper(data)
	}
}
