package controllers

import (
	"crypto/hmac"
	"crypto/sha256"
	"fmt"
	"git.paihang8.com/lib/goutils"
	"github.com/iris-contrib/middleware/jwt"
	"github.com/kataras/iris/v12/context"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"net/url"
	"strconv"
	"strings"
)

type LoginInfo struct {
	UserId    uint64
	CompanyId uint64
	OemId     uint64
}

type SysLoginInfo struct {
	SysUserId uint64
	Scopes    []string
	Role      *string
}

func RetriveLoginInfo(ctx context.Context) LoginInfo {
	return LoginInfo{
		UserId:    retriveUserID(ctx),
		CompanyId: retriveCompanyID(ctx),
		OemId:     retriveOemID(ctx),
	}
}

func RetriveSysLoginInfo(ctx context.Context) SysLoginInfo {
	role := retriveRole(ctx)
	return SysLoginInfo{
		SysUserId: retriveUserID(ctx),
		Scopes:    nil,
		Role:      &role,
	}
}

func retriveUserID(ctx context.Context) uint64 {
	token := ctx.Values().Get("jwt").(*jwt.Token)
	user := token.Claims.(jwt.MapClaims)
	return uint64(user["user_id"].(float64))
}

func retriveOemID(ctx context.Context) uint64 {
	token := ctx.Values().Get("jwt").(*jwt.Token)
	user := token.Claims.(jwt.MapClaims)
	return uint64(user["oem_id"].(float64))
}

func retriveCompanyID(ctx context.Context) uint64 {
	token := ctx.Values().Get("jwt").(*jwt.Token)
	user := token.Claims.(jwt.MapClaims)
	return uint64(user["cid"].(float64))
}

func retriveRole(ctx context.Context) string {
	token := ctx.Values().Get("jwt").(*jwt.Token)
	user := token.Claims.(jwt.MapClaims)
	return user["role"].(string)
}

func RetriveBindedMerchantClient(ctx context.Context) models.Merchanter {
	mClient, _ := models.GetMerchant(models.PlatformHy88)
	mClient.UpdateAccount("id", strconv.Itoa(int(retriveOemID(ctx))))
	return mClient
}

func HashPassword(username, password string) string {
	hash := hmac.New(sha256.New, []byte(username))
	hash.Write([]byte(password))
	return fmt.Sprintf("%x", hash.Sum(nil))
}

func VerifyPassword(username, password, hashpassword string) bool {
	hashed := HashPassword(username, password)
	return hashed == hashpassword
}

func HasPermission(ctx context.Context, permissions []string, any ...bool) bool {
	hasPermission := true
	token := ctx.Values().Get("jwt").(*jwt.Token)
	user := token.Claims.(jwt.MapClaims)

	if _, ok := user["scopes"]; ok {
		rawPermissions := user["scopes"].([]interface{})
		//整理成map, 后面好用
		holdPermissions := map[string]struct{}{}
		for _, scope := range rawPermissions {
			parts := strings.Split(scope.(string), ":")
			if len(parts) > 1 {
				for i := 1; i < len(parts); i++ {
					holdPermissions[parts[0]+":"+parts[i]] = struct{}{}
				}
			} else {
				holdPermissions[parts[0]] = struct{}{}
			}

		}

		hasCount := 0
		for _, scope := range permissions {
			if _, ok := holdPermissions[scope]; ok {
				hasCount++
			}
		}
		if hasCount < len(permissions) && len(any) == 0 {
			hasPermission = false
		}
	} else {
		hasPermission = false
	}
	if !hasPermission {
		configs.ApiError{Code: configs.ErrScopeMissing.Error(), Msg: "权限不足"}.Response(ctx)
	}
	return hasPermission
}

func toWebUrl(toUrl string, ctx context.Context) string {
	loginInfo := RetriveLoginInfo(ctx)
	merchant, _ := services.Merchant.GetByCompanyId(loginInfo.CompanyId, models.PlatformHy88)
	ts := dbtypes.SHNow().Unix()
	uid := strconv.Itoa(int(loginInfo.OemId))
	mobile := merchant.Account["mobile"].(string)
	password := merchant.Account["password"].(string)
	hash := goutils.Md5str(fmt.Sprintf("%s%s%s%d", uid, mobile, goutils.Md5str(password), ts))

	v := url.Values{}
	v.Add("user_id", uid)
	v.Add("user_mobile", mobile)
	v.Add("hash", hash)
	v.Add("url", toUrl)
	v.Add("t", strconv.Itoa(int(ts)))
	query := v.Encode()
	return "http://my.huangye88.com/login/redirect.html?" + query
}
