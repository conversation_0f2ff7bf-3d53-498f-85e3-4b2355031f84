package controllers

import (
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/dbtypes"
)

type CompanyProductController struct {
	Ctx       context.Context
	loginInfo LoginInfo
}

func HandleCompanyProduct(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(CompanyProductController))
}

// Auth godoc
// @Summary 获取发布产品列表
// @Description 获取发布产品列表
// @Description status -1:所有状态(默认值) 0:等待推送 1:推送成功 3:推送失败
// @Tags client
// @Accept  json
// @Produce  json
// @Param sortby query string  false "string enums" Enums(created_at,updated_at)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Param keyword query string false "搜索关键词，搜索标题"
// @Param status query string false "发布状态，int enums" Enums(-1, 0,1,3)
// @Success 200 {array} models.CompanyProduct
// @Security ApiKeyAuth
// @Router /companyproduct/query [get]
func (c *CompanyProductController) GetQuery() interface{} {

	status := c.Ctx.URLParamIntDefault("status", dbtypes.CompanyProductStatusNone)
	keyword := c.Ctx.URLParam("keyword")

	sortby := c.Ctx.URLParamDefault("sortby", "created_at")
	order := c.Ctx.URLParamDefault("order", "desc")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)

	if infos, err := services.NewCompanyProductService().GetCompanyProducts([]uint64{c.loginInfo.CompanyId}, status, keyword, sortby, order, limit, offset, false); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(infos)
	}
}

// Auth godoc
// @Summary 获取发布产品列表 带分页
// @Description 获取发布产品列表 带分页
// @Description status 0:等待推送 1:推送成功 3:推送失败
// @Tags client
// @Accept  json
// @Produce  json
// @Param sortby query string  false "string enums" Enums(created_at,updated_at)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Param keyword query string false "搜索关键词，搜索标题"
// @Param status query string false "发布状态，int enums" Enums(0,1,3)
// @Success 200 {object} services.CompanyProductPaginator
// @Security ApiKeyAuth
// @Router /companyproduct/items [get]
func (c *CompanyProductController) GetItems() interface{} {

	status := c.Ctx.URLParamIntDefault("status", dbtypes.CompanyProductStatusNone)
	keyword := c.Ctx.URLParam("keyword")

	sortby := c.Ctx.URLParamDefault("sortby", "created_at")
	order := c.Ctx.URLParamDefault("order", "desc")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)

	if infos, err := services.NewCompanyProductService().GetCompanyProductsWithPaginator([]uint64{c.loginInfo.CompanyId}, status, keyword, sortby, order, limit, offset, false); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(infos)
	}
}

func (c *CompanyProductController) BeginRequest(ctx context.Context) {
	c.loginInfo = RetriveLoginInfo(ctx)
}

func (c *CompanyProductController) EndRequest(ctx context.Context) {
}
