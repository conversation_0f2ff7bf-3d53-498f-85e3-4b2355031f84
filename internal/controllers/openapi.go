package controllers

import (
	"errors"
	"git.paihang8.com/lib/goutils"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/services"
	"strconv"
	"time"
)

type ApiController struct {
	Ctx context.Context
}

const Secret = "fdsifdsaioXfdsafasd"

func HandleApi(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(ApiController))
}

// openapi godoc
// @Summary 获取关键词列表
// @Description 获取关键词列表。。 关键词组成 'word source'
// @Description source: baidu:百度电脑端; baidu_m:百度移动
// @Tags client
// @Produce  json
// @Param hash query string true "string"
// @Param annouce query string true "string"
// @Param limit query int true "int"
// @Success 200 {array} string
// @Router /openapi/tasks [get]
func (c *ApiController) GetTasks() interface{} {
	hash := c.Ctx.URLParamDefault("hash", "")
	annouce := c.Ctx.URLParamDefault("annouce", "")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	if err := verify(hash, annouce); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  "hash invalid",
		}.Response(c.Ctx, true)
	}
	return utils.NewApiOkWrapper(services.Rank.Getkeywords(limit))
}

// openapi godoc
// @Summary 获取关键词列表
// @Description 获取关键词列表。。 关键词组成 'word source'
// @Description source: baidu:百度电脑端; baidu_m:百度移动
// @Tags client
// @Produce  json
// @Param hash query string true "string"
// @Param annouce query string true "string"
// @Param url query string true "url"
// @Param keyword query string true "关键词"
// @Param rank query string true "排名"
// @Param eg query string true "source:like BD"
// @Success 200 {object} models.Infos
// @Router /openapi/rank/status [get]
func (c *ApiController) GetRankStatus() interface{} {
	hash := c.Ctx.URLParamDefault("hash", "")
	annouce := c.Ctx.URLParamDefault("annouce", "")
	url := c.Ctx.URLParamDefault("url", "")
	keyword := c.Ctx.URLParamDefault("keyword", "")
	rank := c.Ctx.URLParamDefault("rank", "")
	eg := c.Ctx.URLParamDefault("eg", "")
	egg := services.EgRankType(eg)
	if err := verify(hash, annouce); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  "hash invalid",
		}.Response(c.Ctx, true)
	}
	if data, err := services.Rank.Status(keyword, url, egg, rank); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx, true)
	} else {
		return utils.NewApiOkWrapper(data)
	}
}

// openapi godoc
// @Summary 添加排名
// @Description 添加排名，添加前，需要小调用status, 避免重复插入
// @Tags client
// @Produce  json
// @Param hash query string true "string"
// @Param annouce query string true "string"
// @Param url query string true "url"
// @Param keyword query string true "关键词"
// @Param rank query string true "排名"
// @Param eg query string true "source:like BD"
// @Success 200 {object} models.Infos
// @Router /openapi/rank [post]
func (c *ApiController) PostRank() interface{} {
	hash := c.Ctx.URLParamDefault("hash", "")
	annouce := c.Ctx.URLParamDefault("annouce", "")
	url := c.Ctx.URLParamDefault("url", "")
	keyword := c.Ctx.URLParamDefault("keyword", "")
	rank := c.Ctx.URLParamDefault("rank", "")
	eg := c.Ctx.URLParamDefault("eg", "")
	egg := services.EgRankType(eg)
	snap := c.Ctx.URLParamDefault("snap", "")
	if err := verify(hash, annouce); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  "hash invalid",
		}.Response(c.Ctx, true)
	}
	if err := services.Rank.Insert(keyword, url, egg, rank, snap); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx, true)
	} else {
		return utils.NewApiOkWrapper(nil)
	}
}
func verify(hash, annouce string) error {
	if len(hash) != 32 {
		return errors.New("hash invalid")
	}
	now := time.Now().Unix() % 10000
	mhash := goutils.Md5str(Secret + annouce)
	if mhash[:28] != hash[:28] {
		return errors.New("hash incorrect")
	}
	t, _ := strconv.Atoi(hash[28:32])
	if now-int64(t) > 60 {
		return errors.New("request expired")
	}
	return nil
}
