package controllers

import (
	"encoding/json"
	"testing"
)

func TestHashPassword(t *testing.T) {
	users := []struct {
		username string
		password string
	}{{"huifa", "huifa123!"},
		{"admin", "abc"},
	}
	for _, u := range users {
		t.Log(HashPassword(u.username, u.password))
	}
}

type Items []struct {
	Id int `json:"id"`
}

func TestFindErr(t *testing.T) {
	text := `
[{"id":1066}, 
 {"id":1067}, 
 {"id":1069}, 
 {"id":1070}, 
 {"id":1071}, 
 {"id":1072}, 
 {"id":1073}, 
 {"id":1074}, 
 {"id":1075}, 
 {"id":1076}, 
 {"id":1077}, 
 {"id":1078}, 
 {"id":1079}, 
 {"id":1080}, 
 {"id":1081}, 
 {"id":1082}, 
 {"id":1083}, 
 {"id":1084}, 
 {"id":1085}, 
 {"id":1086}, 
 {"id":1087}, 
 {"id":1088}, 
 {"id":1089}, 
 {"id":1090}, 
 {"id":1095}, 
 {"id":1096}, 
 {"id":1097}, 
 {"id":1098}, 
 {"id":1099}, 
 {"id":1100}, 
 {"id":1101}, 
 {"id":1102}, 
 {"id":1103}, 
 {"id":1104}, 
 {"id":1105}, 
 {"id":1106}, 
 {"id":1107}, 
 {"id":1108}, 
 {"id":1109}, 
 {"id":1110}, 
 {"id":1111}, 
 {"id":1112}, 
 {"id":1113}, 
 {"id":1114}, 
 {"id":1115}, 
 {"id":1116}, 
 {"id":1117}, 
 {"id":1118}, 
 {"id":1119}, 
 {"id":1120}, 
 {"id":1121}, 
 {"id":1122}, 
 {"id":1123}, 
 {"id":1124}, 
 {"id":1125}, 
 {"id":1126}, 
 {"id":1127}, 
 {"id":1128}, 
 {"id":1129}, 
 {"id":1130}, 
 {"id":1131}, 
 {"id":1132}, 
 {"id":1133}, 
 {"id":1134}, 
 {"id":1135}, 
 {"id":1136}, 
 {"id":1137}, 
 {"id":1138}, 
 {"id":1139}, 
 {"id":1140}, 
 {"id":1141}, 
 {"id":1142}, 
 {"id":1143}, 
 {"id":1145}, 
 {"id":1147}, 
 {"id":1149}, 
 {"id":1150}, 
 {"id":1151}, 
 {"id":1152}, 
 {"id":1153}, 
 {"id":1154}, 
 {"id":1155}, 
 {"id":1156}, 
 {"id":1157}, 
 {"id":1158}, 
 {"id":1159}, 
 {"id":1160}, 
 {"id":1161}, 
 {"id":1162}, 
 {"id":1163}, 
 {"id":1164}, 
 {"id":1165}, 
 {"id":1166}, 
 {"id":1167}, 
 {"id":1168}, 
 {"id":1169}, 
 {"id":1171}, 
 {"id":1172}, 
 {"id":1173}, 
 {"id":1174}]`
	ids := []int{1174, 1173, 1172, 1171, 1169, 1168, 1167, 1165, 1164, 1163, 1162, 1161, 1160, 1159, 1158, 1157, 1156, 1155, 1154, 1153, 1152, 1151, 1150, 1149, 1147, 1145, 1143, 1142, 1141, 1140, 1139, 1138, 1137, 1136, 1135, 1134, 1133, 1132, 1131, 1130, 1129, 1128, 1127, 1126, 1125, 1124, 1123, 1122, 1121, 1120, 1119, 1118, 1117, 1116, 1115, 1114, 1113, 1112, 1111, 1110, 1109, 1108, 1107, 1106, 1105, 1104, 1103, 1102, 1101, 1100, 1099, 1098, 1097, 1096, 1095, 1090, 1089, 1088, 1087, 1085, 1084, 1083, 1082, 1081, 1080, 1079, 1078, 1077, 1076, 1075, 1074, 1073, 1072, 1071, 1070, 1069, 1067, 1066}
	var items Items
	json.Unmarshal([]byte(text), &items)

	t.Log(len(items), len(ids))
	for _, item := range items {
		found := false
		for _, ex := range ids {
			if ex == item.Id {
				found = true
				break
			}
		}
		if !found {
			t.Log(item, "is not found")
		}
	}
}
