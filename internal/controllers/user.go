package controllers

import (
	ctx "context"
	"fmt"
	"git.paihang8.com/lib/goutils"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/business/merchanter"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/product"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"golang.org/x/sync/errgroup"
	"net/url"
	"strconv"
)

type UserController struct {
	Ctx       context.Context
	loginInfo LoginInfo
}

func HandleUser(app *mvc.Application) {
	app.Handle(new(UserController))
}

// User godoc
// @Summary 获取当前用户信息
// @Description 获取当前用户信息
// @Tags client
// @Produce  json
// @Success 200 {object} models.User
// @Security ApiKeyAuth
// @Router /user [get]
func (c *UserController) Get() interface{} {
	if user, err := services.NewUserService().GetUserById(c.loginInfo.UserId); err != nil {
		return utils.ApiError{Code: utils.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(user)
	}
}

// User godoc
// @Summary 免登陆到黄页88页面
// @Description 免登陆到黄页88页面
// @Tags client
// @Param url query string true "要跳转的huangye88页面"
// @Success 200 {object} configs.ApiOkWrapper
// @Security ApiKeyAuth
// @Router /user/redirect [get]
func (c *UserController) GetRedirect() interface{} {
	turl := c.Ctx.URLParam("url")
	if merchant, err := services.Merchant.GetByCompanyId(c.loginInfo.CompanyId, models.PlatformHy88); err != nil {
		return utils.ApiError{Code: utils.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
	} else {
		ts := dbtypes.SHNow().Unix()
		uid := strconv.Itoa(int(c.loginInfo.OemId))
		mobile := merchant.Account["mobile"].(string)
		password := merchant.Account["password"].(string)
		hash := goutils.Md5str(fmt.Sprintf("%s%s%s%d", uid, mobile, goutils.Md5str(password), ts))

		v := url.Values{}
		v.Add("user_id", uid)
		v.Add("user_mobile", mobile)
		v.Add("hash", hash)
		v.Add("url", turl)
		v.Add("t", strconv.Itoa(int(ts)))
		query := v.Encode()
		return utils.NewApiOkWrapper("http://my.huangye88.com/login/redirect.html?" + query)
	}
}

// User godoc
// @Summary 用户修改密码
// @Description 用户修改密码, 未启用
// @Tags client
// @Accept  json
// @Produce  json
// @Param body body schemas.ModifyPassword true "ModifyPassword"
// @Success 200 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /user/modify_password [post]
func (c *UserController) PostModify_password() interface{} {
	var modify schemas.ModifyPassword
	if err := c.Ctx.ReadJSON(&modify); err != nil {
		return utils.ApiError{utils.ErrModifyPasswordFailed.Error(), err.Error(), nil}.Response(c.Ctx)
	}
	var user models.User
	if err := db.Instance().Get().Find(&user, c.loginInfo.UserId).Error; err != nil {
		return utils.ApiError{utils.ErrModifyPasswordFailed.Error(), err.Error(), nil}.Response(c.Ctx)
	}
	if !VerifyPassword(user.Username, user.Password, modify.Old) {
		return utils.ApiError{utils.ErrModifyPasswordFailed.Error(), "旧密码不正确", nil}.Response(c.Ctx)
	}

	user.Password = HashPassword(user.Username, modify.New)
	if err := db.Instance().Get().Save(&user).Error; err != nil {
		return utils.ApiError{utils.ErrModifyPasswordFailed.Error(), err.Error(), nil}.Response(c.Ctx)
	}
	return utils.NewApiOkWrapper(nil)
}

// User godoc
// @Summary 获取当前用户开通功能列表
// @Description 获取当前用户信息
// @Tags client
// @Produce  json
// @Success 200 {array} models.UserPlatform
// @Security ApiKeyAuth
// @Router /user/func_list [get]
func (c *UserController) GetFunc_list() interface{} {
	if list, err := services.NewUserPlatformService().GetFuncListByCompanyId(c.loginInfo.CompanyId); err != nil {
		return utils.ApiError{Code: utils.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(list)
	}
}

// User godoc
// @Summary 获取当前用户数据汇总
// @Description 获取当前用户数据汇总
// @Tags client
// @Produce  json
// @Success 200 {object} schemas.UserOverView
// @Security ApiKeyAuth
// @Router /user/overview [get]
func (c *UserController) GetOverview() interface{} {
	result := schemas.UserOverView{}
	var err error
	eg, _ := errgroup.WithContext(ctx.Background())
	// 开通平台数
	eg.Go(func() error {
		if ms, err := services.Merchant.GetEnabled(c.loginInfo.CompanyId, true, false); err != nil {
			return err
		} else {
			result.Platform.Activated = len(ms)
			result.Platform.Total = models.CountOfPlatform()
		}
		return nil
	})
	//信息统计
	eg.Go(func() error {
		result.Info.Yesterday = services.NewInfoService().SuccessCountByCompanyId(c.loginInfo.CompanyId, true)
		result.Info.Total = services.NewInfoService().SuccessCountByCompanyId(c.loginInfo.CompanyId, false)
		return nil
	})
	// 产品统计
	eg.Go(func() error {
		if data, err := product.NewProductService().CountByStatusOfCompany(c.loginInfo.CompanyId); err != nil {
			return err
		} else {
			result.Product.Total = data[0]
			for key, item := range data {
				if key == dbtypes.ProductStatusPromote {
					result.Product.Promotions = item
				}
			}
		}
		return nil
	})
	// 排名统计
	eg.Go(func() error {
		mClient := RetriveBindedMerchantClient(c.Ctx)
		if data, err := mClient.(*merchanter.Hy88Merchant).GetRanks(0, 1); err != nil {
			return err
		} else {
			result.Rank.HY88 = int64(data.Pageinfos.Total)
			result.Rank.Total += int64(data.Pageinfos.Total)
		}
		if _, total, err := services.NewRankService().GetRanks(int(c.loginInfo.CompanyId), 1, 1, 0, -1); err != nil {
			return err
		} else {
			result.Rank.Total += total
		}
		return nil
	})
	eg.Wait()
	if err != nil {
		return utils.ApiError{Code: utils.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	return utils.NewApiOkWrapper(result)
}

func (c *UserController) BeginRequest(ctx context.Context) {
	c.loginInfo = RetriveLoginInfo(ctx)
}

func (c *UserController) EndRequest(ctx context.Context) {
}
