package controllers

import (
	"fmt"
	"git.paihang8.com/lib/goutils"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"testing"
)

func TestUserController_GetRedirect(t *testing.T) {
	/*
		{"mobile": "15896328282", "password": "hf654123", "id": 3005933, "token": "15696514986471-b8f560a92d7bbc924f99bb8ec4fd990dce76f101"}
	*/
	uid := "3005933"
	mobile := "15896328282"
	password := "hf654123"
	ts := dbtypes.SHNow().Unix()
	hash := goutils.Md5str(fmt.Sprintf("%s%s%s%d", uid, mobile, goutils.Md5str(password), ts))
	url := "http://my.huangye88.com/manageinfo/"
	t.Logf("http://my.huangye88.com/login/redirect.html?user_id=%s&user_mobile=%s&hash=%s&url=%s&t=%d", uid, mobile, hash, url, ts)
}

func TestUserController_GetRedirect2(t *testing.T) {
	config, err := configs.NewConfig("../config.ini")
	if err != nil {
		t.Fatal(err)
	}
	db.InitRedis(*config)
	db.Init(*config, db.ConnectorTypeMysql)
	url := "http://fabuxinxi.huangye88.com/"
	var merchant models.Merchant
	if err := db.Instance().Get().Find(&merchant, 27).Error; err != nil {
		t.Error(err)
	}
	uid := merchant.Account["id"].(string)
	mobile := merchant.Account["mobile"].(string)
	password := merchant.Account["password"].(string)
	hash := goutils.Md5str(fmt.Sprintf("%s%s%s", uid, mobile, goutils.Md5str(password)))
	t.Log(fmt.Sprintf("http://my.huangye88.com/login/redirect.html?user_id=%s&user_mobile=%s&hash=%s&url=%s", uid, mobile, hash, url), 302)
}
