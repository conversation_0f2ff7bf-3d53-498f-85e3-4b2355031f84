package v2

import (
	_ "git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services"
)

type AreaController struct {
	Ctx context.Context
}

func HandleArea(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(AreaController))
}

// Area godoc
// @Summary 根据父级id获取下一级地区
// @Description 传0获取省份和直辖市
// @Description 不用接口，用js的方式:  http://api.huangye88.com/js/apiarea.js
// @Tags common
// @Produce  json
// @Param pid path int true "int"
// @Success 200 {array} schemas.Area
// @Router /v2/areas/{pid} [get]
func (c *AreaController) GetBy(pid int) interface{} {
	if data, err := services.Area.FindSubsByPid(uint64(pid)); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx, true)
	} else {
		return utils.NewApiOkWrapper(data)
	}
}

// Area godoc
// @Summary js版本
// @Description
// @Description   http://api.huangye88.com/js/apiarea.js的發發助手版本
// @Tags common
// @Produce html,json,xml,application/javascript
// @Success 200 {string} string
// @Router /v2/areas/all [get]
func (c *AreaController) GetAll() interface{} {
	detail := services.Area.AllAreas()
	c.Ctx.ContentType("application/javascript")
	return detail
}

// Area godoc
// @Summary 检查地区是否ok
// @Description 检查分类是否ok
// @Description 只检查已经开通的平台，如果没有对应的数据，需要提示用户。
// @Tags common
// @Produce  json
// @Param id query int true "地区id"
// @Param cid query int true "公司id"
// @Success 200 {array} string
// @Security ApiKeyAuth
// @Router /v2/areas/check [get]
func (c *AreaController) GetCheck() interface{} {
	id := c.Ctx.URLParamIntDefault("id", 0)
	cid := c.Ctx.URLParamIntDefault("cid", 0)
	if id == 0 || cid == 0 {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  "参数错误",
		}.Response(c.Ctx, true)
	}
	pf, _ := services.NewUserPlatformService().GetAutoPostedPlatForms(uint64(cid))
	var invalid []string
	for _, form := range pf {
		if _, e := services.Area.GetMapping(uint64(id), form); e != nil {
			invalid = append(invalid, models.PlatFormName(form))
		}
	}
	return utils.NewApiOkWrapper(invalid)
}
