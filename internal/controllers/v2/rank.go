package v2

import (
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/controllers"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
)

type RankController struct {
	Ctx       context.Context
	loginInfo controllers.LoginInfo
}

func HandleRanks(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(RankController))
}

// Rank godoc
// @Summary 获取搜索排名列表
// @Description 获取搜索排名列表
// @Description query string parmas:
// @Description eg: 0 全部(默认值) 1 百度 2 360， 3 搜狗 4 神马 5头条
// @Description platform: -1 全部(默认值) 0 黄页88 1 八方资源 2 中国供应商 4 sole网  5 爱采购
// @Tags client
// @Produce  json
// @Param eg query int true "int enums" Enums(0,1,2,3,4,5)
// @Param pf query int true "int enums" Enums(0,1,2,3,4,5)
// @Param page query int true "int"
// @Success 200 {object} models.Rank
// @Security ApiKeyAuth
// @Router /v2/ranks [get]
func (c *RankController) Get() interface{} {
	page := c.Ctx.URLParamIntDefault("page", 1)
	eg := c.Ctx.URLParamIntDefault("eg", 0)
	pf := c.Ctx.URLParamIntDefault("pf", -1)
	if data, total, err := services.Rank.GetRanks(int(c.loginInfo.CompanyId), page, 10, eg, pf); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx, true)
	} else {
		ret := schemas.RanksData{}
		for _, v := range data {
			ret.RanksData.Data = append(ret.RanksData.Data, publisher.RankItem{
				ID:      int(v.ID),
				UID:     int(v.CompanyID),
				Rank:    v.Rank,
				Engine:  v.Eg,
				URL:     v.Url,
				Keyword: v.Keyword,
				Snap:    v.Snap,
				Times:   v.UpdatedAt.Format("2006-01-02 15:04:05"),
			})
		}
		ret.Pageinfos.Total = int(total)
		ret.Pageinfos.Page = page
		ret.Pageinfos.Pagesize = 10
		ret.Pageinfos.Count = 10
		ret.Statics.InfoTotal = int(services.NewInfoService().CountOtherInfos(c.loginInfo.CompanyId))
		if ranks, err := services.Rank.GroupRanksByEg(c.loginInfo.CompanyId); err == nil {
			for _, rank := range ranks {
				ret.Statics.RankTotal += rank.C
				switch services.EgRankType(rank.Eg) {
				case services.EgRankBaidu, services.EgRankbaiduM:
					ret.Statics.BaiDu += rank.C
				case services.EgRankShenma:
					ret.Statics.ShenMa += rank.C
				case services.EgRankHaosou:
					ret.Statics.HaoSou += rank.C
				case services.EgRankSogou, services.EgRankSogouM:
					ret.Statics.Sougou += rank.C
				case services.EgRankToutiao, services.EgRankToutiaoM:
					ret.Statics.TouTiao += rank.C
				}
			}
		}
		return utils.NewApiOkWrapper(ret)
	}
}

func (c *RankController) BeginRequest(ctx context.Context) {
	c.loginInfo = controllers.RetriveLoginInfo(ctx)
}

func (c *RankController) EndRequest(ctx context.Context) {
}
