package v2

import (
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/cat"
)

type CategoryController struct {
	Ctx context.Context
}

func HandleCategory(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(CategoryController))
}

// Category godoc
// @Summary 获取分类详情
// @Description
// @Tags common
// @Produce  json
// @Param id path int true "int"
// @Success 200 {object} models.Cat
// @Router /v2/category/{id} [get]
func (c *CategoryController) GetBy(id uint64) interface{} {
	if detail, err := cat.NewCatService().Find(id); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx, true)
	} else {
		return utils.NewApiOkWrapper(detail)
	}
}

// Category godoc
// @Summary 获取所有分类,level版
// @Description
// @Tags common
// @Produce html,json,xml,application/javascript
// @Success 200 {string} string
// @Router /v2/category [get]
func (c *CategoryController) Get() interface{} {
	detail := cat.NewCatService().GenJs()
	c.Ctx.ContentType("application/javascript")
	return detail
}

// Category godoc
// @Summary 获取所有分类, 树版
// @Description
// @Tags common
// @Produce html,json,xml,application/javascript
// @Success 200 {string} string
// @Router /v2/category/tree [get]
func (c *CategoryController) GetTree() interface{} {
	detail := cat.NewCatService().GenJs2()
	c.Ctx.ContentType("application/javascript")
	return detail
}

// Category godoc
// @Summary 获取子分类
// @Description id=0表示获取1级分类。。其他为具体分类的子分类
// @Tags common
// @Produce  json
// @Param id path int true "id"
// @Success 200 {array} schemas.Cat
// @Security ApiKeyAuth
// @Router /v2/category/{id}/subs [get]
func (c *CategoryController) GetBySubs(id uint64) interface{} {
	if subs, err := cat.NewCatService().FindSubsByPid(id, 0, 1000); err != nil {
		subs = []schemas.Cat{}
		return utils.NewApiOkWrapper(subs)
	} else {
		return utils.NewApiOkWrapper(subs)
	}
}

// Category godoc
// @Summary 获取属性列表
// @Description input->(string),textarea->长文本,radio->单选按钮,checkbox->多选按钮,select->下拉框
// @Tags common
// @Produce  json
// @Param id path int true "id"
// @Param merge query string 0 "合并百姓网属性"
// @Success 200 {array} schemas.CatField
// @Security ApiKeyAuth
// @Router /v2/category/{id}/properties [get]
func (c *CategoryController) GetByProperties(id uint64) interface{} {
	merge := c.Ctx.URLParamDefault("merge", "0")
	if properties, err := cat.NewCatService().GetProperties(id); err == nil {
		if merge == "1" {
			if bxproperties, err := cat.NewCatService().GetBaixingProperties(id, "", ""); err == nil {
				for _, meta := range bxproperties {
					options := []interface{}{}
					for _, v := range meta.Data {
						options = append(options, v)
					}
					if meta.Name == "分类" {
						continue
					}
					properties = append(properties, schemas.CatField{
						Displayname:  meta.Name,
						Fieldtype:    string(meta.Type),
						Fieldname:    meta.Name,
						Fieldoptions: options,
						Sort:         0,
						Required:     meta.Required,
					})
				}
			} else {
			}
		}

		return utils.NewApiOkWrapper(properties)
	} else {
		return utils.NewApiOkWrapper(properties)
	}
}

// Category godoc
// @Summary 获取百姓网属性列表
// @Description input->(string),textarea->长文本,radio->单选按钮,checkbox->多选按钮,select->下拉框
// @Tags common
// @Produce  json
// @Param id path int true "id"
// @Param pvalue query string false "父级"
// @Param name query string false "meta名字"
// @Success 200 {array} schemas.CatField
// @Security ApiKeyAuth
// @Router /v2/category/baixing/{id}/properties [get]
func (c *CategoryController) GetBaixingByProperties(id uint64) interface{} {
	pvalue := c.Ctx.URLParamDefault("pvalue", "")
	name := c.Ctx.URLParamDefault("name", "")
	if properties, err := cat.NewCatService().GetBaixingProperties(id, pvalue, name); err != nil {
		return utils.NewApiOkWrapper(properties)
	} else {
		return utils.NewApiOkWrapper(properties)
	}
}

// Category godoc
// @Summary 检查分类是否ok
// @Description 只检查已经开通的平台，如果没有对应的数据，需要提示用户。
// @Tags common
// @Produce  json
// @Param id query int true "分类id"
// @Param cid query int true "公司id"
// @Success 200 {array} string
// @Security ApiKeyAuth
// @Router /v2/category/check [get]
func (c *CategoryController) GetCheck() interface{} {
	id := c.Ctx.URLParamIntDefault("id", 0)
	cid := c.Ctx.URLParamIntDefault("cid", 0)
	if id == 0 || cid == 0 {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  "参数错误",
		}.Response(c.Ctx, true)
	}
	pf, _ := services.NewUserPlatformService().GetAutoPostedPlatForms(uint64(cid))
	var invalid []string
	for _, form := range pf {
		if _, e := cat.NewCatService().GetMapping(uint64(id), form); e != nil {
			invalid = append(invalid, models.PlatFormName(form))
		}
	}
	return utils.NewApiOkWrapper(invalid)
}
