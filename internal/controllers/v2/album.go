package v2

import (
	"encoding/json"
	"errors"
	"fmt"
	"git.paihang8.com/lib/goutils/sites/fs"
	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/controllers"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/db"
	"gopkg.in/go-playground/validator.v9"
	"io/ioutil"
	"mime/multipart"
	"strconv"
	"strings"
	"sync"
)

type AlbumController struct {
	Ctx       iris.Context
	Svc       services.AlbumServicer
	loginInfo controllers.LoginInfo
}

func HandleAlbum(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	c := new(AlbumController)
	c.Svc = services.NewAlbumService(db.Instance().Get())
	app.Handle(c)
}

// Album godoc
// @Summary 创建相册
// @Description 创建相册
// @Tags client
// @Accept  json
// @Produce  json
// @Param body body schemas.Album true "Album"
// @Success 200 {object} schemas.AlbumResponse
// @Failure 400 {object} configs.ApiError
// @Failure 401 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /v2/album [post]
func (c *AlbumController) Post() interface{} {
	var validate *validator.Validate
	validate = validator.New()
	var album schemas.Album
	//validate.RegisterStructValidation(UserStructLevelValidation, Album{})
	if err := c.Ctx.ReadJSON(&album); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}

	if err := validate.Struct(album); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	u := controllers.RetriveLoginInfo(c.Ctx)
	a, err := c.Svc.Create(album.Name, album.IsOpen, album.Description, u.CompanyId)
	if err != nil {
		return utils.ApiError{
			Code: utils.ErrCallApiError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	return utils.NewApiOkWrapper(schemas.AlbumResponse{Albumid: strconv.Itoa(int(a.ID))})
}

// Album godoc
// @Summary 修改相册
// @Description 修改相册
// @Tags client
// @Accept  json
// @Produce  json
// @Param id path int true "id"
// @Param body body schemas.Album true "album"
// @Success 200 {object} models.Album
// @Failure 400 {object} configs.ApiError
// @Failure 401 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /v2/album/{id} [put]
func (c *AlbumController) PutBy(id int) interface{} {
	var validate *validator.Validate
	validate = validator.New()
	var album schemas.Album
	//validate.RegisterStructValidation(UserStructLevelValidation, Album{})
	if err := c.Ctx.ReadJSON(&album); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}

	if err := validate.Struct(album); err != nil {
		if _, ok := err.(*validator.InvalidValidationError); ok {
			return utils.ApiError{
				Code: utils.ErrInputParamError.Error(),
				Msg:  err.Error(),
			}.Response(c.Ctx)
		}
		return nil
	}
	m := map[string]interface{}{}
	_ = c.Ctx.ReadJSON(&m)
	if _, ok := m["is_open"]; ok {
		m["is_open"], _ = strconv.ParseFloat(m["is_open"].(string), 64)
	}
	if a, err := c.Svc.Update(c.loginInfo.CompanyId, uint64(id), m); err != nil {
		return utils.ApiError{
			Code: utils.ErrCallApiError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(a)
	}
}

// Album godoc
// @Summary 获取相册列表
// @Description 获取当前用户的相册列表
// @Tags client
// @Produce  json
// @Success 200 {array} models.Album
// @Failure 400 {object} configs.ApiError
// @Failure 401 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /v2/album/ [get]
func (c *AlbumController) Get() interface{} {
	u := controllers.RetriveLoginInfo(c.Ctx)
	if data, err := c.Svc.ListAlbum(u.CompanyId, 0); err != nil {
		return utils.ApiError{
			Code: utils.ErrCallApiError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(data)
	}
}

// Album godoc
// @Summary 删除相册
// @Description 删除相册，干4件事。1是把相册里的图片转移到默认相册。 2. 更新默认相册属性，增加图片数和文件总大小。3 更新相册统计表中的相册数. 4是删相册记录
// @Tags client
// @Produce  json
// @Param id path int true "id"
// @Success 200 {object} configs.ApiError
// @Failure 400 {object} configs.ApiError
// @Failure 401 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /v2/album/{id} [delete]
func (c *AlbumController) DeleteBy(id uint64) interface{} {

	if err := services.NewAlbumService(db.Instance().Get()).Delete(c.loginInfo.CompanyId, id); err != nil {
		return utils.ApiError{
			Code: utils.ErrCallApiError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(nil)
	}
}

// Album godoc
// @Summary 上传文件
// @Description 支持JPG/JPEG/BMP/GIF/PNG两种格式
// @Tags client
// @Accept  multipart/form-data
// @Produce  json
// @Param id path int true "id"
// @Param   file formData file true  "jpg,jpeg,bmp, gif, png file"
// @Param   purpose query int false "上传目的，默认0-上传到相册。1-公司营业执照。2-八方资源企业证明。3-爱采购营业执照。4-爱采购合同文件 5-身份证正面 6-手持身份证"
// @Success 200 {array} schemas.UploadResponse
// @Security ApiKeyAuth
// @Router /v2/album/upload/{id} [post]
func (c *AlbumController) PostUploadBy(id int) interface{} {
	purpose := Purpose(c.Ctx.URLParamIntDefault("purpose", 0))
	u := controllers.RetriveLoginInfo(c.Ctx)
	err := c.Ctx.Request().ParseMultipartForm(1024 * 1024 * 100)
	if err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	if c.Ctx.Request().MultipartForm != nil {
		if fhs := c.Ctx.Request().MultipartForm.File; fhs != nil {
			if fileHeaders, ok := fhs["file"]; ok {
				for _, header := range fileHeaders {
					paths := strings.Split(header.Filename, ".")
					ext := strings.ToLower(paths[len(paths)-1])
					if err := purpose.Validate(ext); err != nil {
						return utils.ApiError{
							Code: utils.ErrInputParamError.Error(),
							Msg:  err.Error(),
						}.Response(c.Ctx)
					}
				}
				var wg sync.WaitGroup
				wg.Add(len(fileHeaders))
				m := make(map[string]schemas.UploadResponse)
				for _, header := range fileHeaders {
					go func(header *multipart.FileHeader) {
						defer wg.Done()
						var disableCompress bool
						if id == 0 {
							disableCompress = true
						}
						uri := utils.ApiConfig.Upload.Url + "/file/upload"
						if purpose == PurposeAicaigouContract {
							uri = utils.ApiConfig.Upload.Url + "/pdf/upload"
						}
						r, err := controllers.PostFileByHeader("file", header, map[string]string{
							"token":            fs.GenToken(utils.ApiConfig.Upload.Token),
							"path":             fmt.Sprintf("%d/%d", u.CompanyId, id),
							"disable_compress": fmt.Sprintf("%v", disableCompress),
						}, uri)
						if err != nil {
							m[header.Filename] = schemas.UploadResponse{
								Msg: err.Error(),
							}
							return
						}
						defer r.Body.Close()
						if b, err := ioutil.ReadAll(r.Body); err != nil {
							m[header.Filename] = schemas.UploadResponse{
								Msg: err.Error(),
							}
						} else {
							var res schemas.UploadApiResponse
							if err := json.Unmarshal(b, &res); err != nil {
								m[header.Filename] = schemas.UploadResponse{
									Msg: err.Error(),
								}
							} else {
								if res.Error != "" {
									m[header.Filename] = schemas.UploadResponse{
										Msg: res.Error,
									}
								} else {
									if purpose != PurposeNormal {
										m[header.Filename] = res.Data
									} else {
										if err := services.NewImageService().AddToAlbum(&models.Image{
											Name:      header.Filename,
											Url:       res.Data.Url,
											Size:      res.Data.Size,
											Hash:      res.Data.Etag,
											CompanyId: u.CompanyId,
											AlbumId:   uint64(id),
										}, uint64(id)); err != nil {
											m[header.Filename] = schemas.UploadResponse{
												Msg: err.Error(),
											}
											if errors.Is(err, utils.ErrDupImage) {
												res.Data.Msg = err.Error()
												m[header.Filename] = res.Data
											}
										} else {
											m[header.Filename] = res.Data
										}
									}

								}

							}
						}
					}(header)
				}
				wg.Wait()
				var res []schemas.UploadResponse
				for _, header := range fileHeaders {
					res = append(res, m[header.Filename])
				}
				return utils.NewApiOkWrapper(res)
			}
		}
	}
	return utils.ApiError{
		Code: utils.ErrInputParamError.Error(),
		Msg:  "no file found",
	}.Response(c.Ctx)
}

// Album godoc
// @Summary 删除照片
// @Description 删除照片，其实是改状态
// @Tags client
// @Produce  json
// @Param body body schemas.DelImages true "DelImages"
// @Success 200 {object} configs.ApiError
// @Failure 400 {object} configs.ApiError
// @Failure 401 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /v2/album/images/ [delete]
func (c *AlbumController) DeleteImages() interface{} {
	var input schemas.DelImages
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	if err := services.NewImageService().Delete(c.loginInfo.CompanyId, input.From, input.Ids); err != nil {
		return utils.ApiError{
			Code: utils.ErrCallApiError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(nil)
	}
}

// Album godoc
// @Summary 移动图片
// @Description 把图片从一个相册移动到另一个相册
// @Tags client
// @Produce  json
// @Param id path int true "id"
// @Param body body schemas.MoveImages true "MoveImages"
// @Success 200 {object} configs.ApiError
// @Failure 400 {object} configs.ApiError
// @Failure 401 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /v2/album/images/moveto/{id} [post]
func (c *AlbumController) PostImagesMovetoBy(id uint64) interface{} {
	var input schemas.MoveImages
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	if err := services.NewImageService().Moveto(c.loginInfo.CompanyId, input.Ids, input.From, id); err != nil {
		return utils.ApiError{
			Code: utils.ErrCallApiError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(nil)
	}
	return utils.NewApiOkWrapper(nil)
}

// Album godoc
// @Summary 修改图片
// @Description 修改图片信息，只能改名字
// @Tags client
// @Accept  json
// @Produce  json
// @Param id path int true "id"
// @Param body body models.Image true "album"
// @Success 200 {object} models.Image
// @Failure 400 {object} configs.ApiError
// @Failure 401 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /v2/album/images/{id} [put]
func (c *AlbumController) PutImagesBy(id int) interface{} {
	var validate *validator.Validate
	validate = validator.New()
	var img models.Image
	//validate.RegisterStructValidation(UserStructLevelValidation, Album{})
	if err := c.Ctx.ReadJSON(&img); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}

	if err := validate.Struct(img); err != nil {
		if _, ok := err.(*validator.InvalidValidationError); ok {
			return utils.ApiError{
				Code: utils.ErrInputParamError.Error(),
				Msg:  err.Error(),
			}.Response(c.Ctx)
		}
		return nil
	}
	m := map[string]interface{}{}
	_ = c.Ctx.ReadJSON(&m)
	if a, err := services.NewImageService().Update(c.loginInfo.CompanyId, uint64(id), m); err != nil {
		return utils.ApiError{
			Code: utils.ErrCallApiError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(a)
	}
}

// Album godoc
// @Summary 获取相册内图片列表
// @Description 获取相册内图片列表
// @Tags client
// @Accept  json
// @Produce  json
// @Param id path int true "id"
// @Param limit query int false "默认20"
// @Param offset query int false "默认0"
// @Success 200 {array} models.Image
// @Security ApiKeyAuth
// @Router /v2/album/{id} [get]
func (c *AlbumController) GetBy(albumId uint64) interface{} {
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	if albumId == 0 {
		if defaut, err := services.NewAlbumService(db.Instance().Get()).DefaultAlbum(c.loginInfo.CompanyId); err != nil {
			return utils.ApiError{
				Code: utils.ErrCallApiError.Error(),
				Msg:  err.Error(),
			}.Response(c.Ctx)
		} else {
			albumId = defaut.ID
		}
	}
	if data, err := services.NewImageService().ListOfAlbum(limit, offset, albumId); err != nil {
		return utils.ApiError{
			Code: utils.ErrCallApiError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(data)
	}
}

// Album godoc
// @Summary 搜索图片
// @Description 根据图片名字做模糊索索
// @Tags client
// @Accept  json
// @Produce  json
// @Param id path int true "id"
// @Param kw query string true "关键词"
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Param limit query int false "默认20"
// @Param offset query int false "默认0"
// @Success 200 {array} models.Image
// @Security ApiKeyAuth
// @Router /v2/album/{id}/images/ [get]
func (c *AlbumController) GetByImages(albumId uint64) interface{} {
	kw := c.Ctx.URLParam("kw")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	if data, err := services.NewImageService().Search(c.loginInfo.CompanyId, albumId, kw, limit, offset); err != nil {
		return utils.ApiError{
			Code: utils.ErrCallApiError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(data)
	}
}

// Album godoc
// @Summary 获取相册统计数据
// @Description 相册总数， 图片总数， 购买的图片数， 赠送的图片数， 已上传图片数，可上传图片数。
// 已上传图片数即相册里的图片数，包括删除的图片。
// @Tags client
// @Produce  json
// @Param id path int true "id"
// @Success 200 {object} models.UserStat
// @Failure 400 {object} configs.ApiError
// @Failure 401 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /v2/album/stat/ [get]
func (c *AlbumController) GetStat() interface{} {
	if data, err := services.NewUserStatService().GetStatByCompanyId(c.loginInfo.CompanyId); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(data)
	}
}

func (c *AlbumController) BeginRequest(ctx context.Context) {
	c.loginInfo = controllers.RetriveLoginInfo(ctx)
}

func (c *AlbumController) EndRequest(ctx context.Context) {
}
