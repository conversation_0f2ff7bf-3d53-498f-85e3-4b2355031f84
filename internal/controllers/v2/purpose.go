package v2

import (
	"errors"
)

type Purpose int

const (
	PurposeNormal           = 0
	PurposeCompanyLicense   = 1
	PurposeBafang           = 2
	PurposeAicaigouLicense  = 3
	PurposeAicaigouContract = 4
	PurposeIdCardFront      = 5
	PurposeIdCardHand       = 6
)

func (p Purpose) Validate(ext string) error {
	switch p {
	case PurposeNormal:
		fallthrough
	case PurposeBafang:
		fallthrough
	case PurposeCompanyLicense:
		if ext != "png" && ext != "jpg" && ext != "jpeg" && ext != "bmp" && ext != "gif" {
			return errors.New("文件类型不支持")
		}
	case PurposeIdCardHand:
		fallthrough
	case PurposeIdCardFront:
		if ext != "png" && ext != "jpg" && ext != "jpeg" && ext != "bmp" && ext != "gif" {
			return errors.New("文件类型不支持")
		}
	case PurposeAicaigouLicense:
		if ext != "jpg" {
			return errors.New("文件类型不支持")
		}
	case PurposeAicaigouContract:
		if ext != "pdf" {
			return errors.New("文件类型不支持")
		}
	}
	return nil
}
