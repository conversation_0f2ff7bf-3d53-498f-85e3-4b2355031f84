package controllers

import (
	_ "gitlab.com/all_publish/api/internal/business/eventtrigger"
	"gitlab.com/all_publish/api/internal/business/merchanter"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services"
	"strconv"
)

func OnUserLogin(u *models.User, mClient models.Merchanter) {
	if u.Company.GetAreaID() == 0 {
		if c, err := mClient.(*merchanter.Hy88Merchant).GetCompanyInfo(strconv.Itoa(u.OemId)); err == nil {
			//u.Company.Cate = c.Cate
			u.Company.AreaIds = c.AreaIds
			services.NewCompanyService().Save(&u.Company)
		}
	}
	for k, config := range u.PostsMap {
		form := models.PlatFormByDomain(k)
		if tr, ok := models.GetTrigger(form); ok {
			tr.OnUserLogin(u, config, mClient)
		}
	}

}

func OnUserCreated(u *models.User, mClient models.Merchanter) {
	for k, config := range u.PostsMap {
		form := models.PlatFormByDomain(k)
		if tr, ok := models.GetTrigger(form); ok {
			tr.OnFirstLogin(u, config, mClient)
		}
	}
}
