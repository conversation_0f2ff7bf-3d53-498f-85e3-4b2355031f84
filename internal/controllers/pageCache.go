package controllers

import (
	"errors"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
	"gopkg.in/go-playground/validator.v9"
	"gorm.io/gorm"
)

type PageCacheController struct {
	Ctx       context.Context
	loginInfo LoginInfo
}

func HandlePageCache(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(PageCacheController))
}

// HandlePage godoc
// @Summary  更新缓存
// @Description 更新缓存
// @Tags client
// @Accept  json
// @Produce  json
// @Param productId path int true "productId"
// @Param body body models.PageCache true "PageCache"
// @Success 200 {object} models.Product
// @Security ApiKeyAuth
// @Router /pagecache/{productId} [put]
func (c *PageCacheController) PutBy(productId uint64) interface{} {
	var product, modifyProduct models.PageCache
	if err := c.Ctx.ReadJSON(&modifyProduct); err != nil {
		return utils.ApiError{Code: utils.ErrAddProductFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	var validate = validator.New()
	if err := validate.Struct(modifyProduct); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	var err error
	if err = db.Instance().Get().First(&product, productId).Error; errors.Is(err, gorm.ErrRecordNotFound) {
		err = db.Instance().Get().Save(modifyProduct).Error
	} else {
		product.Cache = modifyProduct.Cache
		err = db.Instance().Get().Save(product).Error
	}
	if err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}

	return utils.NewApiOkWrapper(nil)
}

// HandlePage godoc
// @Summary 获取缓存
// @Description 获取缓存
// @Tags client
// @Produce  json
// @Param productId path int true "productId"
// @Success 200 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /pagecache/{productId} [get]
func (c *PageCacheController) GetBy(productId int) interface{} {
	var pageCache models.PageCache
	if err := db.Instance().Get().First(&pageCache, productId).Error; errors.Is(err, gorm.ErrRecordNotFound) {
		return utils.NewApiOkWrapper(map[string]interface{}{})
	} else {
		return utils.NewApiOkWrapper(pageCache.Cache)
	}
}

func (c *PageCacheController) BeginRequest(ctx context.Context) {
	c.loginInfo = RetriveLoginInfo(ctx)
}

func (c *PageCacheController) EndRequest(ctx context.Context) {
}
