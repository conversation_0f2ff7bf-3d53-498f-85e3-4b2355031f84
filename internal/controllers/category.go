package controllers

import (
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/business/merchanter"
	"gitlab.com/all_publish/api/internal/models"
)

type CategoryController struct {
	Ctx context.Context
}

func HandleCategory(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(CategoryController))
}

// Category godoc
// @Summary 获取分类详情
// @Description
// @Tags deprecated
// @Produce  json
// @Param id path int true "int"
// @Success 200 {object} publisher.CategoryDetail
// @Security ApiKeyAuth
// @Router /category/{id} [get]
func (c *CategoryController) Get() interface{} {
	id, _ := c.Ctx.Params().GetInt("id")
	mClient, _ := models.GetMerchant(models.PlatformHy88)
	if detail, err := mClient.(*merchanter.Hy88Merchant).GetCatByID(id); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx, true)
	} else {
		return utils.NewApiOkWrapper(detail)
	}
}

// Category godoc
// @Summary 获取子分类
// @Description
// @Tags deprecated
// @Produce  json
// @Param id path int true "id"
// @Success 200 {array} publisher.Category
// @Security ApiKeyAuth
// @Router /category/{id}/subs [get]
func (c *CategoryController) GetSubs() interface{} {
	id, _ := c.Ctx.Params().GetInt("id")
	mClient, _ := models.GetMerchant(models.PlatformHy88)
	if subs, err := mClient.(*merchanter.Hy88Merchant).GetAllCatsByPID(id); err != nil {
		subs = []publisher.Category{}
		return utils.NewApiOkWrapper(subs)
	} else {
		return utils.NewApiOkWrapper(subs)
	}
}

// Category godoc
// @Summary 获取属性列表
// @Description input->(string),textarea->长文本,radio->单选按钮,checkbox->多选按钮,select->下拉框
// @Tags deprecated
// @Produce  json
// @Param id path int true "id"
// @Success 200 {array} publisher.Property
// @Security ApiKeyAuth
// @Router /category/{id}/properties [get]
func (c *CategoryController) GetProperties() interface{} {
	id, _ := c.Ctx.Params().GetInt("id")
	mClient, _ := models.GetMerchant(models.PlatformHy88)
	if properties, err := mClient.(*merchanter.Hy88Merchant).GetCatProperties(id); err != nil {
		properties = []publisher.Property{}
		return utils.NewApiOkWrapper(properties)
	} else {
		return utils.NewApiOkWrapper(properties)
	}
}
