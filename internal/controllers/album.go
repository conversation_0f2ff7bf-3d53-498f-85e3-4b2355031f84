package controllers

import (
	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/business/merchanter"
	"gitlab.com/all_publish/api/internal/schemas"
	"gopkg.in/go-playground/validator.v9"
	"strconv"
	"strings"
)

type AlbumController struct {
	Ctx iris.Context
}

func HandleAlbum(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(AlbumController))
}

func (c *AlbumController) Post() interface{} {
	var validate *validator.Validate
	validate = validator.New()
	var album schemas.Album
	//validate.RegisterStructValidation(UserStructLevelValidation, Album{})
	if err := c.Ctx.ReadJSON(&album); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}

	if err := validate.Struct(album); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}

	client := RetriveBindedMerchantClient(c.Ctx)
	response, err := client.(*merchanter.Hy88Merchant).CreateAlbum(album)
	if err != nil {
		return utils.ApiError{
			Code: utils.ErrCallApiError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	return utils.NewApiOkWrapper(response)
}

func (c *AlbumController) PutBy(id int) interface{} {
	var validate *validator.Validate
	validate = validator.New()
	var album schemas.Album
	//validate.RegisterStructValidation(UserStructLevelValidation, Album{})
	if err := c.Ctx.ReadJSON(&album); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}

	if err := validate.Struct(album); err != nil {
		if _, ok := err.(*validator.InvalidValidationError); ok {
			return utils.ApiError{
				Code: utils.ErrInputParamError.Error(),
				Msg:  err.Error(),
			}.Response(c.Ctx)
		}
		return nil
	}

	client := RetriveBindedMerchantClient(c.Ctx)
	if err := client.(*merchanter.Hy88Merchant).ModifyAlbum(strconv.Itoa(id), album); err != nil {
		return utils.ApiError{
			Code: utils.ErrCallApiError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	return utils.NewApiOkWrapper(nil)
}

func (c *AlbumController) Get() interface{} {
	client := RetriveBindedMerchantClient(c.Ctx)
	if data, err := client.(*merchanter.Hy88Merchant).GetAlbums(); err != nil {
		return utils.ApiError{
			Code: utils.ErrCallApiError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(data)
	}
}

func (c *AlbumController) GetBy(albumId int) interface{} {
	client := RetriveBindedMerchantClient(c.Ctx)
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	if data, err := client.(*merchanter.Hy88Merchant).GetImagesOfAlbum(strconv.Itoa(albumId), limit, offset); err != nil {
		return utils.ApiError{
			Code: utils.ErrCallApiError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(data)
	}
}

func (c *AlbumController) PostUploadBy(id int) interface{} {
	c.Ctx.Request().ParseMultipartForm(1024 * 1024 * 100)
	if c.Ctx.Request().MultipartForm != nil {
		if fhs := c.Ctx.Request().MultipartForm.File; fhs != nil {
			if fileHeaders, ok := fhs["file"]; ok {
				for _, header := range fileHeaders {
					paths := strings.Split(header.Filename, ".")
					ext := strings.ToLower(paths[len(paths)-1])
					if ext != "png" && ext != "jpg" && ext != "jpeg" {
						return utils.ApiError{
							Code: utils.ErrInputParamError.Error(),
							Msg:  "file type error",
						}.Response(c.Ctx)
					}
				}
				client := RetriveBindedMerchantClient(c.Ctx)
				if urls, err := client.(*merchanter.Hy88Merchant).UploadImagesToAlbum(strconv.Itoa(id), fileHeaders); err != nil {
					return utils.ApiError{
						Code: utils.ErrCallApiError.Error(),
						Msg:  err.Error(),
					}.Response(c.Ctx)
				} else {
					return urls
				}
			}
		}
	}
	return utils.ApiError{
		Code: utils.ErrInputParamError.Error(),
		Msg:  "no file found",
	}.Response(c.Ctx)
}
