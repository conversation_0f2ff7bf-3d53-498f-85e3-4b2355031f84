package controllers

import (
	_ "git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/business/merchanter"
)

type SeekController struct {
	Ctx context.Context
}

func HandleSeek(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(SeekController))
}

// Seek godoc
// @Summary  获取收录统计
// @Description 获取当天用户的收录统计
// @Tags client
// @Produce  json
// @Success 200 {object} publisher.SeekCountData
// @Security ApiKeyAuth
// @Router /seeks/count [get]
func (c *SeekController) GetCount() interface{} {
	mClient := RetriveBindedMerchantClient(c.Ctx)
	if data, err := mClient.(*merchanter.Hy88Merchant).GetSeekCount(); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx, true)
	} else {
		return utils.NewApiOkWrapper(data)
	}
}

// Seek godoc
// @Summary 获取收录列表
// @Description 获取收录排名列表
// @Description query string parmas:
// @Description eg: pc_baidu:百度电脑端; m_baidu:百度移动; pc_sogou:搜狗电脑端;m_sogou:搜狗移动;m_haosou:好搜移动;m_toutiao:头条移动
// @Tags client
// @Produce  json
// @Param eg query string true "string enums" Enums(pc_baidu,m_baidu,pc_sogou,m_sogou,m_haosou,m_toutiao )
// @Param page query int true "int"
// @Success 200 {object} publisher.RanksData
// @Security ApiKeyAuth
// @Router /seeks [get]
func (c *SeekController) Get() interface{} {
	page := c.Ctx.URLParamIntDefault("page", 1)
	eg := c.Ctx.URLParamDefault("eg", "m_baidu")
	mClient := RetriveBindedMerchantClient(c.Ctx)
	if data, err := mClient.(*merchanter.Hy88Merchant).GetSeeks(eg, page); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx, true)
	} else {
		return utils.NewApiOkWrapper(data)
	}
}
