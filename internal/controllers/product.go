package controllers

import (
	"strconv"

	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	"gitlab.com/all_publish/api/configs"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/publisher"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/cat"
	ps "gitlab.com/all_publish/api/internal/services/product"
	"gitlab.com/all_publish/api/internal/services/service"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gopkg.in/go-playground/validator.v9"
)

type ProductController struct {
	Ctx       context.Context
	loginInfo LoginInfo
}

func HandleProduct(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(ProductController))
}

// Post Product godoc
// @Summary 添加产品
// @Description 添加产品
// @Tags client
// @Accept  json
// @Produce  json
// @Param body models.Product true "Product"
// @Success 200 {object} models.Product
// @Security ApiKeyAuth
// @Router /product [post]
func (c *ProductController) Post() interface{} {
	var product models.Product
	if err := c.Ctx.ReadJSON(&product); err != nil {
		return configs.ApiError{Code: configs.ErrAddProductFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	m := map[string]interface{}{}
	c.Ctx.ReadJSON(&m)
	var validate = validator.New()
	if err := validate.Struct(product); err != nil {
		return configs.ApiError{
			Code: configs.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	user, _ := services.NewUserService().GetUserById(c.loginInfo.UserId)
	if int(ps.NewProductService().CountByCompanyId(c.loginInfo.CompanyId)) >= user.MaxProducts {
		return configs.ApiError{Code: configs.ErrAddProductFailed.Error(),
			Msg: "产品已经达到上限" + strconv.Itoa(user.MaxProducts)}.Response(c.Ctx)
	}
	delete(m, "id")
	product.ID = 0
	product.Status = dbtypes.ProductStatusDraft
	product.CompanyID = c.loginInfo.CompanyId
	product.CreatedAt = dbtypes.SHNow()
	product.UpdatedAt = dbtypes.SHNow()
	product.Active = 1
	for k := range m {
		product.ModifyFields = append(product.ModifyFields, k)
	}
	if err := ps.NewProductService().Create(&product); err != nil {
		return configs.ApiError{Code: configs.ErrAddProductFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	return configs.NewApiOkWrapper(product)
}

// PutBy Product godoc
// @Summary 修改产品内容
// @Description 修改产品描述， 只上传需要修改的部分
// @Tags client
// @Accept  json
// @Produce  json
// @Param productId path int true "productId"
// @Param body models.Product true "Product"
// @Success 200 {object} models.Product
// @Security ApiKeyAuth
// @Router /product/{productId} [put]
func (c *ProductController) PutBy(productId uint64) interface{} {
	var product, modifyProduct models.Product
	if err := c.Ctx.ReadJSON(&modifyProduct); err != nil {
		return utils.ApiError{Code: utils.ErrAddProductFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	var validate = validator.New()
	if err := validate.Struct(modifyProduct); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}

	if err := db.Instance().Get().Find(&product, productId).Error; err != nil {
		return utils.ApiError{Code: utils.ErrAddProductFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	lastAuditRes2 := product.AuditRes2
	for k, v := range product.LastAuditRes2 {
		if _, ok := lastAuditRes2[k]; !ok {
			lastAuditRes2[k] = v
		}
	}
	m := map[string]interface{}{}
	_ = c.Ctx.ReadJSON(&m)
	delete(m, "id")
	if err := dbtypes.UpdateModelFromMap(&product, m); err != nil {
		return utils.ApiError{Code: utils.ErrUpdateModelError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	product.Status = dbtypes.ProductStatusDraft
	product.UpdatedAt = dbtypes.SHNow()
	product.LastAuditRes2 = lastAuditRes2

	product.AuditRes = nil
	product.ModifyFields = nil
	for k := range m {
		product.ModifyFields = append(product.ModifyFields, k)
	}
	if err := dbtypes.UpdateModelFromMap(&product, m); err != nil {
		return configs.ErrUpdateModelError
	}
	m["status"] = dbtypes.ProductStatusDraft
	m["updated_at"] = nil
	m["audit_res"] = nil
	m["last_audit_res2"] = product.LastAuditRes2
	m["modify_fields"] = nil
	if ps.NewProductService().NameExist(product.Name, c.loginInfo.CompanyId, product.ID) {
		return configs.ApiError{Code: configs.ErrAddProductFailed.Error(), Msg: "产品名称与其他产品重复，请修改。"}.Response(c.Ctx)
	}
	if err := ps.NewProductService().Updates(&product, m); err != nil {
		return utils.ApiError{Code: utils.ErrAddProductFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	return utils.NewApiOkWrapper(product)
}

// GetOptBy Product godoc
// @Summary 操作产品
// @Description 提交审核、推广、暂停推广等操作
// @Description query string parmas:
// @Description opt: 1 提交审核 2 开始推广 3 取消推广 4 删除
// @Tags client
// @Produce  json
// @Param opt query int true "int enum Enum(1,2,3,4)"
// @Success 200 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /product/opt/{productId} [get]
func (c *ProductController) GetOptBy(productId int) interface{} {
	opt, err := c.Ctx.URLParamInt("opt")
	if err != nil {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	if product, err := ps.NewProductService().UpdateStatusByOpt(productId, opt); err != nil {
		return err.Response(c.Ctx)
	} else {
		if opt == 4 {
			titleMaker := publisher.NewSimpleTitleMaker(product)
			titleMaker.ResetTitleQueue()
			titleMaker.CloseDB()
			contentMaker := publisher.NewContentMaker(product)
			contentMaker.ResetTxtCache()
			contentMaker.CloseDB()
		}
		return utils.NewApiOkWrapper(nil)
	}
}

// GetBy Product godoc
// @Summary 获取产品详情
// @Description 获取产品详情, 只能获取当前用户的
// @Tags client
// @Produce  json
// @Param productId path int true "productId"
// @Success 200 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /product/{productId} [get]
func (c *ProductController) GetBy(productId int) interface{} {
	if product, err := ps.NewProductService().Get(uint64(productId), true); err != nil {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(c.Ctx)
	} else {
		if product.CompanyID != c.loginInfo.CompanyId {
			return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: "非法请求"}.Response(c.Ctx)
		}
		product.AreaNames = services.Area.FindNames(product.AreaIds)
		product.CateNames, _ = cat.NewCatService().FindNames(product.Cate)
		if ps, err := services.NewUserPlatformService().GetAutoPostedPlatForms(c.loginInfo.CompanyId); err == nil {
			product.SetPlatformsByforms(ps)
		}
		return utils.NewApiOkWrapper(product)
	}
}

// GetQuery Product godoc
// @Summary 获取产品列表
// @Description 获取产品列表
// @Description 0.2.57 开始字段精简
// @Description 后端分页
// @Tags client
// @Produce  json
// @Param sortby query string  false "string enums" Enums(created_at,updated_at)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Param count query int true "0：不返回总数，1：返回总数" default(0)
// @Success 200 {array} schemas.Product
// @Security ApiKeyAuth
// @Router /product/query [get]
func (c *ProductController) GetQuery() interface{} {
	sortby := c.Ctx.URLParamDefault("sortby", "created_at")
	order := c.Ctx.URLParamDefault("order", "desc")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	count := c.Ctx.URLParamIntDefault("count", 0)

	if cnt, products, err := ps.NewProductService().GetProductsOfCompanyId(c.loginInfo.CompanyId, sortby, order, limit, offset); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		versions := c.Ctx.GetHeader("version")
		if versions != "" {
			if result, err := service.VersionCompare(versions, 0, 2, 57); err == nil {
				if result > 0 {

					if count == 0 {
						var ret = []schemas.Product{}
						ps, _ := services.NewUserPlatformService().GetAutoPostedPlatForms(c.loginInfo.CompanyId)
						for _, v := range products {
							v.SetPlatformsByforms(ps)
							ret = append(ret, schemas.Product{
								Base:      v.Base,
								ID:        v.ID,
								Name:      v.Name,
								Brand:     v.Brand,
								Status:    v.Status,
								AuditRes2: v.AuditRes2,
								Platforms: v.Platforms,
								Mode:      v.Mode,
							})
						}
						return utils.NewApiOkWrapper(ret)
					} else {
						var ret = schemas.PageResult{Total: cnt}
						ps, _ := services.NewUserPlatformService().GetAutoPostedPlatForms(c.loginInfo.CompanyId)
						for _, v := range products {
							v.SetPlatformsByforms(ps)
							ret.Items = append(ret.Items, schemas.Product{
								Base:      v.Base,
								ID:        v.ID,
								Name:      v.Name,
								Brand:     v.Brand,
								Status:    v.Status,
								AuditRes2: v.AuditRes2,
								Platforms: v.Platforms,
								Mode:      v.Mode,
							})
						}
						return utils.NewApiOkWrapper(ret)
					}

				}
			}
		}

		return utils.NewApiOkWrapper(products)
	}
}

// GetStat Product godoc
// @Summary 获取产品统计信息
// @Description 获取产品统计信息
// @Tags client
// @Produce  json
// @Success 200 {object} 	schemas.StatusCount
// @Security ApiKeyAuth
// @Router /product/stat [get]
func (c *ProductController) GetStat() interface{} {
	if data, err := ps.NewProductService().CountByStatusOfCompany(c.loginInfo.CompanyId); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(data)
	}
}

func (c *ProductController) BeginRequest(ctx context.Context) {
	c.loginInfo = RetriveLoginInfo(ctx)
}

func (c *ProductController) EndRequest(ctx context.Context) {
}
