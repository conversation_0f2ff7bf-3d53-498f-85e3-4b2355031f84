package controllers

import (
	"fmt"

	"git.paihang8.com/lib/goutils/sites/baidu"
	publisher2 "git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/publisher"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
)

var wxyy = baidu.NewWxyy()

type ToolsController struct {
	Ctx       context.Context
	loginInfo LoginInfo
}

func HandleTools(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(ToolsController))
}

// Tools godoc
// @Summary 检查重复度
// @Description 最后一条和前面的一一对比
// @Tags common
// @Accept  json
// @Produce  json
// @Param body body schemas.Similar true "Similar"
// @Success 200 {object} configs.ApiOkWrapper
// @Success 400 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /tools/similar [post]
func (c *ToolsController) PostSimilar() interface{} {
	var similar schemas.Similar
	if err := c.Ctx.ReadJSON(&similar); err != nil {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	target := similar.Data[similar.Index]
	for i := 0; i < len(similar.Data); i++ {
		if i == similar.Index {
			continue
		}
		if v := publisher.NewCosineDetecor(similar.Data[i], target, publisher.WithWord, publisher.WithWordSize(2)).Detect(); v > publisher.SimilarProductM {
			return utils.ApiError{
				Code: utils.ErrInputParamError.Error(),
				Msg:  fmt.Sprintf("第%d段文本和第%d段文本相似度过高", similar.Index+1, i+1),
			}
		}
	}
	return utils.NewApiOkWrapper(nil)
}

// Tools godoc
// @Summary 数据挖掘
// @Description 返回推荐的关键词
// @Tags common
// @Accept  json
// @Produce  json
// @Param kw query string true "string"
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Success 200 {object} configs.ApiOkWrapper
// @Success 400 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /tools/dig/keywords [get]
func (c *ToolsController) GetDigKeywords() interface{} {
	keyword := c.Ctx.URLParam("kw")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	if data, err := services.GetKeywords(c.loginInfo.CompanyId, keyword, limit, offset); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
			Data: toWebUrl("https://fafa.huangye88.com/dig/", c.Ctx),
		}
	} else {
		data.ChargeUrl = toWebUrl("https://fafa.huangye88.com/dig/", c.Ctx)
		return utils.NewApiOkWrapper(data)
	}
}

// Tools godoc
// @Summary 数据挖掘
// @Description 返回推荐的素材
// @Tags common
// @Accept  json
// @Produce  json
// @Param kw query string true "string"
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Success 200 {object} configs.ApiOkWrapper
// @Success 400 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /tools/dig/materials [get]
func (c *ToolsController) GetDigMaterials() interface{} {
	keyword := c.Ctx.URLParam("kw")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	if data, err := services.GetMaterials(c.loginInfo.CompanyId, keyword, limit, offset); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
			Data: toWebUrl("https://fafa.huangye88.com/dig/", c.Ctx),
		}
	} else {
		data.ChargeUrl = toWebUrl("https://fafa.huangye88.com/dig/", c.Ctx)
		return utils.NewApiOkWrapper(data)
	}
}

// GetChatAsk Tools godoc
// @Summary  chatgpt ask
// @Description chatgpt ask
// @Tags common
// @Accept  json
// @Produce  json
// @Param kw query string true "string"
// @Param type query string true "int"
// @Success 200 {object} configs.ApiOkWrapper
// @Success 400 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /tools/chat/ask [get]
func (c *ToolsController) GetChatAsk() interface{} {
	keyword := c.Ctx.URLParam("kw")
	typ := c.Ctx.URLParamIntDefault("type", 0)
	switch typ {
	case models.AiTypeGpt:
		mClient := RetriveBindedMerchantClient(c.Ctx)
		api := mClient.GetApi().(publisher2.PublishApi) // publisher2.PublishApi
		if !api.Authorized() {
			if _, err := api.Authorize(utils.ApiConfig.Hy88.CHNID, utils.ApiConfig.Hy88.CHNUser, utils.ApiConfig.Hy88.CHNPassword); err != nil {
				return utils.ApiError{
					Code: utils.ErrInputParamError.Error(),
					Msg:  err.Error(),
				}.Response(c.Ctx, true)
			}
		}
		if data, err := api.Chat(publisher2.ChatMessage{
			Uid:   mClient.Account()["id"].(string),
			Title: keyword,
		}); err != nil {
			return utils.ApiError{
				Code: utils.ErrInputParamError.Error(),
				Msg:  err.Error(),
			}.Response(c.Ctx, true)
		} else {
			return utils.NewApiOkWrapper(data.Data)
		}
	case models.AiTypeWenxin:
		if res, err := wxyy.Chat(keyword); err != nil {
			return utils.ApiError{
				Code: utils.ErrInputParamError.Error(),
				Msg:  err.Error(),
			}.Response(c.Ctx, true)
		} else {
			return utils.NewApiOkWrapper(res)
		}
	case models.AiTypeDoubao, models.AiTypeDeepSeek:
		// doubao := douyin.NewDouBao()
		// if res, err := doubao.Chat(stdContext.Background(), keyword, false); err != nil {
		// 	return utils.ApiError{
		// 		Code: utils.ErrInputParamError.Error(),
		// 		Msg:  err.Error(),
		// 	}.Response(c.Ctx, true)
		// } else {
		// 	return utils.NewApiOkWrapper(res)
		// }
		mClient := RetriveBindedMerchantClient(c.Ctx)
		api := mClient.GetApi().(publisher2.PublishApi) // publisher2.PublishApi
		if !api.Authorized() {
			if _, err := api.Authorize(utils.ApiConfig.Hy88.CHNID, utils.ApiConfig.Hy88.CHNUser, utils.ApiConfig.Hy88.CHNPassword); err != nil {
				return utils.ApiError{
					Code: utils.ErrInputParamError.Error(),
					Msg:  err.Error(),
				}.Response(c.Ctx, true)
			}
		}
		aimodel := "deepseek"
		if typ == models.AiTypeDoubao {
			aimodel = "doubao"
		}
		if data, err := api.Chat(publisher2.ChatMessage{
			Uid:    mClient.Account()["id"].(string),
			Title:  keyword,
			AiMode: aimodel,
		}); err != nil {
			return utils.ApiError{
				Code: utils.ErrInputParamError.Error(),
				Msg:  err.Error(),
			}.Response(c.Ctx, true)
		} else {
			return utils.NewApiOkWrapper(data.Data)
		}
	}
	return utils.ApiError{
		Code: utils.ErrInputParamError.Error(),
		Msg:  "不支持的类型",
	}.Response(c.Ctx, true)
}

// GetChatStreamAsk Tools godoc
// @Summary  chatgpt ask
// @Description chatgpt ask
// @Tags common
// @Accept  json
// @Produce  json
// @Param kw query string true "string"
// @Param type query string true "int"
// @Success 200 {object} configs.ApiOkWrapper
// @Success 400 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /tools/chat/stream/ask [get]
func (c *ToolsController) GetChatStreamAsk() interface{} {
	return c.GetChatAsk()
	/*	keyword := c.Ctx.URLParam("kw")
		typ := c.Ctx.URLParamIntDefault("type", 0)

		if keyword != "" {
			if err := services.NewSearchRecordInfoService().Create(&models.ChatSearchRecord{
				CompanyId: c.loginInfo.CompanyId,
				Keyword:   keyword,
				AiType:    models.AiType(typ),
				Cnt:       1,
			}); err != nil {
				return utils.ApiError{
					Code: utils.ErrInputParamError.Error(),
					Msg:  err.Error(),
				}
			}
			if ch, err := chatgpt.AskWithStream(keyword, chatgpt.WithVersion("3.5")); err != nil {
				return utils.ApiError{
					Code: utils.ErrInputParamError.Error(),
					Msg:  err.Error(),
				}
			} else {
				for data := range ch {
					c.Ctx.ResponseWriter().Naive().Write([]byte(data))
					c.Ctx.ResponseWriter().Flush()
				}
				c.Ctx.ResponseWriter().Naive().Write([]byte("0\r\n\r\n"))
				return nil
			}
		} else {
			return utils.ApiError{
				Code: utils.ErrInputParamError.Error(),
				Msg:  "question empty",
			}
		}*/
}

// Histories Tools godoc
// @Summary  chat question history
// @Description chat question history
// @Tags common
// @Accept  json
// @Produce  json
// @Param type query string true "int"
// @Success 200 {object} configs.ApiOkWrapper
// @Success 400 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /tools/chat/histories [get]
func (c *ToolsController) GetChatHistories() interface{} {
	typ := c.Ctx.URLParamIntDefault("type", 0)
	if items, err := services.NewSearchRecordInfoService().Histories(c.loginInfo.CompanyId, typ); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}
	} else {
		return utils.NewApiOkWrapper(items)
	}
}

func (c *ToolsController) BeginRequest(ctx context.Context) {
	c.loginInfo = RetriveLoginInfo(ctx)
}

func (c *ToolsController) EndRequest(ctx context.Context) {
}
