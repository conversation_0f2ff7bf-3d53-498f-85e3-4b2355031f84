package controllers

import (
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	coreDb "gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gopkg.in/go-playground/validator.v9"
	"strconv"
)

type InfoController struct {
	Ctx       context.Context
	loginInfo LoginInfo
}

func HandleInfo(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(InfoController))
}

// Info godoc
// @Summary 手动发布信息
// @Description 手动发布信息
// @Tags client
// @Accept  json
// @Produce  json
// @Param body body models.Info true "Info"
// @Success 200 {array} models.Info
// @Security ApiKeyAuth
// @Router /info [post]
func (c *InfoController) Post() interface{} {
	var input schemas.PubInfo
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	var validate = validator.New()
	if err := validate.Struct(input); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	var info models.Info
	info.Status = dbtypes.InfoStatusWattingpublish
	info.CompanyID = c.loginInfo.CompanyId
	info.Title = &input.Fields.Title
	info.Word = input.Fields.Word
	info.TitlePic = input.Fields.TitlePic
	info.Price = input.Fields.Price
	info.Unit = input.Fields.Unit

	var merchants []models.Merchant
	if err := coreDb.Instance().Get().Where("company_id=? and id in ?", info.CompanyID, input.MerchantIds).Find(&merchants).Error; err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	if len(merchants) == 0 {
		return utils.ApiError{
			Code: utils.ErrPubInfoFailed.Error(),
			Msg:  "还没有创建商户，不能发布信息",
		}.Response(c.Ctx)
	}
	var infos []models.Info
	for _, m := range merchants {
		_info := info
		_info.Platform = m.PlatForm
		_info.PubRes = map[string]interface{}{
			"merchant_id": m.ID,
			"platform":    m.PlatForm,
		}
		infos = append(infos, _info)
	}
	var err error
	for _, info := range infos {
		if err = coreDb.Instance().Get().Create(&info).Error; err != nil {
			break
		}
	}
	if err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(infos)
	}

}

// Auth godoc
// @Summary 获取发布信息列表
// @Description 获取发布信息列表
// @Description status 0:所有状态 1:等待推送 2:推送成功 4:推送失败
// @Tags client
// @Accept  json
// @Produce  json
// @Param sortby query string  false "string enums" Enums(created_at,updated_at)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Param keyword query string false "搜索关键词，搜索标题"
// @Param status query string false "发布状态，int enums" Enums(1,2,4)
// @Param pub_type query string false "发布类型，string enums" Enums(auto,manual)
// @Param platform query string false "目标平台，int enums, 默认-1所有，0--黄页88，1--八方资源" Enums(-1,0,1)
// @Param synced query int false "是否已同步到爱采购，int enums, 默认: 0--不查 1--已同步到爱采购" Enums(0,1)
// @Success 200 {array} models.Info
// @Security ApiKeyAuth
// @Router /info/query [get]
func (c *InfoController) GetQuery() interface{} {
	// Get all matched records
	status := c.Ctx.URLParamIntDefault("status", dbtypes.InfoStatusNone)
	keyword := c.Ctx.URLParam("keyword")
	pubType := c.Ctx.URLParam("pub_type")
	platform := c.Ctx.URLParamIntDefault("platform", int(models.PlatformAll))
	synced := c.Ctx.URLParamIntDefault("synced", 0)

	sortby := c.Ctx.URLParamDefault("sortby", "created_at")
	order := c.Ctx.URLParamDefault("order", "desc")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	if infos, err := services.NewInfoService().GetInfos([]uint64{c.loginInfo.CompanyId}, status, keyword, pubType, sortby, order, limit, offset, models.PlatForm(platform), synced == 1, false); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(infos)
	}
}

// Auth godoc
// @Summary 获取发布信息列表 带分页
// @Description 获取发布信息列表 带分页
// @Description status 0:全部(默认) 1:等待推送 2:推送成功 4:推送失败
// @Tags client
// @Accept  json
// @Produce  json
// @Param sortby query string  false "string enums" Enums(created_at,updated_at)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Param keyword query string false "搜索关键词，搜索标题"
// @Param status query string false "发布状态，int enums" Enums(1,2,4)
// @Param pub_type query string false "发布类型，string enums" Enums(auto,manual)
// @Param platform query string false "目标平台，int enums, 默认-1所有，0--黄页88，1--八方资源" Enums(-1,0,1)
// @Param synced query int false "是否已同步到爱采购，int enums, 默认: 0--不查 1--已同步到爱采购" Enums(0,1)
// @Success 200 {object} services.InfoPaginator
// @Security ApiKeyAuth
// @Router /info/items [get]
func (c *InfoController) GetItems() interface{} {
	// Get all matched records
	status := c.Ctx.URLParamIntDefault("status", dbtypes.InfoStatusNone)
	keyword := c.Ctx.URLParam("keyword")
	pubType := c.Ctx.URLParam("pub_type")
	platform := c.Ctx.URLParamIntDefault("platform", int(models.PlatformAll))
	synced := c.Ctx.URLParamIntDefault("synced", 0)

	sortby := c.Ctx.URLParamDefault("sortby", "created_at")
	order := c.Ctx.URLParamDefault("order", "desc")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	if infos, err := services.NewInfoService().GetInfosWithPaginator([]uint64{c.loginInfo.CompanyId}, status, keyword, pubType, sortby, order, limit, offset, models.PlatForm(platform), synced == 1, false); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(infos)
	}
}

// info godoc
// @Summary 获取发布信息详情
// @Tags client
// @Accept  json
// @Produce  json
// @Param id path int false "信息id"
// @Success 200 {object} models.Info
// @Security ApiKeyAuth
// @Router /info/{id} [get]
func (c *InfoController) GetBy(id int) interface{} {
	if data, err := services.NewInfoService().Find(id); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}
	} else {
		if data.CompanyID != c.loginInfo.CompanyId {
			return utils.ApiError{
				Code: utils.ErrInputParamError.Error(),
				Msg:  "非法访问",
			}
		}
		return utils.NewApiOkWrapper(data)
	}
}

// info godoc
// @Summary 获取前一天信息统计信息
// @Description 获取前一天信息统计信息
// @Tags client
// @Produce  json
// @Success 200 {object} 	schemas.InfoStat
// @Security ApiKeyAuth
// @Router /info/stat [get]
func (c *InfoController) GetStat() interface{} {
	if data, err := services.NewInfoService().GroupByPlatforms(c.loginInfo.CompanyId); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		forms := []models.PlatForm{
			models.PlatformHy88,
			models.PlatformBafang,
			models.PlatformChina,
			models.PlatformKuyiso,
			models.PlatformSole,
			models.PlatformSoleExt,
			models.PlatformLiebiao,
			models.PlatformBaixing,
			models.PlatformSouHaoHuo,
		}
		stat := schemas.InfoStat{}
		for _, form := range forms {
			iform := int(form)
			stat[iform] = schemas.InfoGroupForClient{
				Platform: iform,
				Name:     models.PlatFormName(form),
				Count:    "-",
				Enabled:  false,
			}
			if v, ok := data[iform]; ok {
				stat[iform] = schemas.InfoGroupForClient{
					Platform: iform,
					Name:     models.PlatFormName(form),
					Count:    strconv.Itoa(v.Count),
					Enabled:  true,
				}
			}
		}

		if ms, err := services.Merchant.GetEnabled(c.loginInfo.CompanyId, true, false); err == nil {
			for _, m := range ms {
				iform := int(m.PlatForm)
				if stat[iform].Count == "-" {
					v := stat[iform]
					v.Count = "0"
					stat[iform] = v
				}
				v := stat[iform]
				v.Enabled = true
				stat[iform] = v
			}
		}

		return utils.NewApiOkWrapper(stat)
	}
}

func (c *InfoController) BeginRequest(ctx context.Context) {
	c.loginInfo = RetriveLoginInfo(ctx)
}

func (c *InfoController) EndRequest(ctx context.Context) {
}
