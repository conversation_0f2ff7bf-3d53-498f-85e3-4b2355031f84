package admin

import (
	"fmt"
	"git.paihang8.com/lib/goutils/sites/b2b168"
	"github.com/go-playground/validator/v10"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/business/merchanter"
	"gitlab.com/all_publish/api/internal/controllers"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	coreDb "gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"strconv"
)

type MerchantController struct {
	Ctx          context.Context
	sysLoginInfo controllers.SysLoginInfo
}

func HandleMerchant(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(MerchantController))
}

// Merchant godoc
// @Summary 为某公司添加商户信息
// @Description 需要权限：company:modify or add
// @Description 仅需传输需要修改的部分
// @Tags sys
// @Accept  json
// @Produce  json
// @Param companyId path int true "companyId"
// @Param body body models.Merchant true "Merchant"
// @Success 200 {object} models.Merchant
// @Security ApiKeyAuth
// @Router /sys/{companyId}/merchant [post]
func (c *MerchantController) Post() interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"company:add", "company:modify"}) {
		return nil
	}
	var input models.Merchant
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	var validate = validator.New()
	if err := validate.Struct(input); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	input.CompanyID, _ = c.Ctx.Params().GetUint64("companyId")

	if client, ok := models.GetMerchant(models.PlatForm(input.PlatForm)); !ok {
		return utils.ApiError{
			Code: utils.ErrAddMerchantFailed.Error(),
			Msg:  "merchant_class无效",
		}.Response(c.Ctx)
	} else {
		if !client.(*merchanter.Hy88Merchant).VerifyAccount(input.Account) {
			return utils.ApiError{
				Code: utils.ErrAddMerchantFailed.Error(),
				Msg:  "account是无效的",
			}.Response(c.Ctx)
		}
		if err := coreDb.Instance().Get().Create(&input).Error; err != nil {
			return utils.ApiError{
				Code: utils.ErrOPDataBaseError.Error(),
				Msg:  err.Error(),
			}.Response(c.Ctx)
		}

	}
	return utils.NewApiOkWrapper(input)
}

// Merchant godoc
// @Summary 修改某公司的某个商户信息
// @Description 需要权限：company:modify
// @Description 仅需传输需要修改的部分
// @Tags sys
// @Accept  json
// @Produce  json
// @Param companyId path int true "companyId"
// @Param merchantId path int true "merchantId"
// @Param body body models.Merchant true "Merchant"
// @Success 200 {object} schemas.Album
// @Security ApiKeyAuth
// @Router /sys/{companyId}/merchant/{merchantId}  [put]
func (c *MerchantController) PutBy(merchantId uint64) interface{} {
	companyId, _ := c.Ctx.Params().GetEntryAt(0).Uint64Default(0)
	if !controllers.HasPermission(c.Ctx, []string{"company:modify"}) {
		return nil
	}
	var input models.Merchant
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	var validate = validator.New()
	if err := validate.Struct(input); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	var output models.Merchant
	if err := coreDb.Instance().Get().Find(&output, merchantId).Error; err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	if output.CompanyID != companyId {
		return utils.ApiError{
			Code: utils.ErrModifyMerchantFailed.Error(),
			Msg:  "商户与企业不匹配",
		}.Response(c.Ctx)
	}
	if len(input.Account) > 0 {
		if client, ok := models.GetMerchant(models.PlatForm(output.PlatForm)); !ok {
			return utils.ApiError{
				Code: utils.ErrAddMerchantFailed.Error(),
				Msg:  "原有数据的plat_form无效, 找不到实现类",
			}.Response(c.Ctx)
		} else {
			if !client.(*merchanter.Hy88Merchant).VerifyAccount(input.Account) {
				return utils.ApiError{
					Code: utils.ErrAddMerchantFailed.Error(),
					Msg:  "account是无效的",
				}.Response(c.Ctx)
			}
		}
	}
	var m map[string]interface{}
	c.Ctx.ReadJSON(&m)
	delete(m, "plat_form")
	if err := dbtypes.UpdateModelFromMap(&output, m); err != nil {
		return utils.ApiError{Code: utils.ErrUpdateModelError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	if output.Pause {
		output.PauseReason = fmt.Sprintf("由用户%d(%s)手工关闭", c.sysLoginInfo.SysUserId, *c.sysLoginInfo.Role)
	} else {
		output.PauseReason = ""
	}
	if err := coreDb.Instance().Get().Save(&output).Error; err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	return utils.NewApiOkWrapper(output)
}

// Merchant godoc
// @Summary 获取某个公司的商户列表
// @Description 需要权限：company:get 或者 company:list
// @Tags sys
// @Accept  json
// @Produce  json
// @Param companyId path int true "companyId"
// @Param sortby query string  false "string enums" Enums(created_at,updated_at)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Success 200 {array} models.Merchant
// @Security ApiKeyAuth
// @Router /sys/{companyId}/merchant/query [get]
func (c *MerchantController) GetQuery() interface{} {
	companyId, _ := c.Ctx.Params().GetUint64("companyId")
	if !controllers.HasPermission(c.Ctx, []string{"company:get", "company:list"}, true) {
		return nil
	}
	sortby := c.Ctx.URLParamDefault("sortby", "created_at")
	order := c.Ctx.URLParamDefault("order", "desc")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)

	if merchants, err := services.Merchant.GetItemsOfCompany(companyId, "", sortby, order, limit, offset); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(merchants)
	}
}

// Merchant godoc
// @Summary 获取商户Meta信息
// @Description <company_id> 可传任意值 需要权限：company:get 或者 company:list 、company:add 、company:modify
// @Tags sys
// @Accept  json
// @Param companyId path int true "companyId"
// @Produce  json
// @Success 200 {array} models.MerchantMeta
// @Security ApiKeyAuth
// @Router /sys/{companyId}/merchant/meta [get]
func (c *MerchantController) GetMeta() interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"company:get", "company:list", "company:add", "company:modify"}, true) {
		return nil
	}
	var metas = make([]models.MerchantMeta, 0, len(models.MerchantMap))
	for _, m := range models.MerchantMap {
		metas = append(metas, m.Meta())
	}
	return utils.NewApiOkWrapper(metas)
}

func (c *MerchantController) BeginRequest(ctx context.Context) {
	c.sysLoginInfo = controllers.RetriveSysLoginInfo(ctx)
}

func (c *MerchantController) EndRequest(ctx context.Context) {
}

// Merchant godoc
// @Summary 绑定酷易搜账号
// @Description 绑定酷易搜账号
// @Tags client
// @Produce  json
// @Param companyId path int true "companyId"
// @Param body body schemas.BindKuyiso true "BindKuyiso"
// @Success 200 {object} models.Merchant
// @Security ApiKeyAuth
// @Router /sys/{companyId}/merchant/bind/kuyiso [post]
func (c *MerchantController) PostBindKuyiso() interface{} {
	var input schemas.BindKuyiso
	companyId, _ := c.Ctx.Params().GetUint64("companyId")
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	if err := b2b168.Validate.Struct(input); err != nil {
		errStr := b2b168.Translate(err.(validator.ValidationErrors))
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  errStr,
		}.Response(c.Ctx)
	}
	if client, ok := models.GetTrigger(models.PlatformKuyiso); ok {
		if m, err := services.Merchant.GetByCompanyId(companyId, models.PlatformKuyiso); err != nil {
			return utils.ApiError{
				Code: utils.ErrInputParamError.Error(),
				Msg:  err.Error(),
			}.Response(c.Ctx)
		} else {
			if err := client.BindUser(m, input.Mobile, input.Password); err != nil {
				if ce, ok := err.(utils.CodeError); ok {
					return utils.ApiError{
						Code: strconv.Itoa(ce.Code),
						Msg:  ce.Error(),
					}
				} else {
					return utils.ApiError{
						Code: utils.ErrInputParamError.Error(),
						Msg:  err.Error(),
					}.Response(c.Ctx)
				}
			}
		}
	}
	return utils.NewApiOkWrapper(nil)
}
