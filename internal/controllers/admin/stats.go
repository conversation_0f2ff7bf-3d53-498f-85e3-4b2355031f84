package admin

import (
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/controllers"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"time"
)

type UserStatsController struct {
	Ctx          context.Context
	sysLoginInfo controllers.SysLoginInfo
}

func HandleUserStats(app *mvc.Application) {
	app.Handle(new(UserStatsController))
}

// User godoc
// @Summary 获取公司统计信息列表
// @Description 获取公司统计信息列表
// @Tags sys
// @Param kw query string false "搜索词"
// @Param sortby query string  false "string enums" Enums(product_cnt,product_promotion_cnt,info_succeed_cnt,info_succeed_cnt_yesterday,info_failed_cnt_yesterday,op_end_time,ranked_cnt)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Produce  json
// @Success 200 {object} services.UserPubStatsPaginator
// @Security ApiKeyAuth
// @Router /sys/stats/query [get]
func (c *UserStatsController) GetQuery() interface{} {
	//if !controllers.HasPermission(c.Ctx, []string{"company:get", "company:list"}, true) {
	//	return nil
	//}
	kw := c.Ctx.URLParamDefault("kw", "")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	sortby := c.Ctx.URLParamDefault("sortby", "product_cnt")
	order := c.Ctx.URLParamDefault("order", "desc")
	sortfields := []string{
		"product_cnt", "product_promotion_cnt", "info_succeed_cnt", "info_succeed_cnt_yesterday", "info_failed_cnt_yesterday", "op_end_time", "ranked_cnt",
	}
	flag := false
	for _, v := range sortfields {
		if sortby == v {
			flag = true
			break
		}
	}
	if !flag {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  "sortby 参数错误",
		}.Response(c.Ctx)
	}
	if order != "asc" && order != "desc" {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  "order参数错误",
		}.Response(c.Ctx)
	}
	var advisorId uint64 = 0
	if *c.sysLoginInfo.Role == dbtypes.Role_Company_Adviser {
		advisorId = c.sysLoginInfo.SysUserId
	}
	if paginator, err := services.NewStatsService().GetList(limit, offset, int(advisorId), kw, sortby, order); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(paginator)
	}
}

// User godoc
// @Summary 获取公司统计详情
// @Description 获取公司统计详情
// @Tags sys
// @Param companyId path int true "companyId"
// @Produce  json
// @Success 200 {object} models.UserPubStatDetails
// @Security ApiKeyAuth
// @Router /sys/stats/{companyId} [get]
func (c *UserStatsController) GetBy(companyId uint64) interface{} {
	//if !controllers.HasPermission(c.Ctx, []string{"company:get", "company:list"}, true) {
	//	return nil
	//}

	if item, err := services.NewStatsService().GetDetail(companyId); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(item)
	}
}

// GetDaily User godoc
// @Summary 获取历史统计数据
// @Description 获取历史统计数据
// @Tags sys
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Produce  json
// @Success 200 {object} services.UserPubStatsPaginator
// @Security ApiKeyAuth
// @Router /sys/stats/daily [get]
func (c *UserStatsController) GetDaily() interface{} {
	//if !controllers.HasPermission(c.Ctx, []string{"company:get", "company:list"}, true) {
	//	return nil
	//}
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	typ := c.Ctx.URLParamIntDefault("type", 0)
	start := c.Ctx.URLParamDefault("start", "")
	end := c.Ctx.URLParamDefault("end", "")
	startTime := dbtypes.SHNow().AddDate(0, 0, -7)
	endTime := dbtypes.SHNow()
	if time, err := time.ParseInLocation("2006-01-02 15:04:05", start, dbtypes.SHLocation); err == nil {
		startTime = time
	}
	if time, err := time.ParseInLocation("2006-01-02 15:04:05", end, dbtypes.SHLocation); err == nil {
		endTime = time
	}
	if paginator, err := services.NewDailyHistoryService(db.Instance().Get()).GetHistory(typ, startTime, endTime, limit, offset); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(paginator)
	}
}

func (c *UserStatsController) BeginRequest(ctx context.Context) {
	c.sysLoginInfo = controllers.RetriveSysLoginInfo(ctx)
}

func (c *UserStatsController) EndRequest(ctx context.Context) {
}
