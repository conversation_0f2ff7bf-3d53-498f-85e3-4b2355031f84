package admin

import (
	"errors"
	"net/http"
	"strings"

	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/controllers"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	coreDb "gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gopkg.in/go-playground/validator.v9"
	"gorm.io/gorm"
)

type SysUserController struct {
	Ctx          context.Context
	sysLoginInfo controllers.SysLoginInfo
}

func HandleSysUser(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(SysUserController))
}

// sys/user godoc
// @Summary 系统用户获取当前用户信息
// @Description 系统用户获取当前用户信息
// @Tags sys
// @Produce  json
// @Success 200 {object} models.SystemUser
// @Security ApiKeyAuth
// @Router /sys/user [get]
func (c *SysUserController) Get() interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"sys"}) {
		return nil
	}
	var user models.SystemUser
	if err := coreDb.Instance().Get().Find(&user, c.sysLoginInfo.SysUserId).Error; err != nil {
		c.Ctx.StatusCode(http.StatusInternalServerError)
		return utils.ApiError{Code: utils.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	return utils.NewApiOkWrapper(user)
}

// Auth godoc
// @Summary 系统用户用户修改密码
// @Description 系统用户用户修改密码
// @Tags sys
// @Accept  json
// @Produce  json
// @Param body body schemas.ModifyPassword true "ModifyPassword"
// @Success 200 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /sys/user/modify_password [post]
func (c *SysUserController) PostModify_password() interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"sys"}) {
		return nil
	}
	var modify schemas.ModifyPassword
	if err := c.Ctx.ReadJSON(&modify); err != nil {
		return utils.ApiError{utils.ErrModifyPasswordFailed.Error(), err.Error(), nil}.Response(c.Ctx)
	}
	var validate = validator.New()
	if err := validate.Struct(modify); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	var user models.SystemUser
	if err := coreDb.Instance().Get().Find(&user, c.sysLoginInfo.SysUserId).Error; err != nil {
		return utils.ApiError{Code: utils.ErrModifyPasswordFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	if !controllers.VerifyPassword(user.Username, modify.Old, user.Password) {
		return utils.ApiError{Code: utils.ErrModifyPasswordFailed.Error(), Msg: "旧密码不正确"}.Response(c.Ctx)
	}

	user.Password = controllers.HashPassword(user.Username, modify.New)
	if err := coreDb.Instance().Get().Save(&user).Error; err != nil {
		return utils.ApiError{Code: utils.ErrModifyPasswordFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	return utils.NewApiOkWrapper(nil)
}

// sys/user godoc
// @Summary 获取系统权限列表
// @Description 请求权限：system_user:add or system_user:modify
// @Tags sys
// @Produce  json
// @Success 200 {object} dbtypes.Scopes
// @Security ApiKeyAuth
// @Router /sys/user/scopes [get]
func (c *SysUserController) GetScopes() interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"system_user:add", "system_user:modify"}, true) {
		return nil
	}
	var scopes dbtypes.Scopes
	for scope, desc := range dbtypes.SCOPES_ENTITY {
		parts := strings.Split(scope, ":")
		scopes.Scopes = append(scopes.Scopes, dbtypes.ScopeItem{
			Actions:     parts[1:],
			Description: desc,
			Scope:       scope,
			Entity:      parts[0],
		})
	}
	scopes.Actions = map[string]string{
		"add":    "增加",
		"delete": "删除",
		"list":   "查询",
		"get":    "获取",
		"modify": "修改",
	}
	return utils.NewApiOkWrapper(scopes)
}

// sys/user godoc
// @Summary 添加系统用户
// @Description 用于管理员添加后台用户,需要权限：system_user:add
// @Tags sys
// @Accept  json
// @Produce  json
// @Param body body models.SystemUser true "SystemUser"
// @Success 200 {object} models.SystemUser
// @Security ApiKeyAuth
// @Router /sys/user/system_user [post]
func (c *SysUserController) PostSystem_user() interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"system_user:add"}) {
		return nil
	}
	var sysUser models.SystemUser
	if err := c.Ctx.ReadJSON(&sysUser); err != nil {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	var validate = validator.New()
	if err := validate.Struct(sysUser); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	var systemUser models.SystemUser
	if err := coreDb.Instance().Get().Where("username = ?", sysUser.Username).First(&systemUser).Error; !errors.Is(err, gorm.ErrRecordNotFound) {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: "用户已经存在"}.Response(c.Ctx)
	}

	sysUser.Ban = false
	sysUser.Password = controllers.HashPassword(sysUser.Username, sysUser.Password)
	if sysUser.Role != nil {
		sysUser.Scopes = dbtypes.DEF_Scopes[*sysUser.Role]
	}
	if err := coreDb.Instance().Get().Create(&sysUser).Error; err != nil {
		return utils.ApiError{Code: utils.ErrAddSysUserFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	return utils.NewApiOkWrapper(sysUser)
}

// sys/user godoc
// @Summary 修改系统用户
// @Description "用于管理员修改后台用户,需要权限：system_user:modify
// @Description 仅可修改以下字段 'phone', 'email', 'ban', 'scopes', 'role','name'"
// @Tags sys
// @Accept  json
// @Produce  json
// @Param userId path int true "userId"
// @Param body body schemas.ModifySystemUser true "SystemUser"
// @Success 200 {object} models.SystemUser
// @Security ApiKeyAuth
// @Router /sys/user/system_user/{userId} [put]
func (c *SysUserController) PutSystem_userBy(userId uint64) interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"system_user:modify"}) {
		return nil
	}
	var input schemas.ModifySystemUser
	var output models.SystemUser
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	var validate = validator.New()
	if err := validate.Struct(input); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	if err := coreDb.Instance().Get().Find(&output, userId).Error; err != nil {
		return utils.ApiError{Code: utils.ErrModifySysUserFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}

	modified := models.SystemUser{
		Name:   input.Name,
		Role:   input.Role,
		Phone:  input.Phone,
		Email:  input.Email,
		Ban:    input.Ban,
		Scopes: input.Scopes,
	}
	if err := coreDb.Instance().Get().Model(&output).Updates(modified).Error; err != nil {
		return utils.ApiError{Code: utils.ErrModifySysUserFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	return utils.NewApiOkWrapper(output)
}

// sys/user godoc
// @Summary 获取系统用户列表
// @Description 需要权限：system_user:get 或者 system_user:list
// @Tags sys
// @Accept  json
// @Produce  json
// @Param sortby query string  false "string enums" Enums(created_at,updated_at)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Param keyword query string false "搜索关键词，搜索name"
// @Success 200 {array} models.SystemUser
// @Security ApiKeyAuth
// @Router /sys/user/sys_user/query [get]
func (c *SysUserController) GetSystem_userQuery() interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"system_user:get", "system_user:list"}, true) {
		return nil
	}
	keyword := c.Ctx.URLParam("keyword")
	sortby := c.Ctx.URLParamDefault("sortby", "created_at")
	order := c.Ctx.URLParamDefault("order", "desc")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)

	if systemUsers, err := services.NewSystemUserService().Search(keyword, sortby, order, limit, offset); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(systemUsers)
	}
}

// sys/user godoc
// @Summary 添加客户账号
// @Description 用于管理员添加后台用户,需要权限：user:add
// @Tags sys
// @Accept  json
// @Produce  json
// @Param body body models.User true "User"
// @Success 200 {object} models.User
// @Security ApiKeyAuth
// @Router /sys/user/client_user [post]
func (c *SysUserController) PostClient_user() interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"user:add"}) {
		return nil
	}
	var user models.User
	if err := c.Ctx.ReadJSON(&user); err != nil {
		return utils.ApiError{Code: utils.ErrAddSysUserFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	var validate = validator.New()
	if err := validate.Struct(user); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	user.Password = controllers.HashPassword(user.Username, user.Password)
	if err := coreDb.Instance().Get().Create(&user).Error; err != nil {
		return utils.ApiError{Code: utils.ErrAddSysUserFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	return utils.NewApiOkWrapper(user)
}

// sys/user godoc
// @Summary 修改用户信息
// @Description "用于管理员修改用户信息,需要权限：user:modify
// @Description 仅可修改以下字段 'phone', 'email', 'ban', 'company','max_products(超级管理员才可以)'"
// @Tags sys
// @Accept  json
// @Produce  json
// @Param userId path int true "userId"
// @Param body body models.User true "User"
// @Success 200 {object} models.User
// @Security ApiKeyAuth
// @Router /sys/user/client_user/{userId} [put]
func (c *SysUserController) PutClient_userBy(userId uint64) interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"user:modify"}) {
		return nil
	}
	var input, output models.User
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return utils.ApiError{Code: utils.ErrModifyUserFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	var validate = validator.New()
	if err := validate.Struct(input); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	if err := coreDb.Instance().Get().Find(&output, userId).Error; err != nil {
		return utils.ApiError{Code: utils.ErrModifyUserFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	if err := coreDb.Instance().Get().Find(&output.Company, output.CompanyID).Error; err != nil {
		return utils.ApiError{Code: utils.ErrModifyUserFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	output.LastLoginAt = dbtypes.SHNow()
	var m map[string]interface{}
	c.Ctx.ReadJSON(&m)
	if v, ok := m["company"]; ok {
		com := v.(map[string]interface{})
		if _, ok := com["advisor_id"]; ok && *c.sysLoginInfo.Role != dbtypes.Role_Administrater {
			return utils.ApiError{Code: utils.ErrModifyUserFailed.Error(), Msg: "超级管理员才可以修改服务顾问"}.Response(c.Ctx)
		}
	}
	if err := dbtypes.UpdateModelFromMap(&output, m); err != nil {
		return utils.ApiError{Code: utils.ErrUpdateModelError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	if err := coreDb.Instance().Get().Select("max_products", "ban", "email", "phone").Updates(&output).Error; err != nil {
		return utils.ApiError{Code: utils.ErrModifyUserFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	} else {
		if err := coreDb.Instance().Get().Select("advisor_id", "op_end_time", "phone").Updates(&output.Company).Error; err != nil {
			return utils.ApiError{Code: utils.ErrModifyUserFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
		}
	}
	return utils.NewApiOkWrapper(output)
}

// sys/user godoc
// @Summary 获取客户列表
// @Description 需要权限：user:get 或者 user:list
// @Tags sys
// @Accept  json
// @Produce  json
// @Param sortby query string  false "string enums" Enums(created_at,updated_at)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Param keyword query string false "搜索关键词，搜索name"
// @Success 200 {array} models.User
// @Security ApiKeyAuth
// @Router /sys/user/client_user/query [get]
func (c *SysUserController) GetClient_userQuery() interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"user:get", "user:list"}, true) {
		return nil
	}
	var advisorId uint64
	if *c.sysLoginInfo.Role == dbtypes.Role_Company_Adviser {
		advisorId = c.sysLoginInfo.SysUserId
	}
	keyword := c.Ctx.URLParam("keyword")
	sortby := c.Ctx.URLParamDefault("sortby", "created_at")
	order := c.Ctx.URLParamDefault("order", "desc")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	if users, err := services.NewUserService().Search(keyword, advisorId, sortby, order, limit, offset); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(users)
	}
}

// sys/user godoc
// @Summary 获取指定用户开通功能列表
// @Description 获取指定用户信息
// @Tags sys
// @Produce  json
// @Param company_id query int false "公司id"
// @Success 200 {array} models.UserPlatform
// @Security ApiKeyAuth
// @Router /sys/user/func_list [get]
func (c *SysUserController) GetFunc_list() interface{} {
	cid := c.Ctx.URLParamInt64Default("company_id", 0)
	if list, err := services.NewUserPlatformService().GetFuncListByCompanyId(uint64(cid)); err != nil {
		return utils.ApiError{Code: utils.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(list)
	}
}

func (c *SysUserController) BeginRequest(ctx context.Context) {
	c.sysLoginInfo = controllers.RetriveSysLoginInfo(ctx)
}

func (c *SysUserController) EndRequest(ctx context.Context) {
}
