package admin

import (
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/controllers"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/dbtypes"
)

type TaskController struct {
	Ctx          context.Context
	sysLoginInfo controllers.SysLoginInfo
}

func HandleTask(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(TaskController))
}

// Task godoc
// @Summary 获取任务列表
// @Description 需要权限：product/info:get 或者 product/info:list
// @Description  任务接口获取到的是当前系统用户所绑定的企业等待推送的产品信息列表
// @Tags sys
// @Accept  json
// @Produce  json
// @Param sortby query string  false "string enums" Enums(created_at,updated_at)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(50)
// @Param offset query int true "int default" default(0)
// @Param platform query string false "目标平台，int enums, 默认-1所有，0--黄页88，2--八方资源" Enums(-1,0,2)
// @Param synced query int false "是否已同步到爱采购，int enums, 默认: 0--不查 1--已同步到爱采购" Enums(0,1)
// @Success 200 {object} models.Info
// @Security ApiKeyAuth
// @Router /sys/task/query [get]
func (c *TaskController) GetQuery() interface{} {

	status := dbtypes.InfoStatusWattingpublish
	pubType := "auto"
	keyword := ""
	platform := c.Ctx.URLParamIntDefault("platform", int(models.PlatformAll))
	sortby := c.Ctx.URLParamDefault("sortby", "created_at")
	order := c.Ctx.URLParamDefault("order", "desc")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	synced := c.Ctx.URLParamIntDefault("synced", 0)

	if *c.sysLoginInfo.Role != dbtypes.Role_Company_Adviser {
		if infos, err := services.NewInfoService().GetInfos(nil, status, keyword, pubType, sortby, order, limit, offset, models.PlatForm(platform), synced == 1, true); err != nil {
			return configs.ApiError{Code: configs.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
		} else {
			return configs.NewApiOkWrapper(infos)
		}
	} else {
		if ids, err := services.NewCompanyService().GetCompanyIdsOfAdvisor(c.sysLoginInfo.SysUserId); err != nil {
			return configs.ApiError{
				Code: configs.ErrQueryTaskFailed.Error(),
				Msg:  err.Error(),
			}.Response(c.Ctx)
		} else {
			if infos, err := services.NewInfoService().GetInfos(ids, status, keyword, pubType, sortby, order, limit, offset, models.PlatForm(platform), synced == 1, true); err != nil {
				return configs.ApiError{Code: configs.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
			} else {
				return configs.NewApiOkWrapper(infos)
			}
		}
	}
}

func (c *TaskController) BeginRequest(ctx context.Context) {
	c.sysLoginInfo = controllers.RetriveSysLoginInfo(ctx)
}

func (c *TaskController) EndRequest(ctx context.Context) {
}
