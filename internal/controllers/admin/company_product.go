package admin

import (
	"errors"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/controllers"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/gorm"
)

type CompanyProductController struct {
	Ctx          context.Context
	sysLoginInfo controllers.SysLoginInfo
}

func HandleCompanyProduct(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(CompanyProductController))
}

// Sys/companyproduct godoc
// @Summary 获取发布信息列表
// @Description 需要权限：companyproduct:list
// @Description 状态0-待发布 1-推送成功 3-推送失败
// @Tags sys
// @Accept  json
// @Produce  json
// @Param sortby query string  false "string enums" Enums(created_at,updated_at)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Param keyword query string false "搜索关键词，搜索标题"
// @Param company_id query int false "公司id"
// @Param status query int false "过滤状态， 默认全部" Enums(0,1,3)
// @Success 200 {array} models.CompanyProduct
// @Security ApiKeyAuth
// @Router /sys/companyproduct/query [get]
func (c *CompanyProductController) GetQuery() interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"companyproduct:list"}) {
		return nil
	}
	status := c.Ctx.URLParamIntDefault("status", dbtypes.CompanyProductStatusNone)
	keyword := c.Ctx.URLParam("keyword")

	sortby := c.Ctx.URLParamDefault("sortby", "created_at")
	order := c.Ctx.URLParamDefault("order", "desc")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	if cid, _ := c.Ctx.URLParamInt64("company_id"); cid > 0 {
		if *c.sysLoginInfo.Role == dbtypes.Role_Company_Adviser {
			var company models.Company
			if err := db.Instance().Get().Where("advisor_id = ? and id = ?", c.sysLoginInfo.SysUserId, cid).Find(&company).Error; errors.Is(err, gorm.ErrRecordNotFound) {
				return configs.ApiError{Code: configs.ErrScopeMissing.Error(), Msg: "权限不足"}.Response(c.Ctx)
			}
		}
		if paginator, err := services.NewCompanyProductService().GetCompanyProducts([]uint64{uint64(cid)}, status, keyword, sortby, order, limit, offset, true); err != nil {
			return configs.ApiError{Code: configs.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
		} else {
			return configs.NewApiOkWrapper(paginator)
		}
	} else {
		if paginator, err := services.NewCompanyProductService().GetCompanyProducts(nil, status, keyword, sortby, order, limit, offset, true); err != nil {
			return configs.ApiError{Code: configs.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
		} else {
			return configs.NewApiOkWrapper(paginator)
		}
	}
}

// Sys/companyproduct godoc
// @Summary 获取发布信息列表 带分页
// @Description 需要权限：companyproduct:list
// @Description 状态0-待发布 1-推送成功 3-推送失败
// @Tags sys
// @Accept  json
// @Produce  json
// @Param sortby query string  false "string enums" Enums(created_at,updated_at)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Param keyword query string false "搜索关键词，搜索标题"
// @Param company_id query int false "公司id"
// @Param status query int false "过滤状态， 默认全部" Enums(0,1,3)
// @Success 200 {object} services.CompanyProductPaginator
// @Security ApiKeyAuth
// @Router /sys/companyproduct/items [get]
func (c *CompanyProductController) GetItems() interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"companyproduct:list"}) {
		return nil
	}
	status := c.Ctx.URLParamIntDefault("status", dbtypes.CompanyProductStatusNone)
	keyword := c.Ctx.URLParam("keyword")

	sortby := c.Ctx.URLParamDefault("sortby", "created_at")
	order := c.Ctx.URLParamDefault("order", "desc")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	if cid, _ := c.Ctx.URLParamInt64("company_id"); cid > 0 {
		if *c.sysLoginInfo.Role == dbtypes.Role_Company_Adviser {
			var company models.Company
			if err := db.Instance().Get().Where("advisor_id = ? and id = ?", c.sysLoginInfo.SysUserId, cid).Find(&company).Error; errors.Is(err, gorm.ErrRecordNotFound) {
				return configs.ApiError{Code: configs.ErrScopeMissing.Error(), Msg: "权限不足"}.Response(c.Ctx)
			}
		}
		if paginator, err := services.NewCompanyProductService().GetCompanyProductsWithPaginator([]uint64{uint64(cid)}, status, keyword, sortby, order, limit, offset, true); err != nil {
			return configs.ApiError{Code: configs.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
		} else {
			return configs.NewApiOkWrapper(paginator)
		}
	} else {
		if paginator, err := services.NewCompanyProductService().GetCompanyProductsWithPaginator(nil, status, keyword, sortby, order, limit, offset, true); err != nil {
			return configs.ApiError{Code: configs.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
		} else {
			return configs.NewApiOkWrapper(paginator)
		}
	}
}

func (c *CompanyProductController) BeginRequest(ctx context.Context) {
	c.sysLoginInfo = controllers.RetriveSysLoginInfo(ctx)
}

func (c *CompanyProductController) EndRequest(ctx context.Context) {
}
