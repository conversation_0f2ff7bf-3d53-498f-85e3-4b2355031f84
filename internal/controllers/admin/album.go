package admin

import (
	"encoding/json"
	"errors"
	"fmt"
	"git.paihang8.com/lib/goutils/sites/fs"
	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/controllers"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/db"
	"io/ioutil"
	"mime/multipart"
	"strings"
	"sync"
)

type AlbumController struct {
	Ctx          iris.Context
	Svc          services.AlbumServicer
	sysLoginInfo controllers.SysLoginInfo
}

func HandleAlbum(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	c := new(AlbumController)
	c.Svc = services.NewAlbumService(db.Instance().Get())
	app.Handle(c)
}

// Album godoc
// @Summary 上传文件
// @Description 支持JPG/JPEG/BMP/GIF/PNG两种格式
// @Tags sys
// @Accept  multipart/form-data
// @Produce  json
// @Param cid path int true "cid 公司id"
// @Param   file formData file true  "jpg,jpeg,bmp, gif, png file"
// @Success 200 {array} schemas.UploadResponse
// @Security ApiKeyAuth
// @Router /sys/album/upload/{cid} [post]
func (c *AlbumController) PostUploadBy(cid uint64) interface{} {
	var albumId uint64 = 0
	err := c.Ctx.Request().ParseMultipartForm(1024 * 1024 * 100)
	if err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	if c.Ctx.Request().MultipartForm != nil {
		if fhs := c.Ctx.Request().MultipartForm.File; fhs != nil {
			if fileHeaders, ok := fhs["file"]; ok {
				for _, header := range fileHeaders {
					paths := strings.Split(header.Filename, ".")
					ext := strings.ToLower(paths[len(paths)-1])
					if ext != "png" && ext != "jpg" && ext != "jpeg" && ext != "bmp" && ext != "gif" {
						return utils.ApiError{
							Code: utils.ErrInputParamError.Error(),
							Msg:  "文件类型不支持",
						}.Response(c.Ctx)
					}
				}
				var wg sync.WaitGroup
				wg.Add(len(fileHeaders))
				m := make(map[string]schemas.UploadResponse)
				for _, header := range fileHeaders {
					go func(header *multipart.FileHeader) {
						defer wg.Done()
						r, err := controllers.PostFileByHeader("file", header, map[string]string{
							"token": fs.GenToken(utils.ApiConfig.Upload.Token),
							"path":  fmt.Sprintf("%d/%d", cid, albumId),
						}, utils.ApiConfig.Upload.Url+"/file/upload")
						if err != nil {
							m[header.Filename] = schemas.UploadResponse{
								Msg: err.Error(),
							}
							return
						}
						defer r.Body.Close()
						if b, err := ioutil.ReadAll(r.Body); err != nil {
							m[header.Filename] = schemas.UploadResponse{
								Msg: err.Error(),
							}
						} else {
							var res schemas.UploadApiResponse
							if err := json.Unmarshal(b, &res); err != nil {
								m[header.Filename] = schemas.UploadResponse{
									Msg: err.Error(),
								}
							} else {
								if res.Error != "" {
									m[header.Filename] = schemas.UploadResponse{
										Msg: res.Error,
									}
								} else {
									if err := services.NewImageService().AddToAlbum(&models.Image{
										Name:      header.Filename,
										Url:       res.Data.Url,
										Size:      res.Data.Size,
										Hash:      res.Data.Etag,
										CompanyId: cid,
										AlbumId:   albumId,
									}, uint64(albumId)); err != nil {
										m[header.Filename] = schemas.UploadResponse{
											Msg: err.Error(),
										}
										if errors.Is(err, utils.ErrDupImage) {
											res.Data.Msg = err.Error()
											m[header.Filename] = res.Data
										}
									} else {
										m[header.Filename] = res.Data
									}
								}

							}
						}
					}(header)
				}
				wg.Wait()
				var res []schemas.UploadResponse
				for _, header := range fileHeaders {
					res = append(res, m[header.Filename])
				}
				return utils.NewApiOkWrapper(res)
			}
		}
	}
	return utils.ApiError{
		Code: utils.ErrInputParamError.Error(),
		Msg:  "no file found",
	}.Response(c.Ctx)
}

func (c *AlbumController) BeginRequest(ctx context.Context) {
	c.sysLoginInfo = controllers.RetriveSysLoginInfo(ctx)
}

func (c *AlbumController) EndRequest(ctx context.Context) {
}
