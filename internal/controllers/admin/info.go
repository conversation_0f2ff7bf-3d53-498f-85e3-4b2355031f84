package admin

import (
	"errors"
	"github.com/go-playground/validator/v10"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/controllers"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/gorm"
)

type InfoController struct {
	Ctx          context.Context
	sysLoginInfo controllers.SysLoginInfo
}

func HandleInfo(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(InfoController))
}

// Sys/info godoc
// @Summary 审核并发布产品信息
// @Description 需要权限：info:modify
// @Description 如果信息需要，仅需传输需要修改的部分
// @Description 信息推送成功后修改部分才会保存
// @Tags sys
// @Accept  json
// @Produce  json
// @Param body body models.Info true "Info"
// @Success 200 {object} models.Info
// @Security ApiKeyAuth
// @Router /sys/info/publish/{infoId} [put]
func (c *InfoController) PutPublishBy(id uint64) interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"info:modify", "product:list"}) {
		return nil
	}
	var input, output models.Info
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	input.ID = id
	var rawInfo map[string]interface{}
	c.Ctx.ReadJSON(&rawInfo)
	var validate = validator.New()
	if err := validate.Struct(input); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}

	if err := db.Instance().Get().Find(&output, id).Error; err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	var company models.Company
	if err := db.Instance().Get().Find(&company, output.CompanyID).Error; err != nil {
		return utils.ApiError{
			Code: utils.ErrModifyAndPubinfoFailed.Error(),
			Msg:  "信息关联的公司不存在",
		}.Response(c.Ctx)
	}
	input.Company = &company
	if output.Status == dbtypes.InfoStatusPublishSuccessed {
		return utils.ApiError{
			Code: utils.ErrModifyAndPubinfoFailed.Error(),
			Msg:  "已经发布，请勿重发",
		}.Response(c.Ctx)
	}
	if err := dbtypes.UpdateModelFromMap(&output, rawInfo); err != nil {
		return utils.ApiError{Code: utils.ErrUpdateModelError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	output.Status = dbtypes.InfoStatusPublishSuccessed
	if merchantId, ok := output.PubRes["merchant_id"]; !ok {
		return utils.ApiError{
			Code: utils.ErrModifyAndPubinfoFailed.Error(),
			Msg:  "信息没有指定商户",
		}.Response(c.Ctx)
	} else {
		var m models.Merchant
		if err := db.Instance().Get().Find(&m, uint64(merchantId.(float64))).Error; err != nil {
			return utils.ApiError{
				Code: utils.ErrModifyAndPubinfoFailed.Error(),
				Msg:  "指定商户的商户不存在",
			}.Response(c.Ctx)
		}
		client, _ := models.GetMerchant(models.PlatForm(m.PlatForm))
		client.LoginBy(m.Account)
		if output.ProductID != nil && *output.ProductID > 0 {
			var product models.Product
			if err := db.Instance().Get().Find(&product, output.ProductID).Error; err != nil {
				return utils.ApiError{
					Code: utils.ErrModifyAndPubinfoFailed.Error(),
					Msg:  "信息关联的产品不存在",
				}.Response(c.Ctx)
			} else {
				input.Product = &product
			}
		}
		url, err := client.PublishInfo(input)
		if err != nil {
			return utils.ApiError{
				Code: utils.ErrCallApiError.Error(),
				Msg:  err.Error(),
			}
		} else {
			output.PubRes["res_url"] = url
			output.Status = dbtypes.InfoStatusPublishSuccessed
			if err = db.Instance().Get().Save(&output).Error; err != nil {
				return utils.ApiError{
					Code: utils.ErrOPDataBaseError.Error(),
					Msg:  err.Error(),
				}.Response(c.Ctx)
			} else {
				return utils.NewApiOkWrapper(output)
			}
		}

	}
}

// Sys/info godoc
// @Summary 审核拒绝信息
// @Description 需要权限：info:modify
// @Tags sys
// @Accept  json
// @Produce  json
// @Param infoId path int true "infoId"
// @Param body body schemas.AuditNotPass true "AuditNotPass"
// @Success 200 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /sys/info/check_not_pass/{infoId} [put]
func (c *InfoController) PutCheck_not_passBy(id uint64) interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"info:modify"}) {
		return nil
	}
	var input schemas.AuditNotPass
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	var validate = validator.New()
	if err := validate.Struct(input); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	updated := map[string]interface{}{}
	updated["status"] = dbtypes.InfoStatusAuditnotpass
	updated["audit_res"] = input.AuditRes
	if err := db.Instance().Get().Model(&models.Info{ID: id}).Updates(updated).Error; err != nil {
		return utils.ApiError{Code: utils.ErrModifyAndPubinfoFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	return utils.NewApiOkWrapper(nil)
}

// Sys/info godoc
// @Summary 获取发布信息列表
// @Description 需要权限：info:list
// @Tags sys
// @Accept  json
// @Produce  json
// @Param sortby query string  false "string enums" Enums(created_at,updated_at)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Param keyword query string false "搜索关键词，搜索标题"
// @Param pub_type query string false "发布类型，string enums" Enums(auto,manual)
// @Param company_id query int false "公司id"
// @Param status query int false "过滤状态， 默认全部" Enums(1,2,3,4)
// @Param platform query string false "目标平台，int enums, 默认-1所有，0--黄页88，1--八方资源" Enums(-1,0,1)
// @Param synced query int false "是否已同步到爱采购，int enums, 默认: 0--不查 1--已同步到爱采购" Enums(0,1)
// @Success 200 {array} models.Info
// @Security ApiKeyAuth
// @Router /sys/info/query [get]
func (c *InfoController) GetQuery() interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"info:list"}) {
		return nil
	}
	status := c.Ctx.URLParamIntDefault("status", dbtypes.InfoStatusNone)
	keyword := c.Ctx.URLParam("keyword")
	pubType := c.Ctx.URLParam("pub_type")
	platform := c.Ctx.URLParamIntDefault("platform", int(models.PlatformAll))
	synced := c.Ctx.URLParamIntDefault("synced", 0)

	sortby := c.Ctx.URLParamDefault("sortby", "created_at")
	order := c.Ctx.URLParamDefault("order", "desc")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	if cid, _ := c.Ctx.URLParamInt64("company_id"); cid > 0 {
		if *c.sysLoginInfo.Role == dbtypes.Role_Company_Adviser {
			var company models.Company
			if err := db.Instance().Get().Where("advisor_id = ? and id = ?", c.sysLoginInfo.SysUserId, cid).Find(&company).Error; errors.Is(err, gorm.ErrRecordNotFound) {
				return utils.ApiError{Code: utils.ErrScopeMissing.Error(), Msg: "权限不足"}.Response(c.Ctx)
			}
		}
		if infos, err := services.NewInfoService().GetInfos([]uint64{uint64(cid)}, status, keyword, pubType, sortby, order, limit, offset, models.PlatForm(platform), synced == 1, true); err != nil {
			return utils.ApiError{Code: utils.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
		} else {
			return utils.NewApiOkWrapper(infos)
		}
	} else {
		if infos, err := services.NewInfoService().GetInfos(nil, status, keyword, pubType, sortby, order, limit, offset, models.PlatForm(platform), synced == 1, true); err != nil {
			return utils.ApiError{Code: utils.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
		} else {
			return utils.NewApiOkWrapper(infos)
		}
	}
}

// Sys/info godoc
// @Summary 获取发布信息列表 带分页
// @Description 需要权限：info:list
// @Tags sys
// @Accept  json
// @Produce  json
// @Param sortby query string  false "string enums" Enums(created_at,updated_at)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Param keyword query string false "搜索关键词，搜索标题"
// @Param pub_type query string false "发布类型，string enums" Enums(auto,manual)
// @Param company_id query int false "公司id"
// @Param status query int false "过滤状态， 默认全部" Enums(1,2,3,4)
// @Param platform query string false "目标平台，int enums, 默认-1所有，0--黄页88，1--八方资源" Enums(-1,0,1)
// @Param synced query int false "是否已同步到爱采购，int enums, 默认: 0--不查 1--已同步到爱采购" Enums(0,1)
// @Success 200 {object} services.InfoPaginator
// @Security ApiKeyAuth
// @Router /sys/info/items [get]
func (c *InfoController) GetItems() interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"info:list"}) {
		return nil
	}
	status := c.Ctx.URLParamIntDefault("status", dbtypes.InfoStatusNone)
	keyword := c.Ctx.URLParam("keyword")
	pubType := c.Ctx.URLParam("pub_type")
	platform := c.Ctx.URLParamIntDefault("platform", int(models.PlatformAll))
	synced := c.Ctx.URLParamIntDefault("synced", 0)

	sortby := c.Ctx.URLParamDefault("sortby", "created_at")
	order := c.Ctx.URLParamDefault("order", "desc")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	if cid, _ := c.Ctx.URLParamInt64("company_id"); cid > 0 {
		if *c.sysLoginInfo.Role == dbtypes.Role_Company_Adviser {
			var company models.Company
			if err := db.Instance().Get().Where("advisor_id = ? and id = ?", c.sysLoginInfo.SysUserId, cid).Find(&company).Error; errors.Is(err, gorm.ErrRecordNotFound) {
				return utils.ApiError{Code: utils.ErrScopeMissing.Error(), Msg: "权限不足"}.Response(c.Ctx)
			}
		}
		if infos, err := services.NewInfoService().GetInfosWithPaginator([]uint64{uint64(cid)}, status, keyword, pubType, sortby, order, limit, offset, models.PlatForm(platform), synced == 1, true); err != nil {
			return utils.ApiError{Code: utils.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
		} else {
			return utils.NewApiOkWrapper(infos)
		}
	} else {
		if infos, err := services.NewInfoService().GetInfosWithPaginator(nil, status, keyword, pubType, sortby, order, limit, offset, models.PlatForm(platform), synced == 1, true); err != nil {
			return utils.ApiError{Code: utils.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
		} else {
			return utils.NewApiOkWrapper(infos)
		}
	}
}

// Sys/info godoc
// @Summary 获取发布信息详情
// @Description 需要权限：info:list
// @Tags sys
// @Accept  json
// @Produce  json
// @Param id path int false "信息id"
// @Success 200 {object} models.Info
// @Security ApiKeyAuth
// @Router /sys/info/{id} [get]
func (c *InfoController) GetBy(id int) interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"info:get"}) {
		return nil
	}
	if data, err := services.NewInfoService().Find(id); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}
	} else {
		return utils.NewApiOkWrapper(data)
	}
}

func (c *InfoController) BeginRequest(ctx context.Context) {
	c.sysLoginInfo = controllers.RetriveSysLoginInfo(ctx)
}

func (c *InfoController) EndRequest(ctx context.Context) {
}
