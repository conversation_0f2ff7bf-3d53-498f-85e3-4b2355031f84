package admin

import (
	"git.paihang8.com/lib/goutils/sites/b2b168"
	"github.com/go-playground/validator/v10"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/controllers"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/cat"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
)

type CompanyController struct {
	Ctx          context.Context
	sysLoginInfo controllers.SysLoginInfo
}

func HandleCompany(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(CompanyController))
}

// sys/company godoc
// @Summary 审核、修改企业信息
// @Description 需要权限：company:modify
// @Description 仅需传输auditing_fields 和 id两个字段，系统将修改id对应的company信息依据auditing_fields
// @Tags sys
// @Accept  json
// @Produce  json
// @Param body body schemas.CheckModifyCompany true "CheckModifyCompany"
// @Success 200 {object} models.Company
// @Security ApiKeyAuth
// @Router /sys/company/check_pass [post]
func (c *CompanyController) PostCheck_pass() interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"company:modify"}) {
		return nil
	}
	var input schemas.CheckModifyCompany
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return configs.ApiError{
			Code: configs.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	var validate *validator.Validate
	validate = validator.New()
	if err := validate.Struct(input); err != nil {
		return configs.ApiError{
			Code: configs.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	var company models.Company
	if err := db.Instance().Get().Find(&company, input.ID).Error; err != nil {
		return configs.ApiError{Code: configs.ErrCheckModifyCompanyFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	if err := dbtypes.UpdateModelFromMap(&company, input.AuditingFields); err != nil {
		return configs.ApiError{Code: configs.ErrUpdateModelError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	if err := db.Instance().Get().Save(&company).Error; err != nil {
		return configs.ApiError{Code: configs.ErrCheckModifyCompanyFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	return configs.NewApiOkWrapper(company)
}

// sys/company godoc
// @Summary 审核拒绝公司修改
// @Description 需要权限：company:modify
// @Tags sys
// @Accept  json
// @Produce  json
// @Param companyid path int true "companyid"
// @Param body body schemas.AuditNotPass true "AuditNotPass"
// @Success 200 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /sys/company/check_not_pass/{companyid} [put]
func (c *CompanyController) PutCheck_not_passBy(companyid uint64) interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"company:modify"}) {
		return nil
	}
	var input schemas.AuditNotPass
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return configs.ApiError{
			Code: configs.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	var validate = validator.New()
	if err := validate.Struct(input); err != nil {
		return configs.ApiError{
			Code: configs.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	updatedCompany := map[string]interface{}{}
	updatedCompany["auditing_fields"] = nil
	updatedCompany["audit_res"] = input.AuditRes
	if err := db.Instance().Get().Model(&models.Company{ID: companyid}).Updates(updatedCompany).Error; err != nil {
		return configs.ApiError{Code: configs.ErrModifyCompanyFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	return configs.NewApiOkWrapper(nil)
}

// sys/company godoc
// @Summary [搜索]获取[待审核]公司列表
// @Description 需要权限：company:get 或者 company:list
// @Tags sys
// @Accept  json
// @Produce  json
// @Param sortby query string  false "string enums" Enums(created_at,updated_at)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Param keyword query string false "搜索关键词，搜索公司名"
// @Param wait_audit query bool false "是否只过滤等待审核的公司， string default" default(true)
// @Success 200 {array} models.Company
// @Security ApiKeyAuth
// @Router /sys/company/query [get]
func (c *CompanyController) GetQuery() interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"user:get", "user:list", "company:get", "company:list"}) {
		return nil
	}
	waitAudit := false
	if pwaitAudit, err := c.Ctx.URLParamBool("wait_audit"); err != nil || pwaitAudit {
		waitAudit = true
	}
	var advisorId uint64
	if *c.sysLoginInfo.Role == dbtypes.Role_Company_Adviser {
		advisorId = c.sysLoginInfo.SysUserId
	}
	keyword := c.Ctx.URLParam("keyword")
	sortby := c.Ctx.URLParamDefault("sortby", "created_at")
	order := c.Ctx.URLParamDefault("order", "desc")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	if companies, err := services.NewCompanyService().Search(keyword, waitAudit, advisorId, sortby, order, limit, offset); err != nil {
		return configs.ApiError{
			Code: configs.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return configs.NewApiOkWrapper(companies)
	}
}

// Company godoc
// @Summary 获取公司详情
// @Description 获取公司详情, 只能获取当前用户的
// @Tags sys
// @Produce  json
// @Param id path int true "id"
// @Success 200 {object} models.Company
// @Security ApiKeyAuth
// @Router /sys/company/{id} [get]
func (c *CompanyController) GetBy(id uint64) interface{} {
	if item, err := services.NewCompanyService().Find(id); err != nil {
		return configs.ApiError{Code: configs.ErrInputParamError.Error(), Msg: err.Error()}.Response(c.Ctx)
	} else {
		item.AreaNames = services.Area.FindNames(item.AreaIds)
		item.CateNames, _ = cat.NewCatService().FindNames(item.Cate)
		//item.CanEdit = !services.Merchant.InCertApproving(id)
		item.CanEdit = true
		if !item.CanEdit {
			item.Reason = "企业认证中"
		}
		return configs.NewApiOkWrapper(item)
	}
}

// Company godoc
// @Summary 提交/修改公司信息
// @Description 修改那个字段，提交那个字段, 直接修改
// @Tags sys
// @Accept  json
// @Produce  json
// @Param id path int true "id"
// @Param body body models.Company true "Company"
// @Success 200 {object} models.Company
// @Security ApiKeyAuth
// @Router /sys/company/{id} [put]
func (c *CompanyController) PutBy(id uint64) interface{} {

	var input models.Company
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return configs.ApiError{Code: configs.ErrAddProductFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	if err := b2b168.Validate.Struct(input); err != nil {
		errStr := b2b168.Translate(err.(validator.ValidationErrors))
		return configs.ApiError{
			Code: configs.ErrInputParamError.Error(),
			Msg:  errStr,
		}.Response(c.Ctx)
	}

	if company, err := services.NewCompanyService().Find(id); err != nil {
		return configs.ApiError{Code: configs.ErrModifyCompanyFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	} else {
		m := map[string]interface{}{}
		_ = c.Ctx.ReadJSON(&m)
		delete(m, "id")
		company.AuditingFields = nil
		for k, _ := range m {
			company.AuditingFields = append(company.AuditingFields, k)
		}
		if err := dbtypes.UpdateModelFromMap(company, m); err != nil {
			return configs.ApiError{Code: configs.ErrUpdateModelError.Error(), Msg: err.Error()}.Response(c.Ctx)
		}

		if err := db.Instance().Get().Model(company).Save(company).Error; err != nil {
			return configs.ApiError{configs.ErrModifyCompanyFailed.Error(), err.Error(), nil}.Response(c.Ctx)
		}
		go controllers.OnCompanyEdit(company)
		return configs.NewApiOkWrapper(company)
	}

}

func (c *CompanyController) BeginRequest(ctx context.Context) {
	c.sysLoginInfo = controllers.RetriveSysLoginInfo(ctx)
}

func (c *CompanyController) EndRequest(ctx context.Context) {
}
