package admin

import (
	"errors"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	_ "gitlab.com/all_publish/api/internal/business/eventtrigger"
	"gitlab.com/all_publish/api/internal/controllers"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/convert"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
)

type AicaigouController struct {
	Ctx          context.Context
	sysLoginInfo controllers.SysLoginInfo
}

func HandleAicaigou(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(AicaigouController))
}

// Aicaigou godoc
// @Summary 添加爱采购资料
// @Description 添加爱采购资料
// @Description 需要先完善公司资料，注册好搜了网
// @Tags sys
// @Accept  json
// @Produce  json
// @Param companyId path int true "companyId"
// @Param body body models.AicaigouUsers true "AicaigouUsers"
// @Success 200 {object} models.AicaigouUsers
// @Security ApiKeyAuth
// @Router /sys/{companyId}/aicaigou [post]
func (c *AicaigouController) Post() interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"aicaigou:add", "aicaigou:modify"}) {
		return nil
	}
	var item models.AicaigouUsers
	companyID, _ := c.Ctx.Params().GetUint64("companyId")
	if companyID <= 0 {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: "公司ID错误"}.Response(c.Ctx)
	}
	if err := c.Ctx.ReadJSON(&item); err != nil {
		return utils.ApiError{Code: utils.ErrAddAicaigouFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	mch, err := services.Merchant.GetByCompanyId(companyID, models.PlatformSoleExt)
	if err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	if mch.TargetCompanyID == 0 {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  "请先完善公司资料, 注册搜了网",
		}.Response(c.Ctx)
	}
	m := map[string]interface{}{}
	c.Ctx.ReadJSON(&m)
	if err := models.Valiate(item); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	if err := c.Validate(item); err != nil {
		return utils.ApiError{Code: utils.ErrAddAicaigouFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	svc := services.NewAicaigouUser(db.Instance().Get())
	item.CompanyID = companyID
	if data, err := svc.Find(companyID); err == nil && data.CompanyID > 0 {
		return utils.ApiError{Code: utils.ErrAddAicaigouFailed.Error(),
			Msg: "已添加过, 请编辑"}.Response(c.Ctx)
	} else if err := svc.Insert(&item); err != nil {
		return utils.ApiError{Code: utils.ErrAddAicaigouFailed.Error(),
			Msg: err.Error()}.Response(c.Ctx)
	} else {
		triger, _ := models.GetTrigger(models.PlatformSoleExt)
		com, _ := services.NewCompanyService().Find(companyID)
		go triger.AutoFlow(mch, com, true)
		return utils.NewApiOkWrapper(item)
	}

}

// Aicaigou godoc
// @Summary 提交/修改爱采购用户信息
// @Description 修改那个字段，提交那个字段, 直接修改
// @Tags sys
// @Accept  json
// @Produce  json
// @Param companyId path int true "companyId"
// @Param body body models.AicaigouUsers true "Aicaigou"
// @Success 200 {object} models.AicaigouUsers
// @Security ApiKeyAuth
// @Router /sys/{companyId}/aicaigou [put]
func (c *AicaigouController) Put() interface{} {
	var input models.AicaigouUsers
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return utils.ApiError{Code: utils.ErrEditAicaigouFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	if err := models.Valiate(input); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	companyID, _ := c.Ctx.Params().GetUint64("companyId")
	if companyID <= 0 {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: "公司ID错误"}.Response(c.Ctx)
	}
	svc := services.NewAicaigouUser(db.Instance().Get())
	if old, err := svc.Find(companyID); err != nil {
		return utils.ApiError{Code: utils.ErrEditAicaigouFailed.Error(), Msg: "请先添加资料"}.Response(c.Ctx)
	} else {
		m := map[string]interface{}{}
		_ = c.Ctx.ReadJSON(&m)
		if err := dbtypes.UpdateModelFromMap(old, m); err != nil {
			return utils.ApiError{Code: utils.ErrUpdateModelError.Error(), Msg: err.Error()}.Response(c.Ctx)
		}

		if err := c.Validate(*old); err != nil {
			return utils.ApiError{Code: utils.ErrAddAicaigouFailed.Error(), Msg: err.Error(), Data: nil}.Response(c.Ctx)
		}
		if err := svc.Save(old); err != nil {
			return utils.ApiError{utils.ErrEditAicaigouFailed.Error(), err.Error(), nil}.Response(c.Ctx)
		}
		com, _ := services.NewCompanyService().Find(companyID)
		triger, _ := models.GetTrigger(models.PlatformSoleExt)
		mch, _ := services.Merchant.GetByCompanyId(companyID, models.PlatformSoleExt)
		go triger.AutoFlow(mch, com, true)
		return utils.NewApiOkWrapper(nil)
	}

}

// Aicaigou godoc
// @Summary 保存爱采购用户信息
// @Description 已有就是编辑，没有就是保存
// @Tags sys
// @Accept  json
// @Produce  json
// @Param body body models.AicaigouUsers true "Aicaigou"
// @Success 200 {object} models.AicaigouUsers
// @Security ApiKeyAuth
// @Router /sys/{companyId}/aicaigou/save [post]
func (c *AicaigouController) PostSave() interface{} {
	svc := services.NewAicaigouUser(db.Instance().Get())
	companyID, _ := c.Ctx.Params().GetUint64("companyId")
	if old, err := svc.Find(companyID); err != nil || old.CompanyID == 0 {
		return c.Post()
	} else {
		return c.Put()
	}
}

// Aicaigou godoc
// @Summary 获取爱采购用户详情
// @Description 获取爱采购用户详情, 只能获取当前用户的
// @Tags sys
// @Produce  json
// @Param companyId path int true "companyId"
// @Success 200 {object} models.AicaigouUsers
// @Security ApiKeyAuth
// @Router /sys/{companyId}/aicaigou [get]
func (c *AicaigouController) Get() interface{} {
	svc := services.NewAicaigouUser(db.Instance().Get())
	companyID, _ := c.Ctx.Params().GetUint64("companyId")
	if companyID <= 0 {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: "公司ID错误"}.Response(c.Ctx)
	}
	if item, err := svc.Find(companyID); err != nil {
		return utils.ApiError{Code: utils.ErrNoDataError.Error(), Msg: err.Error()}.Response(c.Ctx)
	} else if item.CompanyID > 0 {
		return utils.NewApiOkWrapper(*item)
	} else {
		return utils.NewApiOkWrapper(map[string]string{})
	}
}

func (c *AicaigouController) BeginRequest(ctx context.Context) {
	c.sysLoginInfo = controllers.RetriveSysLoginInfo(ctx)
}

func (c *AicaigouController) EndRequest(ctx context.Context) {
}

func (c *AicaigouController) Validate(aicaigou models.AicaigouUsers) error {
	if len(aicaigou.CompanyAreaIds) < 2 {
		return errors.New("公司地址不完整")
	}
	if len(aicaigou.BankAreaIds) < 2 {
		return errors.New("银行地址不完整")
	}
	_, err := services.Area.GetMapping(convert.Str(aicaigou.CompanyAreaIds[0]).MustUInt64(), models.PlatformSoleExt)
	if err != nil {
		return errors.New("公司地址:省找不到数据")
	}

	_, err = services.Area.GetMapping(convert.Str(aicaigou.CompanyAreaIds[1]).MustUInt64(), models.PlatformSoleExt)
	if err != nil {
		return errors.New("公司地址:市找不到数据")
	}

	_, err = services.Area.GetMapping(convert.Str(aicaigou.BankAreaIds[0]).MustUInt64(), models.PlatformSoleExt)
	if err != nil {
		return errors.New("银行地址:省找不到数据")
	}

	_, err = services.Area.GetMapping(convert.Str(aicaigou.BankAreaIds[1]).MustUInt64(), models.PlatformSoleExt)
	if err != nil {
		return errors.New("银行地址:市找不到数据")
	}
	return nil
}
