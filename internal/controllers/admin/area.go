package admin

import (
	_ "git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/business/merchanter"
	"gitlab.com/all_publish/api/internal/models"
)

type AreaController struct {
	Ctx context.Context
}

func HandleArea(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(AreaController))
}

// Area godoc
// @Summary 根据父级id获取下一级地区
// @Description 传0获取省份
// @Description 不用接口，用js的方式:  http://api.huangye88.com/js/apiarea.js
// @Tags deprecated
// @Produce  json
// @Param pid path int true "int"
// @Success 200 {array} publisher.Area
// @Security ApiKeyAuth
// @Router /sys/areas/{pid} [get]
func (c *AreaController) GetBy(pid int) interface{} {
	mClient, _ := models.GetMerchant(models.PlatformHy88)
	if data, err := mClient.(*merchanter.Hy88Merchant).GetAreasByPid(pid); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx, true)
	} else {
		return utils.NewApiOkWrapper(data)
	}
}
