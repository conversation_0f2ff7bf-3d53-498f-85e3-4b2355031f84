package admin

import (
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/controllers"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/cat"
	ps "gitlab.com/all_publish/api/internal/services/product"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gopkg.in/go-playground/validator.v9"
)

type ProductController struct {
	Ctx          context.Context
	sysLoginInfo controllers.SysLoginInfo
}

func HandleProduct(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(ProductController))
}

// Product godoc
// @Summary 审核、修改产品信息
// @Description 需要权限：product:modify
// @Description 仅需传输需要修改的部分
// @Tags sys
// @Accept  json
// @Produce  json
// @Param productId path int true "productId"
// @Param body body models.Product true "Product"
// @Success 200 {object} models.Product
// @Security ApiKeyAuth
// @Router /sys/product/check_pass/{productId} [put]
func (c *ProductController) PutCheck_passBy(productId int) interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"product:modify"}) {
		return nil
	}
	var input models.Product
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	var validate = validator.New()
	if err := validate.Struct(input); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	var rawInput map[string]interface{}
	c.Ctx.ReadJSON(&rawInput)
	if product, err := ps.NewProductService().UpdateStatusToPassed(productId, rawInput); err != nil {
		return utils.ApiError{Code: utils.ErrCheckModifyProductFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(product)
	}
}

// Product godoc
// @Summary 直接修改产品信息
// @Description 需要权限：product:modify
// @Description 仅需传输需要修改的部分
// @Tags sys
// @Accept  json
// @Produce  json
// @Param productId path int true "productId"
// @Param body body models.Product true "Product"
// @Success 200 {object} models.Product
// @Security ApiKeyAuth
// @Router /sys/product/{productId} [put]
func (c *ProductController) PutBy(productId int) interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"product:modify"}) {
		return nil
	}
	var product models.Product
	if err := c.Ctx.ReadJSON(&product); err != nil {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	var validate = validator.New()
	if err := validate.Struct(product); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	var rawInput map[string]interface{}
	c.Ctx.ReadJSON(&rawInput)
	if err := db.Instance().Get().Find(&product, productId).Error; err != nil {
		return utils.ApiError{Code: utils.ErrAddProductFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	if err := dbtypes.UpdateModelFromMap(&product, rawInput); err != nil {
		return utils.ApiError{
			Code: utils.ErrUpdateModelError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	product.UpdatedAt = dbtypes.SHNow()
	if err := ps.NewProductService().Updates(&product, rawInput); err != nil {
		return utils.ApiError{Code: utils.ErrAddProductFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(product)
	}
}

// Product godoc
// @Summary 审核拒绝产品
// @Description 需要权限：product:modify\
// @Tags sys
// @Accept  json
// @Produce  json
// @Param productId path int true "productId"
// @Param body body schemas.AuditNotPass true "AuditNotPass"
// @Success 200 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /sys/product/check_not_pass/{productId} [put]
func (c *ProductController) PutCheck_not_passBy(productId uint64) interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"product:modify"}) {
		return nil
	}
	var input schemas.AuditNotPass
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	var validate = validator.New()
	if err := validate.Struct(input); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	}
	if product, err := ps.NewProductService().Get(productId, false); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		ps.NewProductService().UpdateStatusToNotPass(product, input.AuditRes, input.AuditRes2)
		return utils.NewApiOkWrapper(nil)
	}
}

// Product godoc
// @Summary 重置为草稿
// @Description 需要权限：product:modify\
// @Tags sys
// @Accept  json
// @Produce  json
// @Param productId path int true "productId"
// @Success 200 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /sys/product/check_to_draft/{productId} [put]
func (c *ProductController) PutCheck_to_draftBy(productId uint64) interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"product:modify"}) {
		return nil
	}
	var product models.Product
	if err := db.Instance().Get().Find(&product, productId).Error; err != nil {
		return utils.ApiError{Code: utils.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}

	if *c.sysLoginInfo.Role == dbtypes.Role_Company_Adviser {
		var company models.Company
		if err := db.Instance().Get().Find(&company, product.CompanyID).Error; err != nil {
			return utils.ApiError{Code: utils.ErrOPDataBaseError.Error(), Msg: err.Error()}.Response(c.Ctx)
		}
		if company.AdvisorID != c.sysLoginInfo.SysUserId {
			return utils.ApiError{
				Code: utils.ErrScopeMissing.Error(),
				Msg:  "无权限操作此产品",
			}.Response(c.Ctx)
		}
	}

	if err := db.Instance().Get().Model(&product).UpdateColumns(models.Product{
		Status: dbtypes.ProductStatusDraft,
		Base: dbtypes.Base{
			UpdatedAt: dbtypes.SHNow(),
		},
	}).Error; err != nil {
		return utils.ApiError{Code: utils.ErrCheckModifyProductFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	return utils.NewApiOkWrapper(nil)
}

// Product godoc
// @Summary [搜索]获取[待审核]产品列表
// @Description 需要权限：product:get 或者 product:list
// @Tags sys
// @Accept  json
// @Produce  json
// @Param sortby query string  false "string enums" Enums(created_at,updated_at)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Param keyword query string false "搜索关键词，搜索产品名或描述"
// @Param company_id query int false "公司id"
// @Param status query int false "过滤状态， 默认全部" Enums(1,2,3,4)
// @Success 200 {array} schemas.AdminProduct
// @Security ApiKeyAuth
// @Router /sys/product/query [get]
func (c *ProductController) GetQuery() interface{} {
	if !controllers.HasPermission(c.Ctx, []string{"product:get", "product:list"}, true) {
		return nil
	}
	var products []models.Product
	// Get all matched records
	status := c.Ctx.URLParamIntDefault("status", dbtypes.ProductStatusNone)
	keyword := c.Ctx.URLParam("keyword")

	sortby := c.Ctx.URLParamDefault("sortby", "updated_at")
	order := c.Ctx.URLParamDefault("order", "asc")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	cid, _ := c.Ctx.URLParamInt64("company_id")
	var cids []uint64
	if cid > 0 {
		cids = append(cids, uint64(cid))
	} else if *c.sysLoginInfo.Role == dbtypes.Role_Company_Adviser {
		cids, _ = services.NewCompanyService().GetCompanyIdsOfAdvisor(c.sysLoginInfo.SysUserId)
		if len(cids) == 0 {
			return utils.NewApiOkWrapper(products)
		}
	}

	if products, err := ps.NewProductService().Search(keyword, cids, status, sortby, order, limit, offset); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		var ret []schemas.AdminProduct
		ps, _ := services.NewUserPlatformService().GetAutoPostedPlatForms(uint64(cid))
		for _, v := range products {
			v.SetPlatformsByforms(ps)
			ret = append(ret, schemas.AdminProduct{
				Base:         v.Base,
				ID:           v.ID,
				Mode:         uint8(v.Mode),
				Name:         v.Name,
				Phone:        v.User.Phone,
				Status:       v.Status,
				AuditRes:     v.AuditRes,
				AuditRes2:    v.AuditRes2,
				ModifyFields: v.ModifyFields,
				CompanyID:    v.CompanyID,
				ChangedAt:    v.ChangedAt,
				Company: schemas.Company{
					Base:        v.Company.Base,
					ID:          v.Company.ID,
					Name:        v.Company.Name,
					AdvisorID:   v.Company.AdvisorID,
					Cate:        v.Company.Cate,
					ContactName: v.Company.ContactName,
					Phone:       v.Company.Phone,
				},
				Platforms: v.Platforms,
			})
		}
		return utils.NewApiOkWrapper(ret)
	}
}

// Product godoc
// @Summary 获取产品统计信息
// @Description 获取产品统计信息
// @Tags sys
// @Produce  json
// @Param company_id query int false "公司id"
// @Success 200 {object} 	schemas.StatusCount
// @Security ApiKeyAuth
// @Router /sys/product/stat [get]
func (c *ProductController) GetStat() interface{} {
	cid, _ := c.Ctx.URLParamInt64("company_id")
	if data, err := ps.NewProductService().CountByStatusOfCompany(uint64(cid)); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(data)
	}
}

// Product godoc
// @Summary 获取产品详情
// @Description 获取产品详情
// @Tags sys
// @Produce  json
// @Param productId path int true "productId"
// @Param check query int false "int enums" Enums(0,1)
// @Success 200 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /sys/product/{productId} [get]
func (c *ProductController) GetBy(productId int) interface{} {
	check := c.Ctx.URLParamIntDefault("check", 0)
	if product, err := ps.NewProductService().Get(uint64(productId), true); err != nil {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(c.Ctx)
	} else {
		product.AreaNames = services.Area.FindNames(product.AreaIds)
		product.CateNames, _ = cat.NewCatService().FindNames(product.Cate)
		if ps, err := services.NewUserPlatformService().GetAutoPostedPlatForms(product.CompanyID); err == nil {
			product.SetPlatformsByforms(ps)
		}
		if check == 1 {
			ps.NewProductService().CalBadWords(product)
		}
		return utils.NewApiOkWrapper(product)
	}
}

func (c *ProductController) BeginRequest(ctx context.Context) {
	c.sysLoginInfo = controllers.RetriveSysLoginInfo(ctx)
}

func (c *ProductController) EndRequest(ctx context.Context) {
}
