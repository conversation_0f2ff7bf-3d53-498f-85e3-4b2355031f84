package controllers

import (
	"encoding/base64"
	"errors"
	"net/http"
	"regexp"
	"strconv"
	"strings"

	"github.com/iris-contrib/middleware/jwt"
	"github.com/kataras/iris/v12"
	"gitlab.com/all_publish/api/configs"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/business/merchanter"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/service"
	"gitlab.com/all_publish/api/pkg/convert"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gopkg.in/go-playground/validator.v9"
	"gorm.io/gorm"
)

var validate *validator.Validate

const userKey = "user"

func init() {
	validate = validator.New()
	validate.RegisterStructValidation(schemas.LoginStructLevelValidation, schemas.Login{})
}

// Auth godoc
// @Summary 登陆认证接口
// @Description 登陆认证, 黄页88登录使用mobile(grant_type=user), 系统用户使用username(grant_type=system)
// @Tags client,sys
// @Accept  json
// @Produce  json
// @Param body body schemas.Login true "login"
// @Success 200 {object} schemas.LoginResult
// @Router /auth [post]
func AuthHandler(ctx iris.Context) {
	var login schemas.Login
	var callback func() = func() {

	}
	defer func() {
		go callback()
	}()

	if err := ctx.ReadJSON(&login); err != nil {
		utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(ctx)
		return
	}
	if err := validate.Struct(login); err != nil {
		ctx.StatusCode(http.StatusBadRequest)
		utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(ctx)
		return
	}

	switch login.GrantType {
	case "":
		fallthrough
	case "user":
		if result, _ := regexp.MatchString(`^[0-9]{11}$`, login.Mobile); !result {
			utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: "用户名错误"}.Response(ctx, true)
			return
		}
		if mClient, ok := models.GetMerchant(models.PlatformHy88); ok {
			apiUser, err := mClient.(*merchanter.Hy88Merchant).Login(login.Mobile, login.Password)
			u := models.User{}
			if err != nil {
				utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(ctx, true)
				return
			}
			if err := db.Instance().Get().Model(&models.UserLoginHistory{}).Create(&models.UserLoginHistory{
				OemId:       apiUser.OemId,
				Phone:       convert.Str(login.Mobile).MustInt(),
				CompanyId:   apiUser.CompanyID,
				LastLoginIp: ctx.RemoteAddr(),
				Version:     ctx.GetHeader("version"),
			}).Error; err != nil {
				utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(ctx, true)
				return
			}
			//HY-8571
			if up, err := services.NewUserService().GetUserByOem(apiUser.OemId); err == nil {
				u = *up
				u.Password = login.Password
				u.PostsMap = apiUser.PostsMap
				u.DigKeywordLimit = apiUser.DigKeywordLimit
				u.DigMaterialsLimit = apiUser.DigMaterialsLimit
				u.RankExpireTime = apiUser.RankExpireTime
				u.PostExpireTime = apiUser.PostExpireTime
				u.SeekExpireTime = apiUser.SeekExpireTime
				//更新用户
				if err := db.Instance().Get().Model(&u).UpdateColumns(models.User{
					Phone:          apiUser.Phone,
					Username:       apiUser.Username,
					LastLoginAt:    dbtypes.SHNow(),
					ExpireTime:     apiUser.ExpireTime,
					RankExpireTime: apiUser.RankExpireTime,
					PostExpireTime: apiUser.PostExpireTime,
					SeekExpireTime: apiUser.SeekExpireTime,
					RankBuyTime:    apiUser.RankBuyTime,
					PostBuyTime:    apiUser.PostBuyTime,
					SeekBuyTime:    apiUser.SeekBuyTime,
					Base: dbtypes.Base{
						UpdatedAt: dbtypes.SHNow(),
					},
				}).Error; err != nil {
					utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(ctx)
					return
				}

				if c, e := services.NewCompanyService().Find(u.CompanyID); e == nil {
					changed := false
					if u.Company.Site != c.Site {
						changed = true
					}
					u.Company = *c
					if changed {
						services.NewCompanyService().Save(c)
					}
				} else {
					utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(ctx)
					return
				}
				callback = func() {
					OnUserLogin(&u, mClient)
				}

			} else if errors.Is(err, gorm.ErrRecordNotFound) {
				//创建用户, 有可能是换了登录手机号。
				u = apiUser
				u.Password = login.Password
				company, err := mClient.(*merchanter.Hy88Merchant).GetCompanyInfo(strconv.Itoa(apiUser.OemId))
				if err != nil {
					utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(ctx)
					return
				}
				u.CreatedAt = dbtypes.SHNow()
				u.UpdatedAt = dbtypes.SHNow()
				u.LastLoginAt = dbtypes.SHNow()
				u.Phone = login.Mobile
				u.MaxProducts = dbtypes.MaxProducts
				u.Company = company
				person, err := mClient.(*merchanter.Hy88Merchant).GetPersonInfo(strconv.Itoa(apiUser.OemId))
				if err != nil {
					utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(ctx)
					return
				}
				qq := strconv.FormatInt(int64(person.Qq), 10)
				u.Company.Qq = &qq
				if err := db.Instance().Get().Create(&u).Error; err != nil {
					utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(ctx)
					return
				}
				u.CompanyID = u.Company.ID
				callback = func() {
					OnUserCreated(&u, mClient)
				}

			} else {
				utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(ctx)
				return
			}
			shouldBindKuyiso := apiUser.FromKuyiso
			if apiUser.FromKuyiso {
				if v, err := services.Merchant.GetByCompanyId(u.CompanyID, models.PlatformKuyiso); !errors.Is(err, gorm.ErrRecordNotFound) && v != nil && v.CertStatus == dbtypes.CertStatusPassed {
					shouldBindKuyiso = false
				}
			}
			ctx.Values().Set(userKey, jwt.MapClaims{
				"user_id":          u.ID,
				"cid":              u.CompanyID,
				"oem_id":           u.OemId,
				"shouldBindKuyiso": shouldBindKuyiso,
			})
			ctx.Next()
		}

	case "system":
		if systemUser, err := services.NewSystemUserService().GetUserByName(login.Username); err != nil {
			utils.ApiError{Code: utils.ErrAuthFailed.Error(), Msg: "用户名或密码错误"}.Response(ctx)
			return
		} else {
			if !VerifyPassword(login.Username, login.Password, systemUser.Password) {
				utils.ApiError{Code: utils.ErrAuthFailed.Error(), Msg: "用户名或密码错误"}.Response(ctx)
				return
			}
			if systemUser.Ban {
				utils.ApiError{Code: utils.ErrAuthFailed.Error(), Msg: "账户被禁用，请联系管理员"}.Response(ctx)
				return
			}
			now := dbtypes.SHNow()
			systemUser.LastLoginAt = &now
			db.Instance().Get().Save(&systemUser)
			if systemUser.Role != nil {
				ctx.Values().Set(userKey, jwt.MapClaims{
					"user_id": systemUser.ID,
					"scopes":  systemUser.Scopes,
					"role":    *systemUser.Role,
				})
			} else {
				ctx.Values().Set(userKey, jwt.MapClaims{
					"user_id": systemUser.ID,
					"scopes":  systemUser.Scopes,
				})
			}

			ctx.Next()
		}

	default:
		utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  "unsurporrted GrantType:" + login.GrantType,
		}.Response(ctx)
	}

}

func AuthResponseHandler(ctx iris.Context) {
	if u, ok := ctx.Values().Get(userKey).(jwt.MapClaims); ok {
		var loginResult schemas.LoginResult
		if _, ok := u["scopes"]; ok {
			scopes := []string(u["scopes"].(dbtypes.JSONArray))
			u["is_system"] = true
			loginResult.Scopes = append(scopes, "sys")
			u["scopes"] = loginResult.Scopes
		}
		if _, ok := u["role"]; ok {
			loginResult.Role = u["role"].(string)
		}
		if oemId, ok := u["oem_id"]; ok {
			loginResult.OemId = uint64(oemId.(int))
		}
		if fromkuyiso, ok := u["fromkuyiso"]; ok {
			loginResult.FromKuyiso = fromkuyiso.(bool)
		}
		u["exp"] = dbtypes.SHNow().Unix() + configs.ApiConfig.Web.JWTExpDelta
		ctx.Values().Save(userKey, u, false)
		token := jwt.NewTokenWithClaims(jwt.SigningMethodHS256, u)
		tokenString, _ := token.SignedString([]byte(configs.ApiConfig.Web.JWTSecret))
		loginResult.AccessToken = tokenString
		loginResult.Exp = u["exp"].(int64)
		loginResult.Id = u["user_id"].(uint64)
		_, _ = ctx.JSON(loginResult)
	}
	ctx.Next()
}

func JwtErrorHandler(ctx iris.Context, err error) {
	if err == jwt.ErrTokenExpired {
		utils.ApiError{Code: utils.ErrAuthFailed.Error(), Msg: "认证过期，请重新登陆"}.Response(ctx, true)
	} else {
		utils.ApiError{Code: utils.ErrAuthFailed.Error(), Msg: "未认证"}.Response(ctx, true)
	}
}

func CheckVersionHandler(ctx iris.Context) {
	cRoute := ctx.GetCurrentRoute()
	if cRoute != nil {
		versions := ctx.GetHeader("version")
		if versions != "" {
			if result, err := service.VersionCompare(versions, 0, 2, 42); err == nil {
				if result == 0 {
					ctx.Next()
					return
				}
			}

			if result, err := service.VersionCompare(versions, 0, 2, 95); err == nil {
				if result <= 0 {
					utils.ApiError{Code: utils.ErrVersionTooLow.Error(), Msg: "发发助手客户端已暂停维护，继续使用请前往发发助手网页版（ffzs.paihang8.com"}.Response(ctx, true)
					return
				}
			}
		}
		ctx.Next()
	} else {
		ctx.Next()
	}
}

func ValidatePermissionHandler(ctx iris.Context) {
	cRoute := ctx.GetCurrentRoute()
	if cRoute != nil {
		whileList := []string{
			"/auth",
			"/swagger",
			"/static",
			"/debug",
			"/v2/areas",
			"/v2/category",
			"/openapi",
		}
		inWhile := false
		for _, p := range whileList {
			if strings.HasPrefix(cRoute.Path(), p) {
				inWhile = true
				break
			}
		}
		if inWhile {
			if strings.HasPrefix(cRoute.Path(), "/swagger") {
				if configs.ApiConfig.Swagger.User == "" || configs.ApiConfig.Swagger.Password == "" {
					ctx.Next()
				} else {
					token := ctx.Request().Header.Get("Authorization")
					if token != "" {
						expected := configs.ApiConfig.Swagger.User + ":" + configs.ApiConfig.Swagger.Password
						expected = base64.StdEncoding.EncodeToString([]byte(expected))
						if token[6:] == expected {
							ctx.Next()
							return
						}
					}
					ctx.StatusCode(http.StatusUnauthorized)
					ctx.ResponseWriter().Header().Set("WWW-Authenticate", `Basic realm="需要登陆`)
				}
			} else {
				ctx.Next()
			}
		} else {
			j := ctx.Application().ConfigurationReadOnly().GetOther()["jwt"].(*jwt.Middleware)
			if err := j.CheckJWT(ctx); err != nil {
				j.Config.ErrorHandler(ctx, err)
				return
			} else {
				ctx.Next()
			}
		}
	} else {
		ctx.Next()
	}
}

// other godoc
// @Summary 上传文件
// @Description 支持JPG PNG两种格式
// @Tags deprecated
// @Accept  multipart/form-data
// @Produce  json
// @Param   file formData file true  "jpg/png file"
// @Success 200 {string}  string "url_path"
// @Failure 400 {object} configs.ApiError
// @Failure 401 {object} configs.ApiError
// @Security ApiKeyAuth
// @Router /upload/img [post]
// func UploadHandler(ctx iris.Context) {
// 	ctx.Request().ParseMultipartForm(1024 * 1024 * 10)
// 	if ctx.Request().MultipartForm != nil {
// 		if fhs := ctx.Request().MultipartForm.File; fhs != nil {
// 			if fileHeaders, ok := fhs["file"]; ok {
// 				header := fileHeaders[0]
// 				paths := strings.Split(header.Filename, ".")
// 				ext := strings.ToLower(paths[len(paths)-1])
// 				if ext != "png" && ext != "jpg" && ext != "jpeg" {
// 					utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: "file type error"}.Response(ctx)
// 					return
// 				} else {
// 					uid, _ := uuid.NewV4()
// 					header.Filename = uid.String() + "_" + header.Filename
// 					// os.MkdirAll(path.Join(configs.ApiConfig.Web.StaticPath, "img"), os.ModePerm)
// 					// uploadTo(header, path.Join(configs.ApiConfig.Web.StaticPath, "img"))
// 					ctx.JSON(map[string]string{"url_path": "static/img/" + header.Filename})
// 				}
// 			}
// 		}
// 	} else {
// 		utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: "no file found"}.Response(ctx)
// 	}
// }

// func uploadTo(fh *multipart.FileHeader, destDirectory string) (int64, error) {
// 	src, err := fh.Open()
// 	if err != nil {
// 		return 0, err
// 	}
// 	defer src.Close()

// 	out, err := os.OpenFile(filepath.Join(destDirectory, fh.Filename),
// 		os.O_WRONLY|os.O_CREATE, os.FileMode(0666))

// 	if err != nil {
// 		return 0, err
// 	}
// 	defer out.Close()

// 	return io.Copy(out, src)
// }
