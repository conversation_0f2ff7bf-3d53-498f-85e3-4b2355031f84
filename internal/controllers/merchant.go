package controllers

import (
	"git.paihang8.com/lib/goutils/sites/b2b168"
	"github.com/go-playground/validator/v10"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	"github.com/mitchellh/mapstructure"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"strconv"
	"strings"
)

type MerchantController struct {
	Ctx       context.Context
	loginInfo LoginInfo
}

func HandleMerchant(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(MerchantController))
}

// Merchant godoc
// @Summary 获取当前用户开通的商户列表
// @Description 获取当前用户开通的商户列表
// @Tags client
// @Produce  json
// @Param sortby query string  true "string enums" Enums(created_at,updated_at)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Success 200 {array} models.Merchant
// @Security ApiKeyAuth
// @Router /merchant/query [get]
func (c *MerchantController) GetQuery() interface{} {
	keyword := c.Ctx.URLParam("keyword")
	sortby := c.Ctx.URLParamDefault("sortby", "created_at")
	order := c.Ctx.URLParamDefault("order", "asc")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	if merchants, err := services.Merchant.GetItemsOfCompany(c.loginInfo.CompanyId, keyword, sortby, order, limit, offset); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		return utils.NewApiOkWrapper(merchants)
	}
}

// Merchant godoc
// @Summary 获取当前用户开通的商户列表状态
// @Description 获取当前用户开通的商户列表状态
// @Tags client
// @Produce  json
// @Param sortby query string  true "string enums" Enums(created_at,updated_at)
// @Param order query string  true "string enums" Enums(desc,asc)
// @Param limit query int true "int default" default(20)
// @Param offset query int true "int default" default(0)
// @Success 200 {array} schemas.MerchantStep
// @Security ApiKeyAuth
// @Router /merchant/status [get]
func (c *MerchantController) GetStatus() interface{} {
	keyword := c.Ctx.URLParam("keyword")
	sortby := c.Ctx.URLParamDefault("sortby", "created_at")
	order := c.Ctx.URLParamDefault("order", "asc")
	limit := c.Ctx.URLParamIntDefault("limit", 20)
	offset := c.Ctx.URLParamIntDefault("offset", 0)
	if merchants, err := services.Merchant.GetItemsOfCompany(c.loginInfo.CompanyId, keyword, sortby, order, limit, offset); err != nil {
		return utils.ApiError{
			Code: utils.ErrOPDataBaseError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx)
	} else {
		c, _ := services.NewCompanyService().Find(c.loginInfo.CompanyId)
		var resutls []schemas.MerchantStep
		for _, item := range merchants {
			t, _ := models.GetTrigger(item.PlatForm)
			resutls = append(resutls, t.CalSteps(&item, *c))
		}
		return utils.NewApiOkWrapper(resutls)
	}
}

// Merchant godoc
// @Summary 提交企业声明
// @Description 提交企业声明, pic
// @Tags client
// @Accept  json
// @Produce  json
// @Param id path int true "商铺id"
// @Param body body schemas.EditMerchant true "EditMerchant"
// @Success 200 {object} models.Company
// @Security ApiKeyAuth
// @Router /merchant/{id} [put]
func (c *MerchantController) PutBy(id uint64) interface{} {

	var input schemas.EditMerchant
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	if err := b2b168.Validate.Struct(input); err != nil {
		errStr := b2b168.Translate(err.(validator.ValidationErrors))
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  errStr,
		}.Response(c.Ctx)
	}
	if m, err := services.Merchant.GetByCompanyId(c.loginInfo.CompanyId, models.PlatformBafang); err != nil {
		return utils.ApiError{Code: utils.ErrMerchantEditFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	} else {
		if m.ID != id {
			return utils.ApiError{Code: utils.ErrMerchantEditFailed.Error(), Msg: "非法操作"}.Response(c.Ctx)
		}
		var ext merchants.BafangExt
		err := mapstructure.Decode(m.Ext, &ext)
		if err != nil {
			return utils.ApiError{Code: utils.ErrMerchantEditFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
		}

		client, _ := models.GetMerchant(models.PlatformBafang)
		client.UpdateAccount("id", m.TargetCompanyID)
		id, _ := strconv.Atoi(ext.Annonce.Id)
		if id > 0 {
			if data, err := client.GetCert(m.TargetCompanyID, id); err == nil {
				if status, ok := data.(b2b168.GetData); ok {
					parts := strings.SplitN(status.Check, " ", 2)
					if len(parts) == 2 {
						status.Check = parts[0]
						status.CheckReason = parts[1]
					}
					if status.Check == "审核通过" {
						ext.Annonce.Status = dbtypes.CertStatusPassed
						ext.Annonce.Check = "审核通过"
						ext.Annonce.Reason = ""
						m.SetExt(ext)
						if err := services.Merchant.Updates(m, "ext"); err != nil {
							return utils.ApiError{utils.ErrMerchantEditFailed.Error(), err.Error(), nil}.Response(c.Ctx)
						} else {
							return utils.NewApiOkWrapper(m)
						}
					}
				}
			}
		}
		ext.Annonce.Pic = input.Pic
		m.SetExt(ext)
		if err := services.Merchant.Updates(m, "ext"); err != nil {
			return utils.ApiError{utils.ErrMerchantEditFailed.Error(), err.Error(), nil}.Response(c.Ctx)
		}
		com, _ := services.NewCompanyService().Find(m.CompanyID)
		com.License = ext.Annonce.Pic

		if data, err := client.SubmitCert(*com, "企业声明", b2b168.CertTypeComAnnounce); err != nil {
			ext.Annonce.Reason = err.Error()
		} else if status, ok := data.(b2b168.CertData); ok {
			ext.Annonce.Id = status.ID
			ext.Annonce.Status = dbtypes.CertStatusSubmitted
		}
		m.SetExt(ext)
		if err := services.Merchant.Updates(m, "ext"); err != nil {
			return utils.ApiError{utils.ErrMerchantEditFailed.Error(), err.Error(), nil}.Response(c.Ctx)
		}
		return utils.NewApiOkWrapper(m)
	}

}
func (c *MerchantController) BeginRequest(ctx context.Context) {
	c.loginInfo = RetriveLoginInfo(ctx)
}

func (c *MerchantController) EndRequest(ctx context.Context) {
}

// Merchant godoc
// @Summary 获取当前用户已开通的商铺列表
// @Description 获取当前用户已开通的商铺列表
// @Tags client
// @Produce  json
// @Success 200 {array} models.Merchant
// @Security ApiKeyAuth
// @Router /merchant/enables [get]
func (c *MerchantController) GetEnables() interface{} {
	if merchants, err := services.Merchant.GetEnabled(c.loginInfo.CompanyId, true, false); err != nil {
		return utils.ApiError{utils.ErrOPDataBaseError.Error(), err.Error(), nil}.Response(c.Ctx)
	} else {
		for i, m := range merchants {
			merchants[i].PlatFormName = models.PlatFormName(m.PlatForm)
		}
		return utils.NewApiOkWrapper(merchants)
	}
}

// Merchant godoc
// @Summary 绑定酷易搜账号
// @Description 绑定酷易搜账号
// @Tags client
// @Produce  json
// @Param body body schemas.BindKuyiso true "BindKuyiso"
// @Success 200 {object} models.Merchant
// @Security ApiKeyAuth
// @Router /merchant/bind/kuyiso [post]
func (c *MerchantController) PostBindKuyiso() interface{} {
	var input schemas.BindKuyiso
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	if err := b2b168.Validate.Struct(input); err != nil {
		errStr := b2b168.Translate(err.(validator.ValidationErrors))
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  errStr,
		}.Response(c.Ctx)
	}
	if client, ok := models.GetTrigger(models.PlatformKuyiso); ok {
		if m, err := services.Merchant.GetByCompanyId(c.loginInfo.CompanyId, models.PlatformKuyiso); err != nil {
			return utils.ApiError{
				Code: utils.ErrInputParamError.Error(),
				Msg:  err.Error(),
			}.Response(c.Ctx)
		} else {
			if err := client.BindUser(m, input.Mobile, input.Password); err != nil {
				if ce, ok := err.(utils.CodeError); ok {
					return utils.ApiError{
						Code: strconv.Itoa(ce.Code),
						Msg:  ce.Error(),
					}
				} else {
					return utils.ApiError{
						Code: utils.ErrInputParamError.Error(),
						Msg:  err.Error(),
					}.Response(c.Ctx)
				}
			}
		}
	}
	return utils.NewApiOkWrapper(nil)
}
