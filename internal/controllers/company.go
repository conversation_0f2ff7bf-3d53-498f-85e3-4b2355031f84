package controllers

import (
	"git.paihang8.com/lib/goutils/sites/b2b168"
	"github.com/go-playground/validator/v10"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	_ "gitlab.com/all_publish/api/internal/business/eventtrigger"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/cat"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
)

type CompanyController struct {
	Ctx       context.Context
	loginInfo LoginInfo
}

func HandleCompany(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(CompanyController))
}

// Company godoc
// @Summary 提交/修改公司信息
// @Description 修改那个字段，提交那个字段, 直接修改
// @Tags client
// @Accept  json
// @Produce  json
// @Param body body models.Company true "Company"
// @Success 200 {object} models.Company
// @Security ApiKeyAuth
// @Router /company [put]
func (c *CompanyController) Put() interface{} {

	var input models.Company
	if err := c.Ctx.ReadJSON(&input); err != nil {
		return utils.ApiError{Code: utils.ErrAddProductFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	}
	if err := b2b168.Validate.Struct(input); err != nil {
		errStr := b2b168.Translate(err.(validator.ValidationErrors))
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  errStr,
		}.Response(c.Ctx)
	}
	if company, err := services.NewCompanyService().Find(c.loginInfo.CompanyId); err != nil {
		return utils.ApiError{Code: utils.ErrModifyCompanyFailed.Error(), Msg: err.Error()}.Response(c.Ctx)
	} else {
		m := map[string]interface{}{}
		_ = c.Ctx.ReadJSON(&m)
		delete(m, "id")
		company.AuditingFields = nil
		for k, _ := range m {
			company.AuditingFields = append(company.AuditingFields, k)
		}
		if err := dbtypes.UpdateModelFromMap(company, m); err != nil {
			return utils.ApiError{Code: utils.ErrUpdateModelError.Error(), Msg: err.Error()}.Response(c.Ctx)
		}

		if err := db.Instance().Get().Model(company).Save(company).Error; err != nil {
			return utils.ApiError{utils.ErrModifyCompanyFailed.Error(), err.Error(), nil}.Response(c.Ctx)
		}
		if err := validateCompany(company); err != nil {
			return utils.ApiError{utils.ErrModifyCompanyFailed.Error(), err.Error(), nil}.Response(c.Ctx)
		}
		go OnCompanyEdit(company)
		return utils.NewApiOkWrapper(company)
	}

}

// Company godoc
// @Summary 获取公司详情
// @Description 获取公司详情, 只能获取当前用户的
// @Tags client
// @Produce  json
// @Success 200 {object} models.Company
// @Security ApiKeyAuth
// @Router /company/me/ [get]
func (c *CompanyController) GetMe() interface{} {
	if item, err := services.NewCompanyService().Find(c.loginInfo.CompanyId); err != nil {
		return utils.ApiError{Code: utils.ErrInputParamError.Error(), Msg: err.Error()}.Response(c.Ctx)
	} else {
		item.AreaNames = services.Area.FindNames(item.AreaIds)
		item.CateNames, _ = cat.NewCatService().FindNames(item.Cate)
		//item.CanEdit = !services.Merchant.InCertApproving(c.loginInfo.CompanyId)
		item.CanEdit = true
		if !item.CanEdit {
			item.Reason = "企业认证中"
		}
		return utils.NewApiOkWrapper(item)
	}
}

func (c *CompanyController) BeginRequest(ctx context.Context) {
	c.loginInfo = RetriveLoginInfo(ctx)
}

func (c *CompanyController) EndRequest(ctx context.Context) {
}

func OnCompanyEdit(c *models.Company) {
	if ps, err := services.NewUserPlatformService().GetAutoPostedPlatForms(c.ID); err == nil {
		for _, form := range ps {
			if tr, ok := models.GetTrigger(form); ok {
				tr.OnCompanyEdit(c)
			}
		}
	}
}

func validateCompany(c *models.Company) error {
	if ps, err := services.NewUserPlatformService().GetAutoPostedPlatForms(c.ID); err == nil {
		for _, form := range ps {
			if tr, ok := models.GetTrigger(form); ok {
				if err := tr.ValidateCompany(*c); err != nil {
					return err
				}
			}
		}
	}
	return nil
}
