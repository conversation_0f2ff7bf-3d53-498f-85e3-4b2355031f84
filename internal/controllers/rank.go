package controllers

import (
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/mvc"
	utils "gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/business/merchanter"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
)

type RankController struct {
	Ctx       context.Context
	loginInfo LoginInfo
}

func HandleRanks(app *mvc.Application) {
	// app.Register(...)
	// app.Router.Use/UseGlobal/Done(...)
	app.Handle(new(RankController))
}

// Rank godoc
// @Summary 获取搜索排名列表
// @Description 获取搜索排名列表
// @Description query string parmas:
// @Description eg: 0 全部 1 百度 2 360， 3 搜狗 4 神马 5头条
// @Tags client
// @Produce  json
// @Param eg query int true "int enums" Enums(0,1,2,3,4,5)
// @Param page query int true "int"
// @Success 200 {object} schemas.RanksData
// @Security ApiKeyAuth
// @Router /ranks [get]
func (c *RankController) Get() interface{} {
	page := c.Ctx.URLParamIntDefault("page", 1)
	eg := c.Ctx.URLParamIntDefault("eg", 0)
	mClient := RetriveBindedMerchantClient(c.Ctx)
	if data, err := mClient.(*merchanter.Hy88Merchant).GetRanks(eg, page); err != nil {
		return utils.ApiError{
			Code: utils.ErrInputParamError.Error(),
			Msg:  err.Error(),
		}.Response(c.Ctx, true)
	} else {
		ret := schemas.RanksData{}
		ret.RanksData = *data
		for _, item := range data.Statics {
			switch services.EgRankType(item.Engine) {
			case services.EgRankbaiduM, services.EgRankBaidu:
				ret.Statics.BaiDu += item.Times
			case services.EgRankHaosou:
				ret.Statics.HaoSou += item.Times
			case services.EgRankShenma:
				ret.Statics.ShenMa += item.Times
			case services.EgRankSogou, services.EgRankSogouM:
				ret.Statics.Sougou += item.Times
			case services.EgRankToutiao, services.EgRankToutiaoM:
				ret.Statics.TouTiao += item.Times
			}
			ret.Statics.RankTotal += item.Times
		}
		//for i, v := range ret.RanksData.Data {
		//	v.Snap = toWebUrl("http://my.huangye88.com/enquiry/tishi/?url="+v.Snap, c.Ctx)
		//	ret.RanksData.Data[i] = v
		//}
		if seekdata, err := mClient.(*merchanter.Hy88Merchant).GetSeekCount(); err != nil {
			return utils.ApiError{
				Code: utils.ErrInputParamError.Error(),
				Msg:  err.Error(),
			}.Response(c.Ctx, true)
		} else {
			ret.Statics.InfoTotal = seekdata.Items
			return utils.NewApiOkWrapper(ret)
		}

	}
}

func (c *RankController) BeginRequest(ctx context.Context) {
	c.loginInfo = RetriveLoginInfo(ctx)
}

func (c *RankController) EndRequest(ctx context.Context) {
}
