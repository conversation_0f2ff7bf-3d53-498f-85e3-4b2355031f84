package tasks

import (
	"fmt"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/publisher"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"go.uber.org/zap"
	"strings"
	"time"
)

func autoOpenMerchant() {
	OpenMerchant(getPausedMerchant)
}

/*
*
把因为1. 用户过期。 2。超过最大发布数 而关闭的merchant， 有可能已经在huangye88续费/调整过了最大发布数。尝试着打开。
*/
func OpenMerchant(f merchantFunc) {
	merchants := f()
	for _, m := range merchants {
		switch m.PlatForm {
		case models.PlatformBafang:
			switch {
			case strings.Contains(m.PauseReason, "发布超过"):
				infoFinishedMerchant(&m, m.PauseReason)
			}
		case models.PlatformHy88:
			switch {
			//忽略
			case strings.Contains(m.PauseReason, "密码不正确"):
				userExpiredMerchant(&m)
			case strings.Contains(m.PauseReason, "没有通过认证"):
				fallthrough
			case strings.Contains(m.PauseReason, "审核拒绝"):
				fallthrough
			case strings.Contains(m.PauseReason, "数量上限"):
				fallthrough
			case strings.Contains(m.PauseReason, "已发完"):
				infoFinishedMerchant(&m, m.PauseReason)
				productFinishedMerchant(&m, m.PauseReason)
				//调用登录接口，能登录就打开
			case strings.Contains(m.PauseReason, "已经过期"):
				userExpiredMerchant(&m)
				//用户被封禁，得尝试发布信息
			case strings.Contains(m.PauseReason, "账户被封禁"):
				userBanned(&m, m.PauseReason)
			case strings.Contains(m.PauseReason, "合同未回传"):
				infoFinishedMerchant(&m, m.PauseReason)
				productFinishedMerchant(&m, m.PauseReason)
			default:
				log.Info(m.PauseReason, m.CompanyID)
			}
		default:
			client, _ := models.GetMerchant(m.PlatForm)
			if client.IsErrorShouldStopAutoPub(m.PauseReason) {
				infoFinishedMerchant(&m, m.PauseReason)
			}
		}

	}
}

type merchantFunc func() []models.Merchant

func getPausedMerchant() []models.Merchant {
	var merchants []models.Merchant
	if err := db.Instance().Get().Where("pause=true and updated_at>?", time.Now().AddDate(0, -1, 0)).Order("updated_at desc").Find(&merchants).Error; err != nil {
		log.Error("db err", zap.Error(err))
		return nil
	} else {
		return merchants
	}
}
func getMerchantByCompanyId(id int64) merchantFunc {
	return func() []models.Merchant {
		var merchants []models.Merchant
		if err := db.Instance().Get().Where("company_id=?", id).Find(&merchants).Error; err != nil {
			log.Error("db err", zap.Error(err))
			return nil
		} else {
			return merchants
		}
	}

}

func infoFinishedMerchant(m *models.Merchant, reason string) {
	var company models.Company
	if err := db.Instance().Get().Find(&company, m.CompanyID).Error; err == nil {
		var items []models.Info
		err := db.Instance().Get().Where("company_id = ? and  platform=? and status in ? ", company.ID, m.PlatForm, []int{dbtypes.InfoStatusWattingpublish, dbtypes.InfoStatusPublishFailed}).Order("failed_times").Limit(1).Find(&items).Error
		if err != nil {
			log.Error(err, company.ID)
		}
		if len(items) > 0 {
			ids := []uint64{}
			for _, info := range items {
				ids = append(ids, info.ID)
			}
			//db.Instance().Get().Transaction(func(tx *gorm.DB) error {
			//	tx.Table("info").Where("id IN (?)", ids).Updates(map[string]interface{}{"status": dbtypes.InfoStatusWattingpublish, "failed_reason": ""})
			helper := publisher.NewSimpleCompanyHelper(&company, true)
			info := items[0]
			services.NewInfoService().PatchExt(&info)
			err := helper.PubInfoBy(db.Instance().Get(), &info)
			if err == nil {
				log.Info("enable for ", m.CompanyID)
				m.AutoPub = true
				m.Pause = false
				m.PauseReason = ""
				services.Merchant.Save(m)
			} else {
				log.Error(err, m.CompanyID)
			}
			//	return err
			//})

		} else {
			log.Info("no info found for ", m.CompanyID)
		}
	}
}

func productFinishedMerchant(m *models.Merchant, reason string) {
	var company models.Company
	if err := db.Instance().Get().Find(&company, m.CompanyID).Error; err == nil {
		var items []models.CompanyProduct
		err = db.Instance().Get().Where("company_id = ? and status in ? ", company.ID, []int{dbtypes.CompanyProductStatusWattingpublish, dbtypes.CompanyProductStatusPublishFailed}).Order("failed_times").Limit(1).Find(&items).Error
		if err != nil {
			log.Error(err, company.ID)
		}
		if len(items) > 0 {
			ids := []uint64{}
			for _, info := range items {
				ids = append(ids, info.ID)
			}
			//db.Instance().Get().Transaction(func(tx *gorm.DB) error {
			//	tx.Table("company_product").Where("id IN (?)", ids).Updates(map[string]interface{}{"status": dbtypes.CompanyProductStatusWattingpublish, "failed_reason": ""})
			helper := publisher.NewSimpleProductPublisher(&company, true)
			err := helper.PubProductBy(&items[0])
			if err == nil {
				m.EnablePostProduct = true
				m.ProductPauseReason = ""
				services.Merchant.Save(m)
			} else {
				log.Error(err, m.CompanyID)
			}
			//	return err
			//})

		} else {
			log.Info("no product found for ", m.CompanyID)
		}
	}
}

//	func loginAndPostInfo(m *models.Merchant) {
//		if _, err := getUserByMerchant(m); err == nil {
//			infoFinishedMerchant(m)
//		}
//	}
func userExpiredMerchant(m *models.Merchant) {
	if _, err := getUserByMerchant(m); err == nil {
		log.Info("enable for ", m.CompanyID)
		m.Pause = false
		m.PauseReason = ""
		m.AutoPub = true
		services.Merchant.Save(m)
	}
}

func getUserByMerchant(m *models.Merchant) (*models.User, error) {
	if client, ok := models.GetMerchant(models.PlatForm(m.PlatForm)); ok {
		if user, err := client.LoginBy(m.Account); err != nil {
			return nil, err
		} else {
			return &user, nil
		}
	}
	return nil, fmt.Errorf("no client found for: %d", m.PlatForm)
}

func userBanned(m *models.Merchant, reason string) {
	var company models.Company
	if err := db.Instance().Get().Find(&company, m.CompanyID).Error; err == nil {
		var infos []models.Info
		db.Instance().Get().Where("company_id = ?  and platform=? and status in ? ", company.ID, m.PlatForm, []int{dbtypes.InfoStatusWattingpublish, dbtypes.InfoStatusPublishFailed}).Order("failed_times").Limit(1).Find(&infos)
		if len(infos) > 0 {
			infoIds := []uint64{}
			for _, info := range infos {
				infoIds = append(infoIds, info.ID)
			}
			//db.Instance().Get().Transaction(func(tx *gorm.DB) error {
			//	tx.Table("info").Where("id IN (?)", infoIds).Updates(map[string]interface{}{"status": dbtypes.InfoStatusWattingpublish, "failed_reason": ""})
			helper := publisher.NewSimpleCompanyHelper(&company, true)
			err := helper.PubInfoBy(db.Instance().Get(), &infos[0])
			if err == nil {
				m.Pause = false
				m.PauseReason = ""
				m.AutoPub = true
				log.Info("enable for ", m.CompanyID)
				services.Merchant.Save(m)
			} else {
				log.Error(err, m.CompanyID)
			}
			//return err
			//})

		} else {
			log.Info("no info found for ", m.CompanyID)
		}
	} else {
		log.Error(err)
	}
}
