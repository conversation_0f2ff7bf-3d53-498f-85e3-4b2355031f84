package tasks

import (
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/log"
	"go.uber.org/zap"
	"mime/multipart"
	"testing"
	"time"
)

func TestAutoGenerateInfo(t *testing.T) {
	config, err := configs.NewConfig("../../config.ini")
	if err != nil {
		t.Fatal(err)
	}
	db.InitRedis(*config)
	db.Init(*config, db.ConnectorTypeMysql)
	autoGenerateInfo()
}

type MockedMerchanter struct {
	logger *zap.SugaredLogger
}

func (m *MockedMerchanter) RenewWhenNeeded(cid uint64) bool {
	//TODO implement me
	panic("implement me")
}

func (m *MockedMerchanter) IsErrorMsgRunOut(err string) bool {
	return false
}

func (m *MockedMerchanter) SubmitCert(c models.Company, ext ...interface{}) (interface{}, error) {
	panic("implement me")
}

func (m *MockedMerchanter) GetApi() interface{} {
	panic("implement me")
}

func (m *MockedMerchanter) GetPersonInfo(uid string) (*publisher.Person, error) {
	panic("implement me")
}

func (m *MockedMerchanter) GetAreasByPid(pid int) ([]publisher.Area, error) {
	panic("implement me")
}

func (m *MockedMerchanter) GetCatProperties(cid int) ([]publisher.Property, error) {
	panic("implement me")
}

func (m *MockedMerchanter) GetSeeks(eg string, page int) (*publisher.SeeksData, error) {
	panic("implement me")
}

func (m *MockedMerchanter) PublishProduct(info models.CompanyProduct) (*publisher.SimpleProduct, error) {
	panic("implement me")
}

func (m *MockedMerchanter) GetCatByID(id int) (publisher.CategoryDetail, error) {
	panic("implement me")
}

func (m *MockedMerchanter) GetAllCatsByPID(pid int) ([]publisher.Category, error) {
	panic("implement me")
}

func (m *MockedMerchanter) GetSeekCount() (*publisher.SeekCountData, error) {
	panic("implement me")
}

func (m *MockedMerchanter) GetRanks(eg int, page int) (*publisher.RanksData, error) {
	panic("implement me")
}

func (m *MockedMerchanter) IsErrorShouldStopAutoPub(err string) bool {
	return false
}

func (m *MockedMerchanter) Login(user, password string) (models.User, error) {
	defer m.logger.Sync()
	m.logger.Info("")
	return models.User{}, nil
}

func (m *MockedMerchanter) Clone() models.Merchanter {
	return nil
}

func (m *MockedMerchanter) GetCompanyInfo(uid string) (models.Company, error) {
	defer m.logger.Sync()
	m.logger.Info("")
	return models.Company{}, nil
}

func (m *MockedMerchanter) Name() string {
	panic("implement me")
}

func (m MockedMerchanter) Class() string {
	panic("implement me")
}

func (m MockedMerchanter) Meta() models.MerchantMeta {
	panic("implement me")
}

func (m MockedMerchanter) Account() map[string]interface{} {
	panic("implement me")
}

func (m MockedMerchanter) UpdateAccount(key string, value interface{}) {
	panic("implement me")
}

func (m *MockedMerchanter) LoginBy(account map[string]interface{}) (models.User, error) {
	defer m.logger.Sync()
	time.Sleep(50 * time.Millisecond)
	m.logger.Info("")
	return models.User{}, nil
}

func (m MockedMerchanter) VerifyAccount(account map[string]interface{}) bool {
	panic("implement me")
}

func (m MockedMerchanter) CreateAlbum(album schemas.Album) (schemas.AlbumResponse, error) {
	panic("implement me")
}

func (m MockedMerchanter) ModifyAlbum(albumId string, album schemas.Album) error {
	panic("implement me")
}

func (m MockedMerchanter) GetAlbums() ([]schemas.AlbumItem, error) {
	panic("implement me")
}

func (m MockedMerchanter) GetImagesOfAlbum(albumId string, limit, offset int) ([]string, error) {
	panic("implement me")
}

func (m MockedMerchanter) UploadImagesToAlbum(albumId string, fileHeaders []*multipart.FileHeader) ([]string, error) {
	panic("implement me")
}

func NewMockedMerchanter() models.Merchanter {
	logger := log.AppLogger()
	return &MockedMerchanter{logger: logger}
}

func (m *MockedMerchanter) Reg(c models.Company) (interface{}, error) {
	panic("implement me")
}

func (m *MockedMerchanter) PublishInfo(info models.Info) (publisher.SimpleMessage, error) {
	panic("implement me")
}

func (m *MockedMerchanter) PlatForm() models.PlatForm {
	panic("implement me")
}

func (m *MockedMerchanter) GetCert(cid, id int) (interface{}, error) {
	panic("implement me")
}

func TestAutoPub(t *testing.T) {
	InitTest(t)
	//merchant, _ := merchanter.GetMerchant("HuangYe88")
	//mocked := NewMockedMerchanter()
	//merchanter.MerchantMap[merchant.Class()] = mocked
	//defer func() {
	//	merchanter.MerchantMap[merchant.Class()] = merchant
	//}()
	//autoPublishInfo()
	autoGenerateInfo()
}
