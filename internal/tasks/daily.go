package tasks

import (
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/log"
)

var isDailyRunning = false

func RunDaily() {
	if isDailyRunning {
		return
	}
	isDailyRunning = true
	defer func() {
		isDailyRunning = false
	}()
	log.Info("每日统计数据开始")
	s := services.NewDailyHistoryService(db.Instance().Get())
	s.CountUser()
	s.CountProduct()
	s.CountInfo()
	log.Info("每日统计数据结束")
}
