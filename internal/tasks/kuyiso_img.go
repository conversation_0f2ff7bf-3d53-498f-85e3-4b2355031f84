package tasks

import (
	"fmt"
	"git.paihang8.com/lib/goutils/sites/kuyiso"
	"gitlab.com/all_publish/api/internal/models"
	merchants2 "gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/pkg/db"
)

func SyncKuyisoImg() error {
	var shouldDeleteNum int
	var merchants []models.Merchant
	if err := db.Instance().Get().Where("plat_form = ?", models.PlatformKuyiso).Order("updated_at desc").Find(&merchants).Error; err != nil {
		return nil
	} else {
		for i, m := range merchants {
			im := []models.ImageMappings{}
			if err := db.Instance().Get().Model(models.ImageMappings{}).Joins("left join images on images.id=image_mappings.image_id").Where("image_mappings.plat_form=? and images.company_id=?", models.PlatformKuyiso, m.CompanyID).Select("image_mappings.*").Find(&im).Error; err != nil {
				return err
			} else {
				var account merchants2.KuyisoAccount
				account.Init(m.Account)
				client, _ := models.GetMerchant(models.PlatformKuyiso)
				client.LoginBy(m.Account)
				if imageSet, err := client.GetApi().(kuyiso.PublishApi).ListAlbumImages(kuyiso.ListAlbumImagesReq{account.Uid, 0, 10000}); err != nil {
					continue
				} else {
					for _, img := range im {
						if _, ok := imageSet[img.TargetImageID]; !ok {
							shouldDeleteNum++
						}
					}
					println(shouldDeleteNum, fmt.Sprintf("%d/%d", i+1, len(merchants)))
				}
			}
		}
	}
	println(shouldDeleteNum)
	return nil
}
