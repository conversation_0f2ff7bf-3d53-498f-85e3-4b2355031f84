package tasks

import (
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/publisher"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"testing"
	"time"
)

func Test_autoGenerateCompanyProduct(t *testing.T) {
	InitTest(t)
	autoGenerateCompanyProduct()
}

func Test_autoPublishProduct(t *testing.T) {
	InitTest(t)
	autoPublishProduct()
}

func TestTime(t *testing.T) {
	t.Log(dbtypes.SHNow())
	InitTest(t)
	var m models.Merchant
	db.Instance().Get().Find(&m, 8)
	willPubAt := publisher.TodayStart()
	t.Log(willPubAt)
	autoPubTime := (*m.AutoPubTime).Time
	willPubAt = willPubAt.Add(time.Duration(autoPubTime.Hour()) * time.Hour)
	willPubAt = willPubAt.Add(time.Duration(autoPubTime.Minute()) * time.Minute)
	t.Log(autoPubTime)
	t.Log(willPubAt)
}
