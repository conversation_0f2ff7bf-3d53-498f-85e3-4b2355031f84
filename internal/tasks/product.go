package tasks

import (
	"git.paihang8.com/lib/goutils/limiter"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/publisher"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"go.uber.org/zap"
	"sync"
	"sync/atomic"
)

func autoGenerateCompanyProduct() {
	rds := db.GetRedisConn()
	limiter := limiter.NewPoolTicket(2)
	defer func() {
		rds.Close()
	}()
	log.Info("公司产品生成任务开始")

	var companyIds []uint64
	var companies []models.Company
	dbHelp := db.Instance().Get().Joins("left join product on company.id = product.company_id")
	dbHelp = dbHelp.Where("product.status in ?", []int{dbtypes.ProductStatusPromote})
	//todo 临时打开
	dbHelp = dbHelp.Where("product.updated_at > ?", "2022-09-21 00:00:00")
	if err := dbHelp.Model(models.Company{}).Pluck("distinct(company.id)", &companyIds).Error; err != nil {
		log.Info("生成异常", zap.Error(err))
		return
	}
	if len(companyIds) == 0 {
		log.Info("没有公司需要生成产品")
		return
	}
	dbHelp = db.Instance().Get().Where("id in (?)", companyIds)
	if err := dbHelp.Find(&companies).Error; err != nil {
		log.Info("生成异常", zap.Error(err))
	} else {
		var total int32
		wg := sync.WaitGroup{}
		wg.Add(len(companies))
		for _, company := range companies {
			limiter.Take()
			go func(company models.Company) {
				defer func() {
					limiter.Back()
					wg.Done()
				}()
				publish := publisher.NewSimpleProductPublisher(&company, false)

				log.Info("开始为公司生成产品", zap.Uint64("id", company.ID),
					zap.String("name", company.Name))
				count, cause := publish.GenProduct(nil)
				atomic.AddInt32(&total, int32(count))
				log.Info("为公司生成产品结束", zap.Uint64("id", company.ID),
					zap.String("name", company.Name), zap.Int("count=", count), zap.Error(cause))
			}(company)

		}
		wg.Wait()
		log.Info("生成产品结束", zap.Int32("total", total))
	}

}

/**
主体是公司。
限制1 每个商家会限制发布的条数。譬如黄页88每天发10条
*/
func autoPublishProduct() {
	log.Info("开始自动发布")
	limiter := limiter.NewPoolTicket(2)

	var companyIds []uint64
	var companies []models.Company
	dbHelp := db.Instance().Get().Joins("left join company_product on company.id = company_product.company_id")
	dbHelp = dbHelp.Where("company_product.status = ?", dbtypes.CompanyProductStatusWattingpublish)
	//todo 临时打开
	dbHelp = dbHelp.Where("company_product.created_at > ?", "2022-09-21 12:00:00")
	dbHelp = dbHelp.Where("company_product.will_pub_at is not null and company_product.will_pub_at < ?", dbtypes.SHNow())
	if err := dbHelp.Model(models.Company{}).Pluck("distinct(company.id)", &companyIds).Error; err != nil {
		log.Info("发布异常", zap.Error(err))
		return
	}
	if err := db.Instance().Get().Where("id in (?)", companyIds).Find(&companies).Error; err != nil {
		log.Info("发布异常", zap.Error(err))
	} else {
		var total int32
		wg := sync.WaitGroup{}
		wg.Add(len(companies))
		for _, company := range companies {
			limiter.Take()
			go func(company models.Company) {
				defer func() {
					limiter.Back()
					wg.Done()
				}()
				helper := publisher.NewSimpleProductPublisher(&company, false)

				log.Info("开始为公司发布产品", zap.Uint64("id", company.ID),
					zap.String("name", company.Name))
				cnt, err := helper.PubProduct()
				log.Info("为公司发布产品结束", zap.Uint64("id", company.ID),
					zap.String("name", company.Name), zap.Int("cnt", cnt),
					zap.Error(err))
				atomic.AddInt32(&total, int32(cnt))
			}(company)

		}
		wg.Wait()
		log.Info("发布完成", zap.Int32("total", total))
	}

}
