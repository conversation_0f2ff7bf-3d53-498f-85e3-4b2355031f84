package tasks

import (
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/db"
	"strings"
	"time"
)

/*
*
1. 获取所有paused的账户
2. 过滤掉已过期的用户
3. 过滤掉已经通知过的用户
4. 发布通知
 1. 通过pause_reason分析关闭原因
 2. 代运营账号发给 18223177080
 3. 发布周期。3天统计一次
*/
func NotifyPaused() {
	notifyPaused()
}
func notifyPaused() error {
	if users, err := getPausedCompaies(); err != nil {
		return err
	} else {
		ns := services.NewNotifyService(db.Instance().Get())
		for _, user := range users {
			if ps, err := services.NewUserPlatformService().GetAutoPostedPlatForms(uint64(user.CompanyId)); err != nil || len(ps) == 0 {
				continue
			}
			// 过滤掉已经通知过的用户
			if lastNotified, err := ns.LastNotified(user.CompanyId, models.NotifyTypePaused); err != nil {
				//log.Error(err)
				return err
			} else {
				// 没通知过 或者 通知过后有登录的 才给通知
				if lastNotified != nil && user.LastLoginAt.Before(lastNotified.LastNotifyTime) {
					continue
				}
				// 代运营账号 指定发送账号
				if user.AdvisorId != 1 {
					user.Uid = 2900445
				}
				if client, ok := models.GetMerchant(models.PlatformHy88); ok {
					rt := reason2Type(user.PauseReason)
					if rt != -1 {
						if err := client.GetApi().(publisher.PublishApi).SendNotice(user.Uid, rt); err == nil {
							ns.MarkNotified(user.CompanyId, models.NotifyTypePaused)
						}
					}
				}
			}
		}
		return nil
	}
}

type Hy88User struct {
	Uid         int       `json:"uid"`       // 用户id
	Phone       int       `json:"phone"`     // 手机号
	CompanyId   int       `json:"companyId"` // 公司id
	PauseReason string    `json:"pauseReason"`
	AdvisorId   int       `json:"advisor_Id"`    // 顾问id
	LastLoginAt time.Time `json:"last_login_at"` // 最后登录时间
}

func getPausedCompaies() ([]Hy88User, error) {
	var users []Hy88User
	err := db.Instance().Get().Model(models.Merchant{}).Joins("LEFT JOIN `user` ON user.company_id = merchant.company_id ").Joins("left JOIN company on company.id  = merchant.company_id ").Where("pause=1 and cert_status=4 and plat_form = 0 AND user.expire_time > ?", time.Now()).Select("merchant.company_id,pause_reason,oem_id as uid,user.phone,company.advisor_id,last_login_at").Find(&users).Error
	return users, err
}

func reason2Type(pauseReason string) publisher.ReasonType {
	if strings.Contains(pauseReason, "金牌信息") {
		return publisher.ReasonTypeGoldMsgOut
	} else if strings.Contains(pauseReason, "合同未回传") {
		return publisher.ReasonTypeNoContract
	} else if strings.Contains(pauseReason, "密码不正确") {
		return publisher.ReasonTypePasswdChanged
	} else if strings.Contains(pauseReason, "信息总量") {
		return publisher.ReasonTypeMsgOut
	} else if strings.Contains(pauseReason, "手机号码不存在") {
		return publisher.ReasonTypeMobileChanged
	} else if strings.Contains(pauseReason, "您的公司没有通过认证不能发布信息") {
		return publisher.ReasonTypeCertRejected
	} else if strings.Contains(pauseReason, "被封禁") {
		return publisher.ReasonTypeBanned
	} else if strings.Contains(pauseReason, "服务已过期,请续费") {
		return publisher.ReasonTypeExpired
	}
	return -1
}
