package tasks

import (
	"git.paihang8.com/lib/goutils/limiter"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/product"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"go.uber.org/zap"
	"sync"
)

var isStatsRunning = false
var runStatsSN = 1

func RunStats() {
	if isStatsRunning {
		return
	}
	isStatsRunning = true
	rds := db.GetRedisConn()
	limiter := limiter.NewPoolTicket(2)
	defer func() {
		rds.Close()
		isStatsRunning = false
	}()
	log.Info("统计数据开始", runStatsSN)
	var companies []models.Company
	dbHelp := db.Instance().Get()
	if err := dbHelp.Model(models.Company{}).Find(&companies).Error; err != nil {
		log.Info("查询公司列表异常", zap.Error(err))
		return
	}
	wg := sync.WaitGroup{}
	wg.Add(len(companies))
	for _, company := range companies {
		limiter.Take()
		go func(company models.Company) {
			defer func() {
				limiter.Back()
				wg.Done()
			}()
			log.Info("开始为公司统计数据", zap.Uint64("id", company.ID),
				zap.String("name", company.Name))
			countCompany(company)
			log.Info("为公司统计数据结束", zap.Uint64("id", company.ID),
				zap.String("name", company.Name))
		}(company)

	}
	wg.Wait()
	log.Info("统计数据结束", runStatsSN)
	runStatsSN += 1
}

func countCompany(company models.Company) {
	if items, err := services.Merchant.GetEnabled(company.ID, true, true); err != nil {
		return
	} else if len(items) == 0 {
		return
	}
	stats := models.UserPubStat{}
	details := models.UserPubStatDetails{}
	if err := db.Instance().Get().Model(models.UserPubStat{}).Where("company_id=?", company.ID).First(&stats).Error; err != nil {
		stats.CompanyID = company.ID
		// 查用户表
		if u, err := services.NewUserService().GetUserByCompanyId(company.ID); err == nil {
			stats.UserName = u.Username
		}

		details.CompanyID = company.ID
	}
	// 获取原来的记录
	if err := db.Instance().Get().Model(models.UserPubStatDetails{}).Where("company_id=?", company.ID).First(&details).Error; err != nil {
		details.CompanyID = company.ID
	}
	stats.CompanyName = company.Name
	stats.OpEndTime = company.OpEndTime
	// 更新顾问信息
	if stats.AdvisorID != company.AdvisorID {
		stats.AdvisorID = company.AdvisorID
		if stats.AdvisorID > 0 {
			// 查用户表
			if u, err := services.NewSystemUserService().GetUserByID(company.AdvisorID); err == nil {
				stats.AdvisorName = u.Username
			}
		} else {
			stats.AdvisorName = "未分配顾问"
		}
	}
	// 查产品表
	if m, err := product.NewProductService().CountByStatusOfCompany(company.ID); err == nil {
		// 更新列表
		stats.ProductCnt = m[0]
		stats.ProductPromotionCnt = m[dbtypes.ProductStatusPromote]
		// 更新详情
		p := make(map[string]interface{})
		names := map[int]string{
			0:                                 "添加产品数",
			dbtypes.ProductStatusDraft:        "草稿",
			dbtypes.ProductStatusWattingaudit: "待审核",
			dbtypes.ProductStatusAuditpass:    "审核通过",
			dbtypes.ProductStatusPromote:      "推广中",
			dbtypes.ProductStatusAuditnotpass: "审核未通过",
			dbtypes.ProductStatusTitleout:     "标题已用完",
			dbtypes.ProductStatusContentOut:   "素材已用完",
		}
		for s, v := range m {
			p[names[s]] = v
		}
		details.ProductStats = p
	}
	// 查 信息表，统计信息数据
	if g, err := services.NewInfoService().GroupByPlatformAndStatus(company.ID, false); err == nil {
		success := 0
		// 更新详情
		p := make(map[string]interface{})
		for platform, item := range g {
			p[models.PlatFormName(models.PlatForm(platform))] = map[string]int{
				"推送成功": item[dbtypes.InfoStatusPublishSuccessed],
				"推送失败": item[dbtypes.InfoStatusPublishFailed],
				"等待推送": item[dbtypes.InfoStatusWattingpublish],
			}
			success += item[dbtypes.InfoStatusPublishSuccessed]
		}
		// 更新详情
		details.InfoStats = p
		// 更新列表
		stats.InfoSucceedCnt = success
	}

	// 查 信息表，统计信息数据
	if g, err := services.NewInfoService().GroupByPlatformAndStatus(company.ID, true); err == nil {
		success := 0
		failed := 0

		p := make(map[string]interface{})
		for platform, item := range g {
			p[models.PlatFormName(models.PlatForm(platform))] = map[string]int{
				"推送成功": item[dbtypes.InfoStatusPublishSuccessed],
				"推送失败": item[dbtypes.InfoStatusPublishFailed],
				"等待推送": item[dbtypes.InfoStatusWattingpublish],
			}
			success += item[dbtypes.InfoStatusPublishSuccessed]
			failed += item[dbtypes.InfoStatusPublishFailed]
		}
		// 更新详情
		details.InfoStatsYesterday = p
		// 更新列表
		stats.InfoSucceedCntYesterday = success
		stats.InfoFailedCntYesterday = failed
	}

	// 查 排名表
	if _, cnt, err := services.NewRankService().GetRanks(int(company.ID), 1, 1, 0, -1); err == nil {
		stats.RankedCnt = int(cnt)
	}
	if cnt, err := services.NewRankService().GroupRanksByEg(company.ID); err == nil {
		var p = make(map[string]int)
		var pp = make(map[string]interface{})
		for _, count := range cnt {
			key := ""
			switch services.EgRankType(count.Eg) {
			case services.EgRankBaidu, services.EgRankbaiduM:
				key = "百度"
			case services.EgRankShenma:
				key = "神马"
			case services.EgRankHaosou:
				key = "360"
			case services.EgRankSogou, services.EgRankSogouM:
				key = "搜狗"
			case services.EgRankToutiao, services.EgRankToutiaoM:
				key = "头条"
			}
			if key != "" {
				p[key] += count.C
				pp[key] = p[key]
			}
		}
		details.RankedStats = pp
	}
	db.Instance().Get().Save(&stats)
	db.Instance().Get().Save(&details)
}
