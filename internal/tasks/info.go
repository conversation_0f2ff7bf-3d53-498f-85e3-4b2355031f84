package tasks

import (
	"git.paihang8.com/lib/goutils/limiter"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/publisher"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"go.uber.org/zap"
	"strconv"
	"sync"
	"sync/atomic"
)

var isAutoGenerateInfoRunning = false
var genInfoSn int64

func autoGenerateInfo() {
	if isAutoGenerateInfoRunning {
		return
	}
	isAutoGenerateInfoRunning = true
	rds := db.GetRedisConn()
	genInfoSn++
	log.Info("信息生成任务开始[", strconv.FormatInt(genInfoSn, 10), "]")
	limiter := limiter.NewPoolTicket(10)
	var total int32
	defer func() {
		log.Info("信息生成任务结束[", strconv.FormatInt(genInfoSn, 10), "]:", strconv.FormatInt(int64(total), 10))
		rds.Close()
		isAutoGenerateInfoRunning = false
	}()

	var companyIds []uint64
	var companies []models.Company
	dbHelp := db.Instance().Get().Joins("left join product on company.id = product.company_id")
	//todo 临时打开
	//dbHelp = dbHelp.Where("product.updated_at > ?", "2022-09-21 00:00:00")

	dbHelp = dbHelp.Where("product.status in ?", []int{dbtypes.ProductStatusPromote, dbtypes.ProductStatusSubjectOut})
	if err := dbHelp.Model(models.Company{}).Pluck("distinct(company.id)", &companyIds).Error; err != nil {
		log.Info("生成异常", zap.Error(err))
		return
	}
	if len(companyIds) == 0 {
		log.Info("没有公司需要生成信息")
		return
	}
	dbHelp = db.Instance().Get().Where("id in (?)", companyIds)
	if err := dbHelp.Find(&companies).Error; err != nil {
		log.Info("生成异常", zap.Error(err))
	} else {

		wg := sync.WaitGroup{}
		wg.Add(len(companies))
		for _, company := range companies {
			limiter.Take()
			go func(company models.Company) {
				defer func() {
					if err := recover(); err != nil {
						log.Error("生成信息异常", zap.Uint64("id", company.ID),
							zap.String("name", company.Name), zap.Any("err", err))
					}
					limiter.Back()
					wg.Done()
				}()
				helper := publisher.NewSimpleCompanyHelper(&company, false)

				log.Info("开始为公司生成信息", zap.Uint64("id", company.ID),
					zap.String("name", company.Name))
				count, cause := helper.GenInfo(nil)
				atomic.AddInt32(&total, int32(count))
				log.Info("为公司生成信息结束", zap.Uint64("id", company.ID),
					zap.String("name", company.Name), zap.Int("count=", count), zap.Error(cause))
			}(company)

		}
		wg.Wait()

	}

	//rds.Do("DEL", "GEN_INFO")
}

var isAutoPublishInfoRunning = false

/*
*
主体是公司。
限制1 每个商家会限制发布的条数。譬如黄页88每天发10条
*/
func autoPublishInfo() {
	if isAutoPublishInfoRunning {
		return
	}
	isAutoPublishInfoRunning = true
	defer func() {
		isAutoPublishInfoRunning = false
	}()
	log.Info("开始自动发布")
	limiter := limiter.NewPoolTicket(2)

	var companyIds []uint64
	var companies []models.Company
	dbHelp := db.Instance().Get()
	dbHelp = dbHelp.Joins("inner join merchant on company.id = merchant.company_id")
	dbHelp = dbHelp.Joins("left join product on company.id = product.company_id")
	dbHelp = dbHelp.Joins("left join info on company.id = info.company_id")
	dbHelp = dbHelp.Where("merchant.pause=?", false)
	dbHelp = dbHelp.Where("merchant.auto_pub=?", true)
	dbHelp = dbHelp.Where("product.status in (?)", []int{dbtypes.ProductStatusPromote, dbtypes.ProductStatusSubjectOut, dbtypes.ProductStatusContentOut})
	dbHelp = dbHelp.Where("info.status = ?", dbtypes.InfoStatusWattingpublish)
	// 临时控制 todo
	dbHelp = dbHelp.Where("info.created_at > ?", "2022-09-21 12:00:00")

	dbHelp = dbHelp.Where("info.will_pub_at is not null and info.will_pub_at < ?", dbtypes.SHNow())
	if err := dbHelp.Model(models.Company{}).Pluck("distinct(company.id)", &companyIds).Error; err != nil {
		log.Info("发布异常", zap.Error(err))
		return
	}
	if len(companyIds) == 0 {
		log.Info("发布完成", zap.Int32("total", 0))
		return
	}
	if err := db.Instance().Get().Where("id in (?)", companyIds).Find(&companies).Error; err != nil {
		log.Info("发布异常", zap.Error(err))
	} else {
		var total int32
		wg := sync.WaitGroup{}
		wg.Add(len(companies))
		for _, company := range companies {
			limiter.Take()
			go func(company models.Company) {
				defer func() {
					limiter.Back()
					wg.Done()
				}()
				helper := publisher.NewSimpleCompanyHelper(&company, false)

				log.Info("公司发布信息start", zap.Uint64("id", company.ID),
					zap.String("name", company.Name))
				cnt, err := helper.PubInfo(db.Instance().Get())
				log.Info("公司发布信息end", zap.Uint64("id", company.ID),
					zap.String("name", company.Name), zap.Int("cnt", cnt),
					zap.Error(err))
				atomic.AddInt32(&total, int32(cnt))
			}(company)

		}
		wg.Wait()
		log.Info("发布完成", zap.Int32("total", total))
	}

}
