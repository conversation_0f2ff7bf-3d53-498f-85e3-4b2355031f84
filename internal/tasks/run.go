package tasks

import (
	"github.com/robfig/cron"
	"gitlab.com/all_publish/api/configs"
)

func StartCronTask() {
	c := cron.New()
	//Seconds      | Yes        | 0-59            | * / , -
	//Minutes      | Yes        | 0-59            | * / , -
	//Hours        | Yes        | 0-23            | * / , -
	//Day of month | Yes        | 1-31            | * / , - ?
	//Month        | Yes        | 1-12 or JAN-DEC | * / , -
	//Day of week
	if configs.ApiConfig.Web.EnableTask {
		if configs.ApiConfig.Web.EnablePub {
			c.AddFunc("0 */1 * * * * ", autoPublishInfo)
			c.AddFunc("0 */1 * * * * ", autoGenerateInfo)
			c.AddFunc("0 */5 * * * * ", autoPublishProduct)
			c.AddFunc("0 0 * * * * ", autoGenerateCompanyProduct)

		}
		c.AddFunc("0 0 * * * * ", autoOpenMerchant)
		c.AddFunc("0 0 1 * * * ", syncAicaigou)
		c.AddFunc("0 0 2 * * * ", RunStats)
		c.AddFunc("0 0 4 * * * ", RunDaily)
		c.AddFunc("0 0 8 * * * ", NotifyPaused)
	}

	c.Start()
}
