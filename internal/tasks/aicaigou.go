package tasks

import (
	"fmt"
	"git.paihang8.com/lib/goutils"
	"git.paihang8.com/lib/goutils/sites/sole"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"go.uber.org/zap"
	"strconv"
	"strings"
)

func syncAicaigou() {
	var items []models.Merchant
	if err := db.Instance().Get().Where("plat_form=? and cert_status=? and account->'$.status'=? and  account->'$.ordered'=true and  account->'$.sole_cert_status'  = ?", models.PlatformSoleExt, dbtypes.CertStatusPassed, sole.UserAuditStatusPassed, dbtypes.CertStatusPassed).Find(&items).Error; err != nil {
		log.Error("db err", zap.Error(err))
	} else {
		for _, m := range items {
			syncMerchant(&m)
		}
	}
}

func syncMerchant(m *models.Merchant) {
	client, _ := models.GetMerchant(m.PlatForm)
	client.LoginBy(m.Account)
	var account merchants.AicaigouAccount
	account.Init(m.Account)
	if ids, err := client.GetApi().(sole.PublishApi).GetRecall(sole.ActionReq{
		UserName: account.UserName,
		Password: account.Password,
	}); err == nil {
		if len(ids) > 0 {
			for _, p := range ids {
				syncInfo(strconv.Itoa(p.ProductId), m.CompanyID)
			}
		}

	}
	if obj, err := client.GetApi().(sole.PublishApi).LastMonthClickedKeyword(sole.ActionReq{
		UserName: account.UserName,
		Password: account.Password,
	}); err == nil {
		for _, item := range obj.Clicked {
			word := strings.ReplaceAll(item.Keyword, ",", " ")
			parts := strings.Split(word, " ")
			for _, item := range parts {
				services.Rank.AddKeyword(item, services.EgSourceTypeBaidu)
				services.Rank.AddKeyword(item, services.EgSourceTypeBaiduM)
			}
		}
		for _, item := range obj.Showed {
			word := strings.ReplaceAll(item.Keyword, ",", " ")
			parts := strings.Split(word, " ")
			for _, item := range parts {
				services.Rank.AddKeyword(item, services.EgSourceTypeBaidu)
				services.Rank.AddKeyword(item, services.EgSourceTypeBaiduM)
			}
		}
	}

	if obj, err := client.GetApi().(sole.PublishApi).LastMonthClickedProduct(sole.ActionReq{
		UserName: account.UserName,
		Password: account.Password,
	}); err == nil {
		for _, item := range obj.Clicked {
			word := strings.ReplaceAll(item.ProductName, ",", " ")
			parts := strings.Split(word, " ")
			for _, item := range parts {
				services.Rank.AddKeyword(item, services.EgSourceTypeBaidu)
				services.Rank.AddKeyword(item, services.EgSourceTypeBaiduM)
			}
		}
		for _, item := range obj.Showed {
			word := strings.ReplaceAll(item.ProductName, ",", " ")
			parts := strings.Split(word, " ")
			for _, item := range parts {
				services.Rank.AddKeyword(item, services.EgSourceTypeBaidu)
				services.Rank.AddKeyword(item, services.EgSourceTypeBaiduM)
			}
		}
	}
}

func syncInfo(aicaigouProductId string, cid uint64) {
	var item models.Info
	if err := db.Instance().Get().Where("company_id=? and status=2 and platform=? and pub_res->'$.res_url.messageid'  = ?", cid, models.PlatformSoleExt, aicaigouProductId).Find(&item).Error; err != nil {
		log.Error("db err", zap.Error(err))
	} else if item.ID > 0 {
		var res models.AicaigouInfo
		res.Init(item.PubRes)
		rds := db.GetRedisConn()
		defer rds.Close()
		if !res.Ext.Synced {
			res.Ext.Synced = true
			item.PubRes = res.ToMap()
			db.Instance().Get().Save(&item)
		}
	}
}

func _urlToKey(url string, mobile bool) string {
	if mobile {
		return fmt.Sprintf("https://b2b.baidu.com/m/land?id=%s10", goutils.Md5str(url))
	}
	return fmt.Sprintf("https://b2b.baidu.com/land?id=%s10", goutils.Md5str(url))
}

func NewItem(id uint64, key string, source string) string {
	return fmt.Sprintf("%d-%s-%s", id, key, source)
}
