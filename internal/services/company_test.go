package services

import (
	"gitlab.com/all_publish/api/test"
	"testing"
)

func TestCompanyService_Save(t *testing.T) {
	test.InitTest(t)
	if u, err := NewUserService().GetUserByPhone(13205347943); err != nil {
		t.<PERSON>rror(err)
	} else {
		if com, err := NewCompanyService().Find(u.CompanyID); err != nil {
			t.Error(err)
		} else {
			com.Phone[1] = "0534-2155556"
			t.Log(NewCompanyService().Save(com))
		}
	}
}
