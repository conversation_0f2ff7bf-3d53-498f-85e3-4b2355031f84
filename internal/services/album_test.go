package services

import (
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/test"
	"strconv"
	"testing"
)

func TestAlbumService_Exist(t *testing.T) {
	test.InitTest(t)
	svc := NewAlbumService(db.Instance().Get())
	if a, err := svc.Exist("默认相册", 4); err == nil && a != nil {
		t.Error("expected false, result is true")
	}

	if a, err := svc.Create("默认相册", "1", "", 4); err != nil {
		t.Error(err)
	} else if a.ID <= 0 {
		t.Error("expected id > 0, result <=0")
	} else {
		if a, err := svc.Exist("默认相册", 4); err == nil && a == nil {
			t.Error("expected true, result is false")
		}
		if err := svc.Delete(4, a.ID); err != nil {
			t.<PERSON>rro<PERSON>(err)
		} else {
			if a, err := svc.Exist("默认相册", 4); err == nil && a != nil {
				t.Error("expected false, result is true")
			}
		}
	}

}

func TestAlbumService_Create(t *testing.T) {
	test.InitTest(t)
	svc := NewAlbumService(db.Instance().Get())
	name := "默认相册1"
	var companyId uint64 = 4
	if a, err := svc.Exist(name, companyId); err == nil && a != nil {
		t.Error("expected false, result is true")
	}

	if a, err := svc.Create(name, "1", "", companyId); err != nil {
		t.Error(err)
	} else if a.ID <= 0 {
		t.Error("expected id > 0, result <=0")
	} else {
		if b, err := svc.Exist(name, companyId); err == nil && b == nil {
			t.Error("expected true, result is false")
		}
		if _, err := svc.Create(name, "1", "", companyId); err == nil {
			t.Errorf("expected error, result is no err")
		}

		if err := svc.Delete(a.CompanyId, a.ID); err != nil {
			t.Error(err)
		} else {
			if b, err := svc.Exist(name, companyId); err == nil && b != nil {
				t.Error("expected false, result is true")
			}
		}
	}
}

func TestAlbumService_Update(t *testing.T) {
	test.InitTest(t)
	svc := NewAlbumService(db.Instance().Get())
	name := "默认相册"
	var companyId uint64 = 4
	if a, err := svc.Create(name, "1", "", companyId); err != nil {
		t.Fatal(err)
	} else {
		defer func() {
			svc.Delete(a.CompanyId, a.ID)
		}()
		if album, err := svc.Update(companyId, a.ID, map[string]interface{}{
			"name": "修改的相册",
		}); err != nil {
			t.Fatal(err)
		} else {
			if album.Name != "修改的相册" {
				t.Errorf("expected %s, real: %s", "修改的相册", album.Name)
			}
		}
	}
}

func TestAlbumService_ListAlbum(t *testing.T) {
	test.InitTest(t)
	svc := NewAlbumService(db.Instance().Get())
	name := "默认相册"
	var companyId uint64 = 4
	for i := 0; i < 11; i++ {
		svc.Create(name+strconv.Itoa(i), "1", "", companyId)
	}
	if items, err := svc.ListAlbum(companyId, 0); err != nil {
		t.Fatal(err)
	} else {
		for i, item := range items {
			if item.Name != name+strconv.Itoa(i) {
				t.Error("name not match")
			}
			svc.Delete(item.CompanyId, item.ID)
		}
	}

}

func TestAlbumService_OverUploaded(t *testing.T) {
	test.InitTest(t)
	svc := NewAlbumService(db.Instance().Get())
	name := "默认相册"
	var companyId uint64 = 4
	a, _ := svc.Create(name, "1", "", companyId)
	for i := 0; i < 10; i++ {
		//svc.AddImage(a.ID, 1024*1024)
	}
	defer func() {
		svc.Delete(a.CompanyId, a.ID)
	}()
	if over, err := svc.OverUploaded(companyId); err != nil {
		t.Fatal(err)
	} else {
		if !over {
			t.Errorf("expected %v, real %v", true, over)
		}
	}

}

func TestAlbumService_AddImage(t *testing.T) {
	test.InitTest(t)
	svc := NewAlbumService(db.Instance().Get())
	t.Run("album not exist", func(t *testing.T) {
		//if err := svc.AddImage(1000, 10000); err == nil {
		//	t.Error("expected error, real: nil")
		//}
	})
	t.Run("album exist", func(t *testing.T) {
		if cc, err := svc.Create(DefaultName, "1", "", 4); err != nil {
			t.Fatal(err)
		} else {
			//if err := svc.AddImage(cc.ID, 10000); err != nil {
			//	t.Errorf("expected nil, real: %v", err)
			//}
			svc.Delete(cc.CompanyId, cc.ID)
		}

	})
}
