package services

import (
	"gitlab.com/all_publish/api/internal/models"
	"gorm.io/gorm"
)

type AicaigouUser struct {
	db *gorm.DB
}

func NewAicaigouUser(db *gorm.DB) *AicaigouUser {
	return &AicaigouUser{db: db}
}

func (u *AicaigouUser) Insert(item *models.AicaigouUsers) (err error) {
	return u.db.Create(&item).Error
}

func (u *AicaigouUser) Find(cid uint64) (*models.AicaigouUsers, error) {
	var item models.AicaigouUsers
	if err := u.db.Where("company_id=?", cid).Find(&item).Error; err != nil {
		return nil, err
	}
	return &item, nil
}

func (u *AicaigouUser) Save(item *models.AicaigouUsers) (err error) {
	return u.db.Model(item).Save(item).Error
}
