package cat_test

import (
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services/cat"
	"gitlab.com/all_publish/api/test"
	"math/rand"
	"testing"
)

func TestCatService_Find(t *testing.T) {
	test.InitTest(t)
	t.Run("active = 1", func(t *testing.T) {
		if c, err := cat.NewCatService().Find(1); err != nil {
			t.Errorf("expected nil, real is:%v", err)
		} else if c.ID != 1 {
			t.<PERSON><PERSON><PERSON>("expected 1, real is %d", c.ID)
		}
	})
	t.Run("active = 0", func(t *testing.T) {
		if _, err := cat.NewCatService().Find(556); err == nil {
			t.Errorf("expected error, real is nil")
		}
	})
	t.Run("not exists", func(t *testing.T) {
		if _, err := cat.NewCatService().Find(1000000); err == nil {
			t.Errorf("expected error, real is nil")
		}
	})
}

func TestCatService_FindAllByLevel(t *testing.T) {
	test.InitTest(t)
	t.Run("level = 1", func(t *testing.T) {
		if c, err := cat.NewCatService().FindAllByLevel(1, 0, 100); err != nil {
			t.Errorf("expected nil, real is:%v", err)
		} else if len(c) != 5 {
			t.Errorf("expected 5, real is %d", len(c))
		}
	})
}

func TestCatService_FindSubsByPid(t *testing.T) {
	test.InitTest(t)
	t.Run("no subs", func(t *testing.T) {
		if c, err := cat.NewCatService().FindSubsByPid(30, 0, 100); err != nil {
			t.Errorf("expected nil, real is:%v", err)
		} else if len(c) != 0 {
			t.Errorf("expected 0, real is %d", len(c))
		}
	})

	t.Run("has subs", func(t *testing.T) {
		if c, err := cat.NewCatService().FindSubsByPid(41, 0, 100); err != nil {
			t.Errorf("expected nil, real is:%v", err)
		} else if len(c) != 30 {
			t.Errorf("expected 30, real is %d", len(c))
		}
	})
}

func TestCatService_GetProperties(t *testing.T) {
	test.InitTest(t)

	for i := 0; i < 10; i++ {
		cid := rand.Int31n(22873)
		svc := cat.NewCatService()
		got, err := svc.GetProperties(uint64(cid))
		mClient, _ := models.GetMerchant(models.PlatformHy88)
		data, err2 := mClient.GetApi().(publisher.PublishApi).GetCatProperties(int(cid))
		if err != nil && err2 == nil || err == nil && err2 != nil {
			t.Errorf("error not match cid:%d %v, %v", cid, err, err2)
		}
		if len(got) != len(data) {
			t.Errorf("length not match cid:%d %v, %v", cid, got, data)
		} else {
			if len(got) > 0 {

				if got[0].Displayname != data[0].Displayname || got[0].Fieldtype != data[0].Fieldtype ||
					got[0].Fieldname != data[0].Fieldname {
					t.Errorf("dagta not match cid:%d %v, %v", cid, got, data)
				}
			}
		}
	}
}

func TestNewCatService(t *testing.T) {
	test.InitTest(t)
	t.Run("no data ", func(t *testing.T) {
		if c, err := cat.NewCatService().GetMappings(3); err != nil {
			t.Errorf("expected nil, real is:%v", err)
		} else if len(c) != 0 {
			t.Errorf("expected 0, real is %d", len(c))
		}
		if _, err := cat.NewCatService().GetMapping(3, models.PlatformHy88); err == nil {
			t.Errorf("expected err, real is nil")
		}
	})

	t.Run("has data", func(t *testing.T) {
		if c, err := cat.NewCatService().GetMappings(4); err != nil {
			t.Errorf("expected nil, real is:%v", err)
		} else if len(c) != 2 {
			t.Errorf("expected 2, real is %d", len(c))
		}
		if i, err := cat.NewCatService().GetMapping(4, models.PlatformHy88); err != nil {
			t.Errorf("expected nil, real is %v", err)
		} else if i.CatID != 4 {
			t.Errorf("expected 4, real is %v", i.CatID)
		}
	})
}

func TestCatService_GetMapping(t *testing.T) {
	test.InitTest(t)
	t.Run("exist", func(t *testing.T) {
		if d, e := cat.NewCatService().GetMapping(30, models.PlatformBafang); e != nil {
			t.Error(e)
		} else if d.CatID != 30 {
			t.Errorf("expected 30, real is %d", d.CatID)
		}
	})

	t.Run("not exist", func(t *testing.T) {
		if _, e := cat.NewCatService().GetMapping(30000, models.PlatformBafang); e == nil {
			t.Error("expected error, but nil")
		}
	})
}

func Test_catService_GetPropertyName(t *testing.T) {
	test.InitTest(t)
	type args struct {
		id string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"44195", args{"44195"}, "材质"},
		{"44196", args{"44196"}, "加工定制"},
		{"44197", args{"44197"}, "结构"},
		{"44198", args{"44198"}, "箱型"},
		{"44199", args{"44199"}, "载重"},
		{"84391", args{"84391"}, "类型"},
		{"随便", args{"随便"}, "随便"},
	}
	s := cat.NewCatService()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := s.GetPropertyName(tt.args.id); got != tt.want {
				t.Errorf("GetPropertyName() = %v, want %v", got, tt.want)
			} else {
				t.Log("ok", got)
			}
		})
	}
}
