package cat

import (
	"encoding/json"
	"errors"
	"fmt"
	"git.paihang8.com/lib/goutils/sites/baixing"
	"github.com/gomodule/redigo/redis"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/pkg/convert"
	"gitlab.com/all_publish/api/pkg/db"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
)

var service catService

const (
	OtherService = "qitafuwu"
)

func NewCatService() *catService {
	return &catService{names: make(map[string]string)}
}
func init() {
	rand.Seed(time.Now().UnixNano())
}

type catService struct {
	names map[string]string
	sync.Mutex
}

func (c *catService) GetMappings(id uint64) ([]models.CatMappings, error) {
	var mappings []models.CatMappings
	err := db.Instance().Get().Where("cat_id=?", id).Find(&mappings).Error
	return mappings, err
}

func (c *catService) GetSoleCate(id int) (item models.SoleCat, err error) {
	err = db.Instance().Get().Model(item).Where("id=?", id).Order("level desc").First(&item, id).Error
	if item.ID == 0 {
		err = errors.New("找不到对应的搜了网分类")
	}
	return
}

func (c *catService) GetMapping(id uint64, form models.PlatForm) (models.CatMappings, error) {
	var mapping models.CatMappings
	err := db.Instance().Get().Where("cat_id=? and plat_form= ?", id, form).First(&mapping).Error
	if err == nil && mapping.CatID == 0 {
		return models.CatMappings{}, errors.New("record not found")
	}
	return mapping, err
}

func (c *catService) GetIdsForSouhaohuo(cates []string) []string {
	mycates := []string{}
	for _, id := range cates {
		if item, err := c.GetMapping(convert.Str(id).MustUInt64(), models.PlatformSouHaoHuo); err != nil {
			// 没对应二级分类
			continue
		} else {
			mycates = append(mycates, item.TargetCatID)
		}
	}
	return mycates
}

func reverse(s []string) []string {
	for i, j := 0, len(s)-1; i < j; i, j = i+1, j-1 {
		s[i], s[j] = s[j], s[i]
	}
	return s
}

func contains(s []int, data int) bool {
	for _, v := range s {
		if data == v {
			return true
		}
	}
	return false
}

func removeDuplicated(s []models.CatField) []models.CatField {
	var ids []int
	var ret []models.CatField
	for _, d := range s {
		if !contains(ids, d.ID) {
			ret = append(ret, d)
		}
		ids = append(ids, d.ID)
	}
	return ret
}

type fieldSlice []schemas.CatField

func (p fieldSlice) Len() int           { return len(p) }
func (p fieldSlice) Less(i, j int) bool { return p[i].Sort < p[j].Sort }
func (p fieldSlice) Swap(i, j int)      { p[i], p[j] = p[j], p[i] }

func (c *catService) GetPropertyName(id string) string {
	if v, ok := c.names[id]; ok {
		return v
	}
	c.Lock()
	defer c.Unlock()
	if len(c.names) == 0 {
		var items []models.CatField
		if err := db.Instance().Get().Select("id", "displayname").Find(&items).Error; err != nil {
			return ""
		}
		if len(items) == 0 {
			return ""
		}
		for _, item := range items {
			c.names[strconv.Itoa(item.ID)] = item.Displayname
		}
	}
	if v, ok := c.names[id]; ok {
		return v
	} else {
		return id
	}
}

func (c *catService) GetProperties(id uint64) ([]schemas.CatField, error) {
	if fields, err := c.getProperties(id); err != nil {
		return nil, err
	} else {
		var ret fieldSlice

		for _, f := range fields {
			if f.Isenable == 1 && !f.HasChild {
				d := []interface{}{}
				json.Unmarshal([]byte(f.Fieldoptions), &d)
				ret = append(ret, schemas.CatField{
					Displayname: f.Displayname + "：",
					Fieldtype:   f.Fieldtype,
					Fieldname:   fmt.Sprintf("properties[%d]", f.ID),
					//Fieldoptions: f.Fieldoptions,
					Fieldoptions: d,
					Sort:         f.Fieldsort,
				})
			}
		}
		sort.Sort(ret)
		return []schemas.CatField(ret), nil
	}
}

func (c *catService) GetBaixingProperties(id uint64, pvalue, metaName string) ([]baixing.Meta, error) {
	//if m, _ := service.GetMapping(id, models.PlatformBaixing); m.TargetCatID != "" {
	if metas, err := baixing.BaixingPublisher.GetMeta(baixing.QueryMeta{
		//CategoryId:  m.TargetCatID,
		CategoryId:  OtherService,
		ParentValue: "",
		MetaName:    "",
	}); err != nil {
		return nil, err
	} else {
		result := []baixing.Meta{}
		for _, meta := range metas {
			if !meta.Required && (meta.Name == "QQ号" || meta.Name == "微信号") {
				continue
			}
			result = append(result, meta)
		}
		return result, nil
	}
	//}
	return nil, nil

}

func (c *catService) FillBaixingProperties(cateId string, cityId string, properties map[string]interface{}) map[string]interface{} {
	if metas, err := baixing.BaixingPublisher.GetMeta(baixing.QueryMeta{
		//CategoryId:  cateId,
		CategoryId:  OtherService,
		CityId:      cityId,
		ParentValue: "",
		MetaName:    "",
	}); err != nil {
		return properties
	} else {
		for _, item := range metas {
			if item.Required {
				if _, ok := properties[item.Name]; !ok {
					if len(item.Data) > 0 {
						properties[item.Name] = item.Data[rand.Intn(len(item.Data))].Value // 下拉框。没选的随机给选一个
					}
					if item.Name == "分类" {
						properties[item.Name] = cateId
					}
				}
			}
		}
		return properties
	}
}

func (c *catService) getProperties(id uint64) ([]models.CatField, error) {
	var cfs []models.CatField
	cat, err := c.Find(id)
	if err != nil {
		return nil, err
	}
	ids := strings.Split(cat.CatTrace, ",")
	ids = reverse(ids)
	parentIds := []int{}
	chidren := map[int]models.CatField{}
	data := []models.CatField{}
	for _, sid := range ids {
		if sid == "" {
			continue
		}
		cid, err := strconv.Atoi(sid)
		if err != nil {
			continue
		}
		cate, err := c.Find(uint64(cid))
		if err != nil {
			continue
		}
		catFields := []models.CatField{}
		err = db.Instance().Get().Where("catid=?", cid).Order("id desc").Find(&catFields).Error
		if err != nil {
			return nil, err
		}
		for _, field := range catFields {
			field.Cat = cate
			field.Fieldsort = field.ID * 5
			if field.Parentid > 0 && field.Catid != int(id) {
				parentIds = append(parentIds, field.Parentid)
			}
			if field.Parentid > 0 {
				chidren[field.Parentid] = field
			}
			if !contains(parentIds, field.ID) {
				field.HasChild = false
				if v, ok := chidren[field.ID]; ok {
					data = append([]models.CatField{v}, data...)
					field.HasChild = true
				}
				data = append([]models.CatField{field}, data...)
			}

		}

	}
	data = removeDuplicated(data)
	allsorted := map[string]string{}
	allstatus := map[string]string{}
	for _, sid := range ids {
		if sid == "" {
			continue
		}
		cid, err := strconv.Atoi(sid)
		if err != nil {
			continue
		}
		var load models.CatFieldOverload
		err = db.Instance().Get().Model(&load).Find(&load, cid).Error
		if err == nil {
			sorted := map[string]string{}
			status := map[string]string{}
			json.Unmarshal([]byte(load.Status), &status)
			json.Unmarshal([]byte(load.Sorted), &sorted)
			for k, v := range status {
				allstatus[k] = v
			}
			for k, v := range sorted {
				allsorted[k] = v
			}
		}

	}
	for _, v := range data {
		if allstatus[strconv.Itoa(v.ID)] == "0" {
			v.Isenable = 0
		}
		if vv, ok := allsorted[strconv.Itoa(v.ID)]; ok {
			v.Fieldsort, _ = strconv.Atoi(vv)
		}
		cfs = append(cfs, v)
	}
	return cfs, nil
}

func (c *catService) Find(id uint64) (*models.Cat, error) {
	var cat models.Cat
	err := db.Instance().Get().Model(cat).Where("id=? and active=1", id).First(&cat).Error
	return &cat, err
}

func (c *catService) FindAllByLevel(level int, offset, limit int) ([]schemas.Cat, error) {
	var cats []schemas.Cat
	err := db.Instance().Get().Select("id,cat_name,  cat_en, cat_trace, cat_trace_en, parent_id").Where("level=? and active=1", level).Limit(limit).Offset(offset).Find(&cats).Error
	return cats, err
}

func (c *catService) FindSubsByPid(pid uint64, offset, limit int) ([]schemas.Cat, error) {
	var cats []schemas.Cat
	err := db.Instance().Get().Select("id,cat_name,  cat_en, cat_trace, cat_trace_en, parent_id").Where("parent_id=? and active=1", pid).Limit(limit).Offset(offset).Find(&cats).Error
	return cats, err
}

func (c *catService) FindAll(limit, offset int) ([]schemas.Cat, error) {
	var cats []schemas.Cat
	err := db.Instance().Get().Select("id,cat_name, cat_en, cat_trace, cat_trace_en, parent_id").Where("active=1").Limit(limit).Offset(offset).Find(&cats).Error
	return cats, err
}

func (c *catService) FindNames(ids []string) ([]string, error) {
	var items []schemas.Cat
	if err := db.Instance().Get().Where("id in ? ", ids).Find(&items).Error; err != nil {
		return nil, err
	}
	if len(items) == 0 {
		return nil, nil
	}
	m := make(map[int]string)
	for _, item := range items {
		m[int(item.ID)] = item.CatName
	}
	var ret []string
	for _, id := range ids {
		if id != "0" {
			idInt, _ := strconv.Atoi(id)
			ret = append(ret, m[idInt])
		}
	}
	return ret, nil
}

func (c *catService) GenJs() string {
	var cats []models.Cat
	conn := db.GetRedisConn()
	defer conn.Close()
	const key = "js-allcats"
	if data, err := redis.String(conn.Do("get", key)); err == nil && data != "" {
		return data
	}
	if err := db.Instance().Get().Where("active=1").Order("parent_id asc").Order("order_id  asc").Select("id", "cat_name", "shorttitle", "parent_id", "level", "cat_trace").Find(&cats).Error; err != nil {
		return ""
	} else {
		allcat := c.GenLevelTree(cats)
		if data, err := json.Marshal(allcat); err == nil {
			result := `var allcats = ` + string(data) + ";"
			if _, err := conn.Do("setex", key, 86400, result); err != nil {
				return ""
			}
			return result
		} else {
			return ""
		}
	}

	return ""
}

func (c *catService) GenLevelTree(cats []models.Cat) map[string]map[string]map[string]string {
	allcat := make(map[string]map[string]map[string]string)
	for _, cat := range cats {
		if strings.Contains(cat.CatTrace, ",5334,") || strings.Contains(cat.CatTrace, ",8430,") || strings.Contains(cat.CatTrace, ",8508,") || cat.ID == 7850 {
			continue
		}
		if cat.Level != 2 {
			cat.CatName = strings.ToLower(cat.CatName)
		} else {
			cat.CatName = cat.Shorttitle
		}
		id := strconv.Itoa(cat.ID)
		pid := strconv.Itoa(cat.ParentID)
		level := strconv.Itoa(cat.Level)
		ppp := allcat[level]
		if ppp == nil {
			ppp = make(map[string]map[string]string)
			allcat[level] = ppp
		}
		pp := ppp[pid]
		if pp == nil {
			pp = make(map[string]string)
			ppp[pid] = pp
		}

		if level == "3" || level == "4" {
			traceids := strings.Split(strings.Trim(cat.CatTrace, ","), ",")
			traceids = traceids[0 : len(traceids)-2]
			pp[id] = cat.CatName + ":" + strings.Join(traceids, ",")
		} else {
			pp[id] = cat.CatName
		}
	}
	return allcat
}

func (c *catService) GenJs2() string {
	var cats []models.Cat
	conn := db.GetRedisConn()
	defer conn.Close()
	const key = "js-allcats-tree-v3"
	if data, err := redis.String(conn.Do("get", key)); err == nil && len(data) > 100 {
		return data
	}
	if err := db.Instance().Get().Where("active=1").Order("parent_id asc").Order("order_id  asc").Select("id", "cat_name", "shorttitle", "parent_id", "level", "cat_trace").Find(&cats).Error; err != nil {
		return ""
	} else {
		allcat := c.GenLevelTree(cats)
		tree := NewTree(allcat)
		if data, err := json.Marshal(tree.Children); err == nil {
			result := `var allcats = ` + string(data) + ";"
			if _, err := conn.Do("setex", key, 86400, result); err != nil {
				return ""
			}
			return result
		} else {
			return ""
		}
	}

	return ""
}

type CatNode struct {
	Name     string     `json:"n"`
	Id       string     `json:"v"`
	IsLeaf   bool       `json:"f"`
	Children []*CatNode `json:"c"`
	Parent   *CatNode   `json:"-"`
}

func NewTree(levtree map[string]map[string]map[string]string) CatNode {
	all := make(map[string]*CatNode)
	root := CatNode{
		Name:     "",
		Id:       "0",
		IsLeaf:   false,
		Children: nil,
		Parent:   nil,
	}
	all["0"] = &root
	for i := 1; i < 5; i++ {
		for pid, leafs := range levtree[fmt.Sprintf("%d", i)] {
			for id, name := range leafs {
				parent := all[pid]
				if parent == nil {
					continue
				}
				node := &CatNode{
					Name:     strings.Split(name, ":")[0],
					Id:       id,
					IsLeaf:   true,
					Children: nil,
					Parent:   parent,
				}
				all[id] = node
				parent.Children = append(parent.Children, node)
				parent.IsLeaf = false
			}
		}
	}
	return root
}
