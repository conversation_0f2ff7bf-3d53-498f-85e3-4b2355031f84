package services

import (
	"gitlab.com/all_publish/api/test"
	"testing"
)

func TestInfosService_Add(t *testing.T) {
	test.InitTest(t)
	NewInfosService().Add(1, "http://www.baidu.com", 1, 0)
}

func TestInfosService_FindInfoByUrl(t *testing.T) {
	test.InitTest(t)
	t.Log(NewInfosService().FindInfoByUrl("http://jiaoyu.huangye88.com/xinxi/0d193044kcc42f.html"))
	t.Log(NewInfosService().FindInfoByUrl("http://m.huangye88.com/d-hunan/9820-0d193044kcc42f.html"))
}

func TestUrl2pc(t *testing.T) {
	test.InitTest(t)
	uts := []struct {
		input          string
		expectOrigin   string
		expectPc       string
		expectAicaigou bool
	}{
		{
			"http://u606591.cn.b2b168.com/shop/supply/199619963.html",
			"http://u606591.cn.b2b168.com/shop/supply/199619963.html",
			"http://u606591.cn.b2b168.com/shop/supply/199619963.html",
			false,
		},
		{
			"http://m.u606591.b2b168.com/supply/v199619963.html",
			"http://m.u606591.b2b168.com/supply/v199619963.html",
			"http://u606591.cn.b2b168.com/shop/supply/199619963.html",
			false,
		},
		{
			"http://m.u606591.b2b168.com/supply/v199619963.html#b2b.baidu.com",
			"http://m.u606591.b2b168.com/supply/v199619963.html",
			"http://u606591.cn.b2b168.com/shop/supply/199619963.html",
			true,
		},
		{
			"http://mip.b2b168.com/wvs*********.html",
			"http://mip.b2b168.com/wvs*********.html",
			"http://u808009.cn.b2b168.com/shop/supply/*********.html",
			false,
		},
		{
			"http://wap.b2b168.com/wvs*********.html",
			"http://wap.b2b168.com/wvs*********.html",
			"http://u808009.cn.b2b168.com/shop/supply/*********.html",
			false,
		},
		{
			"https://www.china.cn/qtpeidianshudiansheb/4940646022.html",
			"https://www.china.cn/qtpeidianshudiansheb/4940646022.html",
			"https://www.china.cn/qtpeidianshudiansheb/4940646022.html",
			false,
		},
		{
			"https://site.china.cn/qtpeidianshudiansheb/4940646022.html",
			"https://site.china.cn/qtpeidianshudiansheb/4940646022.html",
			"https://www.china.cn/qtpeidianshudiansheb/4940646022.html",
			false,
		},
		{
			"https://m.51sole.com/shop/u06591/companyproductdetail_317853335.htm",
			"https://m.51sole.com/shop/u06591/companyproductdetail_317853335.htm",
			"http://e.51sole.com/u06591/companyproductdetail_317853335.htm",
			false,
		},
		{
			"https://e.51sole.com/u06591/companyproductdetail_317853335.htm",
			"https://e.51sole.com/u06591/companyproductdetail_317853335.htm",
			"http://e.51sole.com/u06591/companyproductdetail_317853335.htm",
			false,
		},
		{
			"https://m.dd.51sole.com/shop/u06591/companyproductdetail_317853335.htm",
			"https://m.dd.51sole.com/shop/u06591/companyproductdetail_317853335.htm",
			"",
			false,
		},
		{
			"http://xuchang.huangye88.com/xinxi/946_03uq82g58a3c7.html",
			"http://xuchang.huangye88.com/xinxi/946_03uq82g58a3c7.html",
			"http://xuchang.huangye88.com/xinxi/946_03uq82g58a3c7.html",
			false,
		},
		{
			"http://m.huangye88.com/d-xuchang/946-03uq82g58a3c7.html",
			"http://m.huangye88.com/d-xuchang/946-03uq82g58a3c7.html",
			"http://xuchang.huangye88.com/xinxi/946_03uq82g58a3c7.html",
			false,
		},
		{
			"http://wujin.huangye88.com/xinxi/f92i606lg348db.html",
			"http://wujin.huangye88.com/xinxi/f92i606lg348db.html",
			"http://wujin.huangye88.com/xinxi/f92i606lg348db.html",
			false,
		},
		{
			"http://m.huangye88.com/wujin/f92i606lg348db.html",
			"http://m.huangye88.com/wujin/f92i606lg348db.html",
			"http://wujin.huangye88.com/xinxi/f92i606lg348db.html",
			false,
		},
		{
			"http://www.liebiao.com/pmjytlcj/",
			"http://www.liebiao.com/pmjytlcj/",
			"",
			false,
		},
		{
			"http://chengdu.liebiao.com/jiuhuo/594689254.html",
			"http://chengdu.liebiao.com/jiuhuo/594689254.html",
			"http://chengdu.liebiao.com/jiuhuo/594689254.html",
			false,
		},
		{
			"http://bjwtyanjiuhs.shop.liebiao.com/",
			"http://bjwtyanjiuhs.shop.liebiao.com/",
			"http://bjwtyanjiuhs.shop.liebiao.com/",
			false,
		},
		{
			"http://m.liebiao.com/chengdu/jiuhuo/594689254.html",
			"http://m.liebiao.com/chengdu/jiuhuo/594689254.html",
			"http://chengdu.liebiao.com/jiuhuo/594689254.html",
			false,
		},
		{
			"http://bjwtyanjiuhs.shop.m.liebiao.com/",
			"http://bjwtyanjiuhs.shop.m.liebiao.com/",
			"http://bjwtyanjiuhs.shop.liebiao.com/",
			false,
		},

		{
			"https://shanghai.baixing.com/wupinhuishou/a2560349545.html",
			"https://shanghai.baixing.com/wupinhuishou/a2560349545.html",
			"http://shanghai.baixing.com/wupinhuishou/a2560349545.html",
			false,
		},
		{
			"https://shanghai.baixing.com/m/wupinhuishou/a2560349545.html",
			"https://shanghai.baixing.com/m/wupinhuishou/a2560349545.html",
			"http://shanghai.baixing.com/wupinhuishou/a2560349545.html",
			false,
		},
		{
			"https://shanghai.baixing.com/wupinhuishou/b1822803067.html",
			"https://shanghai.baixing.com/wupinhuishou/b1822803067.html",
			"http://shanghai.baixing.com/wupinhuishou/b1822803067.html",
			false,
		},
		{
			"https://www.912688.com/supply/312849828.html",
			"https://www.912688.com/supply/312849828.html",
			"https://www.912688.com/supply/312849828.html",
			false,
		},
		{
			"https://mip.912688.com/supply/312849828.html",
			"https://mip.912688.com/supply/312849828.html",
			"https://www.912688.com/supply/312849828.html",
			false,
		},
		{
			"https://m.912688.com/supply/312849828.html",
			"https://m.912688.com/supply/312849828.html",
			"https://www.912688.com/supply/312849828.html",
			false,
		},
		{
			"https://wap.912688.com/supply/312849828.html",
			"https://wap.912688.com/supply/312849828.html",
			"https://www.912688.com/supply/312849828.html",
			false,
		},

		{
			"http://changsha.kuyiso.com/zufang/7317831g6085b.htm",
			"http://changsha.kuyiso.com/zufang/7317831g6085b.htm",
			"http://changsha.kuyiso.com/zufang/7317831g6085b.htm",
			false,
		},
		{
			"http://wap.kuyiso.com/changsha/zufang/7317831g6085b.htm",
			"http://wap.kuyiso.com/changsha/zufang/7317831g6085b.htm",
			"http://changsha.kuyiso.com/zufang/7317831g6085b.htm",
			false,
		},
	}
	for _, u := range uts {
		if o, p, a := Url2pc(u.input); o == u.expectOrigin && p == u.expectPc && a == u.expectAicaigou {
			t.Log("ok")
		} else {
			t.Log(u.input)
			if o != u.expectOrigin {
				t.Errorf("origin:expect %s, real:%s", u.expectOrigin, o)
			}
			if p != u.expectPc {
				t.Errorf("pc:expect %s, real:%s", u.expectPc, p)
			}
		}
	}
}
