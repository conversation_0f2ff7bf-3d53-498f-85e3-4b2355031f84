package services

import (
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/test"
	"testing"
)

func TestImageService_AddToAlbum(t *testing.T) {
	test.InitTest(t)
	type args struct {
		image   models.Image
		albumId uint64
	}

	n, _ := NewAlbumService(db.Instance().Get()).Create("haha", "1", "aaa", 4)
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			"t1",
			args{
				models.Image{
					CompanyId: 4,
					Hash:      "22332",
				},
				n.ID,
			},
			false,
		},

		{
			"t1",
			args{
				models.Image{
					CompanyId: 4,
					Hash:      "22332",
				},
				n.ID,
			},
			true,
		},
		{
			"t1",
			args{
				models.Image{
					CompanyId: 4,
					Hash:      "22332",
				},
				1222222,
			},
			true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//	svc := &ImageService{}
			//	if err := svc.AddToAlbum(tt.args.image, tt.args.albumId); (err != nil) != tt.wantErr {
			//		t.Errorf("AddToAlbum() error = %v, wantErr %v", err, tt.wantErr)
			//	}
		})
	}
}

func TestImageService_GetByUrl(t *testing.T) {
	test.InitTest(t)
	type args struct {
		url       string
		companyId uint64
	}
	tests := []struct {
		name    string
		args    args
		want    interface{}
		wantErr bool
	}{

		{name: "has hy_url", args: args{
			url:       "https://img0.paihang8.com/6/72/WechatIMG29.jpeg",
			companyId: 6,
		},
			want:    "",
			wantErr: false,
		},
		{name: "has no hy_url",
			args: args{
				url:       "https://img0.paihang8.com/6/72/WechatIMG28.jpeg",
				companyId: 6,
			},
			want:    []byte{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//svc := &ImageService{}
			//got, err := svc.GetByUrl(tt.args.url, tt.args.companyId)
			//if (err != nil) != tt.wantErr {
			//	t.Errorf("GetByUrl() error = %v, wantErr %v", err, tt.wantErr)
			//	return
			//}
			//if reflect.TypeOf(got) != reflect.TypeOf(tt.want) {
			//	t.Errorf("GetByUrl() got = %v, want %v", got, tt.want)
			//}
		})
	}
}

//func TestImageService_Update(t *testing.T) {
//	type args struct {
//		url       string
//		companyId uint64
//		hyurl     string
//	}
//	tests := []struct {
//		name    string
//		args    args
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			svc := &ImageService{}
//			if err := svc.Update(tt.args.url, tt.args.companyId, tt.args.hyurl); (err != nil) != tt.wantErr {
//				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}{
//		// TODO: Add test cases.
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			svc := &ImageService{}
//			if err := svc.Update(tt.args.url, tt.args.companyId, tt.args.hyurl); (err != nil) != tt.wantErr {
//				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}

func TestNewImageService(t *testing.T) {
	test.InitTest(t)
}
