package services

import (
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services/service"
	"gitlab.com/all_publish/api/pkg/db"
)

var stats Stats

type Stats struct {
}

func NewStatsService() *Stats {
	return &stats
}

type UserPubStatsPaginator struct {
	Items     []models.UserPubStat `json:"items"`
	Paginator service.Paginator    `json:"paginator"`
}

func (s *Stats) Getkeywords(limit int) []string {
	rds := db.GetRedisConn()
	defer rds.Close()
	if limit > 100 {
		limit = 100
	}
	items := []string{}
	if data, err := rds.Do("zRange", keywordQueue, 0, limit-1); err == nil {
		for _, item := range data.([]interface{}) {
			word := string(item.([]uint8))
			if _, err := rds.Do("zRem", keywordQueue, word); err == nil {
				items = append(items, word)
			}
		}
	}
	return items
}

func (s *Stats) GetList(limit, offset int, advisorId int, keyword, sort, order string) (*UserPubStatsPaginator, error) {
	var items []models.UserPubStat
	var total int64
	query := db.Instance().Get()
	if keyword != "" {
		keyword = "%" + service.EscapeLike(keyword) + "%"
		query = query.Where("user_name like ? or advisor_name like ? or company_name like ?", keyword, keyword, keyword)
	}
	if advisorId > 0 {
		query = query.Where("advisor_id=?", advisorId)
	}
	query = query.Order(sort + " " + order)
	if err := query.Limit(limit).Offset(offset).Find(&items).Error; err != nil {
		return nil, err
	}
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}
	return &UserPubStatsPaginator{
		Items:     items,
		Paginator: service.NewPaginatorByOffset(offset, limit, int(total)),
	}, nil
}

func (s *Stats) GetDetail(cid uint64) (item *models.UserPubStatDetails, err error) {
	query := db.Instance().Get()
	if err := query.Where("company_id = ?", cid).First(&item).Error; err != nil {
		return nil, err
	}
	return item, nil
}
