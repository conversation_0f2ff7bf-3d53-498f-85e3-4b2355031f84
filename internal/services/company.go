package services

import (
	"fmt"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services/service"
	"gitlab.com/all_publish/api/pkg/db"
)

type CompanyServicer interface {
	GetCompanyIdsOfAdvisor(advisorId uint64) ([]uint64, error)
	Search(keyword string, waitAudit bool, advisorId uint64,
		sortby, order string, limit, offset int) ([]models.Company, error)
	Find(id uint64) (*models.Company, error)
	Save(m *models.Company) error
}

type CompanyService struct {
}

func NewCompanyService() CompanyServicer {
	return &CompanyService{}
}

func (service *CompanyService) GetCompanyIdsOfAdvisor(advisorId uint64) ([]uint64, error) {
	var companies []models.Company
	if err := db.Instance().Get().Where("advisor_id = ?", advisorId).Find(&companies).Error; err != nil {
		return nil, err
	}
	var ids []uint64
	for _, company := range companies {
		ids = append(ids, company.ID)
	}
	return ids, nil
}

func (svc *CompanyService) Search(keyword string, waitAudit bool, advisorId uint64,
	sortby, order string, limit, offset int) ([]models.Company, error) {
	var companys []models.Company
	// Get all matched records
	var dbHelp = db.Instance().Get()
	if keyword != "" {
		if service.IsPhone(keyword) {
			dbHelp = dbHelp.Where(`JSON_SEARCH(phone, "all", "?")`, keyword)
		} else {
			dbHelp = dbHelp.Where("name like ?", "%"+service.EscapeLike(keyword)+"%")
		}
	}
	if waitAudit {
		dbHelp = dbHelp.Where("auditing_fields is not NULL")
	}
	if advisorId > 0 {
		dbHelp = dbHelp.Where("advisor_id = ?", advisorId)
	}
	dbHelp = dbHelp.Order(fmt.Sprintf("%s %s", sortby, order))
	dbHelp = dbHelp.Limit(limit).Offset(offset)
	if err := dbHelp.Find(&companys).Error; err != nil {
		return nil, err
	} else {
		return companys, nil
	}
}

func (svc *CompanyService) Find(id uint64) (*models.Company, error) {
	var c models.Company
	if e := db.Instance().Get().Find(&c, id).Error; e != nil {
		return nil, e
	}
	return &c, nil
}

func (svc *CompanyService) Save(m *models.Company) error {
	return db.Instance().Get().Save(m).Error
}
