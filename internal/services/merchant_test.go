package services

import (
	"testing"

	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/test"
)

func TestMerchantService_GetByCompanyId(t *testing.T) {
	test.InitTest(t)
	t.Run("exist", func(t *testing.T) {
		if data, err := Merchant.GetByCompanyId(6, models.PlatformHy88); err != nil {
			t.Error(err)
		} else if data.CompanyID != 6 || data.PlatForm != models.PlatformHy88 {
			t.Error("data not match")
		}
	})

	t.Run("not exist", func(t *testing.T) {
		if _, err := Merchant.GetByCompanyId(1, models.PlatformHy88); err == nil {
			t.<PERSON>rror("exptected err, but nil")
		}
	})
}

func TestMerchantService_Set2Pass(t *testing.T) {
	test.InitTest(t)
	t.Log(Merchant.Set2Pass(17658253975))
}
