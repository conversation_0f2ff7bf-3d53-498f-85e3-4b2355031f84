package services

import (
	"errors"
	"fmt"
	"git.paihang8.com/lib/goutils/sites/sole"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/services/cat"
	"gitlab.com/all_publish/api/internal/services/service"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"
)

type MerchantService struct {
}

var Merchant = &MerchantService{}

func (svc *MerchantService) Updates(m *models.Merchant, query interface{}, args ...interface{}) error {
	return db.Instance().Get().Model(m).Select(query, args...).Updates(m).Error
}

func (svc *MerchantService) GetItemsOfCompany(companyId uint64, keyword string, sortby, order string, limit, offset int) ([]models.Merchant, error) {
	var items []models.Merchant
	var dbHelper = db.Instance().Get()
	dbHelper = dbHelper.Where("company_id = ?", companyId)
	com, err := NewCompanyService().Find(companyId)
	if err != nil {
		return nil, err
	}
	if strings.TrimSpace(keyword) != "" {
		dbHelper = dbHelper.Where("name like ?", "%"+service.EscapeLike(keyword)+"%")
	}
	dbHelper = dbHelper.Order(fmt.Sprintf("%s %s", sortby, order))
	dbHelper = dbHelper.Limit(limit).Offset(offset)

	if err := dbHelper.Find(&items).Error; err != nil {
		return nil, err
	}
	for i, item := range items {
		items[i].PlatFormName = models.PlatFormName(item.PlatForm)
		tr, _ := models.GetTrigger(item.PlatForm)
		items[i].CalStatus()
		item.Company = com
		items[i].Account = tr.FormatAccount(&item)
	}
	return items, nil
}

func (svc *MerchantService) GetByCompanyId(companyId uint64, platform models.PlatForm) (*models.Merchant, error) {
	var item models.Merchant
	q := db.Instance().Get()
	q = q.Where("company_id = ?", companyId)
	if platform != models.PlatformAll {
		q = q.Where("plat_form=?", platform)
	}
	if err := q.First(&item).Error; err != nil {
		return nil, err
	}
	item.PlatFormName = models.PlatFormName(item.PlatForm)
	return &item, nil
}

func (svc *MerchantService) GetById(id uint64) (*models.Merchant, error) {
	var item models.Merchant
	q := db.Instance().Get()
	q = q.Where("id = ?", id)
	if err := q.First(&item).Error; err != nil {
		return nil, err
	}
	item.PlatFormName = models.PlatFormName(item.PlatForm)
	return &item, nil
}

func (svc *MerchantService) GetByThirdAccount(key string, value interface{}, p models.PlatForm) (*models.Merchant, error) {
	var item models.Merchant
	q := db.Instance().Get()
	q = q.Where("plat_form=? and target_company_id>0 and JSON_EXTRACT(account, ?)=?", p, "$."+key, value)
	if err := q.First(&item).Error; err != nil {

		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		} else {
			return nil, err
		}
	}
	item.PlatFormName = models.PlatFormName(item.PlatForm)
	return &item, nil
}

func (svc *MerchantService) SaveWithDb(db *gorm.DB, m *models.Merchant) error {
	return db.Save(m).Error
}

func (svc *MerchantService) Save(m *models.Merchant) error {
	return db.Instance().Get().Save(m).Error
}

func (svc *MerchantService) Stop(m *models.Merchant, reason string) {
	m.AutoPub = false
	m.Pause = true
	m.PauseReason = reason
	m.UpdatedAt = dbtypes.SHNow()
	db.Instance().Get().Model(&m).UpdateColumns(models.Merchant{
		AutoPub:     m.AutoPub,
		Pause:       m.Pause,
		PauseReason: m.PauseReason,
		Base: dbtypes.Base{
			UpdatedAt: m.UpdatedAt,
		},
	})
	db.Instance().Get().Save(m)
}

func (svc *MerchantService) IncreasePubbed(m *models.Merchant, inc int) {
	m.PublishedCnt += inc
	m.UpdatedAt = dbtypes.SHNow()
	db.Instance().Get().Model(&m).UpdateColumns(models.Merchant{
		PublishedCnt: m.PublishedCnt,
		Base: dbtypes.Base{
			UpdatedAt: m.UpdatedAt,
		},
	})
	db.Instance().Get().Save(m)
	if m.PublishedCnt >= m.Total && m.Total > 0 {
		svc.Stop(m, "已发完"+strconv.Itoa(m.Total)+"条")
	}
}

func (svc *MerchantService) Create(m *models.Merchant) error {
	return db.Instance().Get().Create(m).Error
}

func (svc *MerchantService) InCertApproving(cid uint64) bool {
	var cnt int64
	db.Instance().Get().Model(&models.Merchant{}).Where("company_id = ? and cert_status=?", cid, dbtypes.CertStatusSubmitted).Count(&cnt)
	return cnt > 0
}

func TempStoped() bool {
	if time.Now().In(dbtypes.SHLocation).After(time.Date(2024, 1, 11, 17, 30, 0, 0, dbtypes.SHLocation)) && time.Now().In(dbtypes.SHLocation).Before(time.Date(2024, 1, 11, 20, 0, 0, 0, dbtypes.SHLocation)) {
		return true
	}
	return false
}

func (svc *MerchantService) GetEnabled(cid uint64, includePause bool, includeExpired bool) ([]models.Merchant, error) {
	var tmp, merchants []models.Merchant
	if err := db.Instance().Get().Where("company_id = ?", cid).Find(&tmp).Error; err != nil {
		return nil, err
	}
	for _, m := range tmp {
		if m.CertStatus != dbtypes.CertStatusPassed {
			svc.AutoFlow(&m)
		} else {
			if m.PlatForm == models.PlatformKuyiso && TempStoped() {
				continue
			}
			if includePause {
				merchants = append(merchants, m)
			} else {
				if m.Pause == false {
					merchants = append(merchants, m)
				}
			}

		}
	}
	if includeExpired {
		return merchants, nil
	}
	if len(merchants) > 0 {
		if ps, err := NewUserPlatformService().GetAutoPostedPlatForms(cid); err != nil {
			return nil, err
		} else {
			if len(ps) == 0 {
				return nil, nil
			}
			ret := []models.Merchant{}
			for _, m := range merchants {
				ok := false

				for _, p := range ps {
					if m.PlatForm == p {
						ret = append(ret, m)
						ok = true
						break
					}
				}
				if !ok {
					m.Pause = true
					m.PauseReason = "服务已过期,请续费"
					db.Instance().Get().Select("pause", "pause_reason").Updates(&m)
				}
			}
			return ret, nil
		}
	}
	return nil, nil
}

func (svc *MerchantService) AutoFlow(m *models.Merchant) {
	if tr, ok := models.GetTrigger(m.PlatForm); ok {
		if c, err := NewCompanyService().Find(m.CompanyID); err == nil {
			tr.AutoFlow(m, c, false)
		}
	}
}

func (svc *MerchantService) CanPublish(p *models.Product, platform models.PlatForm) (bool, error) {
	var err error
	if p.GetAreaID() != 0 {
		_, err = Area.GetMapping(uint64(p.GetAreaID()), platform)
		if err != nil {
			return false, fmt.Errorf("找不到对应的地区信息，%s不能发布", models.PlatFormName(platform))
		}
	}
	if p.GetCatID() != 0 {
		_, err = cat.NewCatService().GetMapping(uint64(p.GetCatID()), platform)
		if err != nil {
			return false, fmt.Errorf("找不到对应的分类，%s不能发布", models.PlatFormName(platform))
		}
	}
	if p.Platforms != nil {
		var dbps models.Platforms
		dbps.Init(p.Platforms)
		if v, ok := dbps[platform]; ok && v.On == false {
			return false, errors.New(dbps[platform].Name + " 已关闭")
		}
	}

	return true, nil
}

// 给爱采购用，有些特殊用户 在 百度无法通过审核，得手动改
func (svc *MerchantService) Set2Pass(phone int) error {
	if u, err := NewUserService().GetUserByPhone(phone); err != nil {
		return err
	} else {
		var dbHelper = db.Instance().Get()
		dbHelper = dbHelper.Where("company_id = ? and plat_form=?", u.CompanyID, models.PlatformSoleExt)
		var m models.Merchant
		if err := dbHelper.First(&m).Error; err != nil {
			return err
		}
		var account merchants.AicaigouAccount
		var certData merchants.CertData
		account.Init(m.Account)
		certData.Init(m.CertData)
		account.SoleCertStatus = dbtypes.CertStatusPassed
		account.SoleCertReason = ""
		account.Reason = ""
		account.Status = sole.UserAuditStatusPassed
		account.Joined = true
		account.Ordered = true
		m.SetAccount(account)
		return db.Instance().Get().Save(&m).Error
	}
}
