package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"git.paihang8.com/lib/goutils/sites/qiancipai"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/services/service"
	"gitlab.com/all_publish/api/pkg/cachex"
	"sort"
)

type CommonPaginator struct {
	Items     interface{}       `json:"items"`
	Paginator service.Paginator `json:"paginator"`
	Num       int               `json:"num"`
	ChargeUrl string            `json:"charge_url"`
}

func GetKeywords(cid uint64, kw string, limit, offset int) (*CommonPaginator, error) {
	userKey := fmt.Sprintf("keyword-%d-%s", cid, kw)
	userStat, err := NewUserStatService().GetStatByCompanyId(cid)
	if err != nil {
		return nil, err
	}
	if userStat.DigKeywordUsed >= userStat.DigKeywordLimit {
		return nil, configs.ErrQuotaOut
	}

	cacheKey := "keyword-" + kw
	var md qiancipai.KeywordsData
	if v, ok := cachex.Get(cacheKey); ok && v != "" {
		json.Unmarshal([]byte(v), &md)
	} else {
		if data, err := qiancipai.New().GetKeywords(kw); err != nil {
			return nil, err
		} else {
			v, _ := json.Marshal(data)
			cachex.SetEx(cacheKey, v, 86400)
			md = *data

		}
	}
	if offset > md.Total || offset < 0 {
		return nil, errors.New("没那么多数据")
	}
	var result CommonPaginator
	end := offset + limit
	if end > len(md.KeywordList) {
		end = len(md.KeywordList)
	}
	if len(md.KeywordList) > 0 {
		sort.Slice(md.KeywordList, func(i, j int) bool {
			return len(md.KeywordList[i]) < len(md.KeywordList[j])
		})
	}
	result.Paginator = service.NewPaginatorByOffset(offset, limit, md.Total)
	result.Items = md.KeywordList[offset:end]
	if _, ok := cachex.Get(userKey); !ok {
		if md.Total > 0 {
			userStat.DigKeywordUsed += 1
			NewUserStatService().Update(userStat)
			cachex.SetEx(userKey, []byte("used"), 86400)
		}
	}
	result.Num = userStat.DigKeywordLimit - userStat.DigKeywordUsed
	return &result, nil
}

func GetMaterials(cid uint64, kw string, limit, offset int) (*CommonPaginator, error) {
	userKey := fmt.Sprintf("material-%d-%s", cid, kw)
	userStat, err := NewUserStatService().GetStatByCompanyId(cid)
	if err != nil {
		return nil, err
	}
	if userStat.DigMaterialsUsed >= userStat.DigMaterialsLimit {
		return nil, configs.ErrQuotaOut
	}
	cacheKey := "materials-" + kw
	var md qiancipai.MaterialsData
	if v, ok := cachex.Get(cacheKey); ok && v != "" {
		json.Unmarshal([]byte(v), &md)
	} else {
		if data, err := qiancipai.New().GetMaterials(kw); err != nil {
			return nil, err
		} else {
			v, _ := json.Marshal(data)
			cachex.SetEx(cacheKey, v, 86400)
			md = *data
		}
	}
	if offset > md.Total || offset < 0 {
		return nil, errors.New("没那么多数据")
	}
	var result CommonPaginator
	end := offset + limit
	if end > len(md.IntroList) {
		end = len(md.IntroList)
	}
	result.Paginator = service.NewPaginatorByOffset(offset, limit, md.Total)
	result.Items = md.IntroList[offset:end]
	if _, ok := cachex.Get(userKey); !ok {
		if md.Total > 0 {
			userStat.DigMaterialsUsed += 1
			NewUserStatService().Update(userStat)
			cachex.SetEx(userKey, []byte("used"), 86400)
		}
	}
	result.Num = userStat.DigMaterialsLimit - userStat.DigMaterialsUsed
	return &result, nil
}
