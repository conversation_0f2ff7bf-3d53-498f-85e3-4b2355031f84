package services

import (
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"git.paihang8.com/lib/goutils/brower"
	"git.paihang8.com/lib/goutils/sites/hy88/seeker"
	"github.com/gomodule/redigo/redis"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/convert"
	"gitlab.com/all_publish/api/pkg/db"
)

type InfosService struct {
}

var single InfosService

func NewInfosService() *InfosService {
	return &single
}

func (i *InfosService) Add(cid uint64, url string, mid uint64, platform models.PlatForm) error {
	if cid == 0 || url == "" || mid == 0 {
		return errors.New("param invalid")
	}
	var item models.Infos
	item.CompanyID = cid
	item.Url = url
	item.Platform = platform
	item.MID = mid

	return db.Instance().Get().Create(&item).Error
}

/*
*
因为商铺url没啥规律。所以得先查商铺，再查信息
1. aicaigou b2b.baidu.com
2. sole.com
3. huangye88.com
4. b2b168.com
5. china.com
6.liebiao.com
7.baixing.com
8.912688.com
*/
func (i *InfosService) FindInfoByUrl(url string) (*models.Infos, error) {
	fromAicaigou := false
	if strings.Contains(url, "b2b.baidu.com") {
		fromAicaigou = true
		url = strings.ReplaceAll(url, "#b2b.baidu.com", "")
	}
	rds := db.GetRedisConn()
	defer rds.Close()
	key := "url:" + url
	item := models.Infos{}
	if v, _ := redis.String(rds.Do("get", key)); v != "" {
		parts := strings.Split(v, "#")
		if len(parts) != 2 {
			return nil, errors.New(v)
		}
		item.CompanyID = convert.Str(parts[0]).MustUInt64()
		item.Platform = models.PlatForm(convert.Str(parts[1]).MustInt())
		if item.Platform == models.PlatformHy88 {
			return nil, errors.New("do not record hy88")
		}
		return &item, nil
	}

	var m models.Merchant
	if err := db.Instance().Get().Where("company_site=?", "http:"+url).First(&m).Error; err == nil {
		if fromAicaigou {
			m.PlatForm = models.PlatformSoleExt
		}
		rds.Do("set", key, fmt.Sprintf("%d#%d", m.CompanyID, m.PlatForm))
		item.CompanyID = m.CompanyID
		item.Platform = m.PlatForm
		return &item, nil
	}

	if err := db.Instance().Get().Where("company_site=?", "https:"+url).First(&m).Error; err == nil {
		if fromAicaigou {
			m.PlatForm = models.PlatformSoleExt
		}
		rds.Do("set", key, fmt.Sprintf("%d#%d", m.CompanyID, m.PlatForm))
		item.CompanyID = m.CompanyID
		item.Platform = m.PlatForm
		return &item, nil
	}
	if strings.Contains(url, "huangye88.com") {
		// 黄页88的不再记录快照
		return nil, errors.New("do not record hy88")
		if uid, err := seeker.NewSeekerApi(brower.NewHttpBrower(false, false)).DetectUrl(url); err == nil {
			if u, err := NewUserService().GetUserByOem(uid); err == nil {
				item.Platform = models.PlatformHy88
				item.Url = url
				item.CompanyID = u.CompanyID
				rds.Do("set", key, fmt.Sprintf("%d#%d", item.CompanyID, item.Platform))
				return &item, nil
			}
		}
	}
	_, pc, _ := Url2pc(url)
	if pc != "" {
		if err := db.Instance().Get().Where("url = ?", pc).First(&item).Error; err == nil {
			if fromAicaigou {
				item.Platform = models.PlatformSoleExt
			}
			rds.Do("set", key, fmt.Sprintf("%d#%d", item.CompanyID, item.Platform))
			return &item, nil
		}
	}
	// 设置缓存时间为 1 小时
	cacheExpiration := time.Hour * 24
	err := errors.New("record not found")
	rds.Do("set", key, err.Error(), "ex", int(cacheExpiration.Seconds()))
	return nil, err
}

/*
*
返回pc为空 表示不支持，这样可以避免sql查询。
*/
func Url2pc(u string) (origin, pc string, fromAicaigou bool) {
	if strings.Contains(u, "b2b.baidu.com") {
		fromAicaigou = true
		u = strings.ReplaceAll(u, "#b2b.baidu.com", "")
	}
	origin = u
	bafangmReg := regexp.MustCompile(`https?://m.(u\d+).b2b168.com/supply/v(\d+).html`)
	mt := bafangmReg.FindStringSubmatch(origin)
	if len(mt) == 3 {
		pc = fmt.Sprintf("http://%s.cn.b2b168.com/shop/supply/%s.html", mt[1], mt[2])
		return
	}
	bafangReg := regexp.MustCompile(`https?://(u\d+).cn.b2b168.com/shop/supply/(\d+).html`)
	mt = bafangReg.FindStringSubmatch(origin)
	if len(mt) == 3 {
		pc = origin
		return
	}

	bafangmipReg := regexp.MustCompile(`http://(wap|mip).b2b168.com/wvs(\d+).html`)
	mt = bafangmipReg.FindStringSubmatch(origin)
	if len(mt) == 3 {
		item := models.Infos{}
		if err := db.Instance().Get().Where("m_id=? and platform=?", mt[2], models.PlatformBafang).First(&item).Error; err == nil {
			pc = item.Url
			return
		}

	}

	chinamReg := regexp.MustCompile(`https://site.china.cn/([^/]+)/(\d+).html`)
	mt = chinamReg.FindStringSubmatch(origin)
	if len(mt) == 3 {
		pc = fmt.Sprintf("https://www.china.cn/%s/%s.html", mt[1], mt[2])
		return
	}
	chinaReg := regexp.MustCompile(`https://www.china.cn/[^/]+/\d+.html`)
	mt = chinaReg.FindStringSubmatch(origin)
	if len(mt) == 1 {
		pc = origin
		return
	}

	solemReg := regexp.MustCompile(`https?://m.51sole.com/shop/u(\d+)/companyproductdetail_(\d+).htm`)
	mt = solemReg.FindStringSubmatch(origin)
	if len(mt) == 3 {
		pc = fmt.Sprintf("http://e.51sole.com/u%s/companyproductdetail_%s.htm", mt[1], mt[2])
		return
	}
	soleReg := regexp.MustCompile(`https?://e.51sole.com/u\d+/companyproductdetail_\d+.htm`)
	mt = soleReg.FindStringSubmatch(origin)
	if len(mt) == 1 {
		pc = strings.ReplaceAll(origin, "https", "http")
		return
	}

	mReg := regexp.MustCompile(`http://m.huangye88.com/d-([^/]+)/(\d+)-(\w+).html`)
	mt = mReg.FindStringSubmatch(origin)
	if len(mt) == 4 {
		pc = fmt.Sprintf("http://%s.huangye88.com/xinxi/%s_%s.html", mt[1], mt[2], mt[3])
		return
	}
	pcReg := regexp.MustCompile(`http://[^.]+.huangye88.com/xinxi/\d+_\w+.html`)
	mt = pcReg.FindStringSubmatch(origin)
	if len(mt) == 1 {
		pc = origin
		return
	}

	mReg = regexp.MustCompile(`http://m.huangye88.com/(\w+)/(\w+).html`)
	mt = mReg.FindStringSubmatch(origin)
	if len(mt) == 3 {
		pc = fmt.Sprintf("http://%s.huangye88.com/xinxi/%s.html", mt[1], mt[2])
		return
	}
	pcReg = regexp.MustCompile(`http://\w+.huangye88.com/xinxi/\w+.html`)
	mt = pcReg.FindStringSubmatch(origin)
	if len(mt) == 1 {
		pc = origin
		return
	}

	pcReg = regexp.MustCompile(`http://(\w+).liebiao.com/(\w+)/(\d+).html`)
	mt = pcReg.FindStringSubmatch(origin)
	if len(mt) == 4 {
		pc = origin
		return
	}
	pcReg = regexp.MustCompile(`http://(\w+).shop.liebiao.com/`)
	mt = pcReg.FindStringSubmatch(origin)
	if len(mt) == 2 {
		pc = origin
		return
	}

	mReg = regexp.MustCompile(`http://m.liebiao.com/(\w+)/(\w+)/(\d+).html`)
	mt = mReg.FindStringSubmatch(origin)
	if len(mt) == 4 {
		pc = fmt.Sprintf("http://%s.liebiao.com/%s/%s.html", mt[1], mt[2], mt[3])
		return
	}
	mReg = regexp.MustCompile(`http://(\w+).shop.m.liebiao.com/`)
	mt = mReg.FindStringSubmatch(origin)
	if len(mt) == 2 {
		pc = fmt.Sprintf("http://%s.shop.liebiao.com/", mt[1])
		return
	}

	pcReg = regexp.MustCompile(`https://(\w+).baixing.com/(\w+)/(\w)(\d+).html`)
	mt = pcReg.FindStringSubmatch(origin)
	if len(mt) == 5 {
		pc = fmt.Sprintf("http://%s.baixing.com/%s/%s%s.html", mt[1], mt[2], mt[3], mt[4])
		return
	}
	mReg = regexp.MustCompile(`https://(\w+).baixing.com/m/(\w+)/(\w)(\d+).html`)
	mt = mReg.FindStringSubmatch(origin)
	if len(mt) == 5 {
		pc = fmt.Sprintf("http://%s.baixing.com/%s/%s%s.html", mt[1], mt[2], mt[3], mt[4])
		return
	}
	pcReg = regexp.MustCompile(`https://(\w+).912688.com/supply/(\d+).html`)
	mt = pcReg.FindStringSubmatch(origin)
	if len(mt) == 3 && (mt[1] == "www" || mt[1] == "mip" || mt[1] == "m" || mt[1] == "wap") {
		pc = fmt.Sprintf("https://www.912688.com/supply/%s.html", mt[2])
		return
	}

	pcReg = regexp.MustCompile(`http://(\w+).kuyiso.com/(\w+)/(\w+).htm`)
	mt = pcReg.FindStringSubmatch(origin)
	if len(mt) == 4 {
		pc = origin
		return
	}
	mReg = regexp.MustCompile(`http://wap.kuyiso.com/(\w+)/(\w+)/(\w+).htm`)
	mt = mReg.FindStringSubmatch(origin)
	if len(mt) == 4 {
		pc = fmt.Sprintf("http://%s.kuyiso.com/%s/%s.htm", mt[1], mt[2], mt[3])
		return
	}
	return
}
