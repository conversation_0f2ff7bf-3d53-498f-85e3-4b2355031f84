package services

import (
	"errors"
	"fmt"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"sync"
	"time"
)

type SystemUserService struct {
	cache     map[uint64]models.SystemUser
	cacheTime time.Time
	lock      sync.RWMutex
}

func NewSystemUserService() *SystemUserService {
	return &SystemUserService{
		cache: make(map[uint64]models.SystemUser),
	}
}

func (svc *SystemUserService) Search(keyword string, sortby, order string, limit, offset int) ([]models.SystemUser, error) {
	var users []models.SystemUser
	var dbHelper = db.Instance().Get()
	if keyword != "" {
		dbHelper = dbHelper.Where("name like ?", "%"+keyword+"%")
	}
	dbHelper = dbHelper.Order(fmt.Sprintf("%s %s", sortby, order))
	dbHelper = dbHelper.Limit(limit).Offset(offset)

	if err := dbHelper.Find(&users).Error; err != nil {
		return nil, err
	}
	return users, nil
}

func (svc *SystemUserService) GetUserByName(name string) (*models.SystemUser, error) {
	var u models.SystemUser
	if err := db.Instance().Get().Where("username = ?", name).First(&u).Error; err != nil {
		return nil, err
	}
	if u.Role != nil {
		u.Scopes = dbtypes.DEF_Scopes[*u.Role]
	}
	return &u, nil
}

func (svc *SystemUserService) GetUserByID(id uint64) (*models.SystemUser, error) {
	var u models.SystemUser
	if err := db.Instance().Get().Where("id = ?", id).First(&u).Error; err != nil {
		return nil, err
	}
	if u.Role != nil {
		u.Scopes = dbtypes.DEF_Scopes[*u.Role]
	}
	return &u, nil
}

func (svc *SystemUserService) LoadWithCache(id uint64) (*models.SystemUser, error) {
	svc.lock.Lock()
	defer svc.lock.Unlock()
	if len(svc.cache) == 0 || svc.cacheTime.After(time.Now().Add(-5*time.Minute)) {
		var items []models.SystemUser
		if err := db.Instance().Get().Find(&items).Error; err != nil {
			return nil, err
		}
		for _, item := range items {
			svc.cache[item.ID] = item
		}
		svc.cacheTime = time.Now()
	}
	if item, ok := svc.cache[id]; ok {
		return &item, nil
	}
	return nil, errors.New("no system user found")
}
