package services

import (
	"errors"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
	"gorm.io/gorm"
)

type UserStatServicer interface {
	GetStatByCompanyId(companyId uint64) (*models.UserStat, error)
	Update(item *models.UserStat) error
}

type UserStatService struct {
}

func NewUserStatService() UserStatServicer {
	return &UserStatService{}
}

func (u *UserStatService) GetStatByCompanyId(companyId uint64) (*models.UserStat, error) {
	var item models.UserStat
	if err := db.Instance().Get().Where("company_id = ?", companyId).First(&item).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			item.CompanyID = companyId
			err = db.Instance().Get().Create(&item).Error
			u.cal(&item)
			return &item, nil
		} else {
			return nil, err
		}
	}
	u.cal(&item)
	return &item, nil
}

func (u *UserStatService) Update(item *models.UserStat) error {
	return db.Instance().Get().Save(item).Error
}

func (u *UserStatService) cal(item *models.UserStat) {
	item.CanUploaded = models.PhotoGift + item.BuyPhoto - item.UploadedCnt
}
