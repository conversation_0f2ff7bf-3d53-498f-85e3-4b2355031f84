package product

import (
	"testing"

	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/test"
)

func getNil() *configs.ApiError {
	return nil
}
func TestProductService_UpdateStatusByOpt(t *testing.T) {
	if getNil() == nil {
		t.Log("ok")
	}

	var err interface{}
	err = getNil()
	if err == nil {
		t.Log("ok")
	} else {
		t.Log("not ok")
	}
}

func TestProductService_CalBadWords(t *testing.T) {
	test.InitTest(t)
	var p models.Product
	db.Instance().Get().Find(&p, 1500)
	s := NewProductService()
	s.CalBadWords(&p)
	t.Log(p)
}
