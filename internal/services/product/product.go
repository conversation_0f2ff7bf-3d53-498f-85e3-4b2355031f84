package product

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/cat"
	"gitlab.com/all_publish/api/internal/services/service"
	"gitlab.com/all_publish/api/pkg/log"

	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"go.uber.org/zap"
)

type ProductService struct {
}

var productServicer ProductService

func NewProductService() *ProductService {
	return &productServicer
}

func (service *ProductService) Create(product *models.Product) error {
	if service.NameExist(product.Name, product.CompanyID) {
		return errors.New("产品名称与其他产品重复，请修改。")
	}
	product.ChangedAt = time.Now()
	return db.Instance().Get().Create(&product).Error
}

/*
*
只有editable的字段才会更新p, 保护字段需要手动设置。
*/
func (service *ProductService) Updates(p *models.Product, m map[string]interface{}) error {
	var query = ""
	var args = []interface{}{}

	for k := range m {
		if query == "" {
			query = k
		} else {
			args = append(args, k)
		}
	}
	//更新时间需要手动补充
	p.ChangedAt = time.Now()
	args = append(args, "changed_at")
	return db.Instance().Get().Model(p).Select(query, args...).Updates(p).Error
}

func (service *ProductService) TryFixRootCat(p *models.Product, save bool) bool {
	service.FixName(p)
	if _, err := p.IsValid(); err != configs.ErrProductCate {
		return false
	} else if len(p.Cate) == 4 {
		cate, _ := strconv.Atoi(strings.TrimSpace(p.Cate[1]))
		if cate <= 0 {
			return false
		}
		if pCate, err := cat.NewCatService().Find(uint64(cate)); err == nil {
			p.Cate[0] = strconv.Itoa(int(pCate.ID))
			if save {
				db.Instance().Get().Model(&p).UpdateColumns(models.Product{
					Cate: p.Cate,
					Base: dbtypes.Base{
						UpdatedAt: dbtypes.SHNow(),
					},
				})
			}
			return true
		}
		return false
	}
	return false
}

func (service *ProductService) FixName(p *models.Product) {
	p.Name = strings.ReplaceAll(p.Name, " ", "")
	var words []string
	for _, w := range p.Word {
		words = append(words, strings.ReplaceAll(w, " ", ""))
	}
	p.Word = words
}

func (service *ProductService) UpdateStatusByOpt(productId int, opt int) (*models.Product, *configs.ApiError) {
	if opt < 1 || opt > 4 {
		return nil, &configs.ApiError{Code: configs.ErrInputParamError.Error(), Msg: "opt should in 1,2,3,4"}
	}
	var product models.Product
	if err := db.Instance().Get().Find(&product, productId).Error; err != nil {
		return nil, &configs.ApiError{Code: configs.ErrOPDataBaseError.Error(), Msg: err.Error()}
	}

	switch opt {
	case 1: //提交审核
		if product.Status != dbtypes.ProductStatusDraft {
			return nil, &configs.ApiError{Code: configs.ErrOptProductFailed.Error(), Msg: "当前状态无法提交审核"}
		}
		//service.TryFixRootCat(&product, false)
		if _, err := product.IsValid(); err != nil {
			return nil, &configs.ApiError{Code: configs.ErrAddProductFailed.Error(), Msg: err.Error()}
		}
		lastAuditRes2 := product.AuditRes2
		// 之所以合并， 是因为 编辑页面分了 4 步。如果不合并，就只会保留最后一步的。
		for k, v := range product.LastAuditRes2 {
			if _, ok := lastAuditRes2[k]; !ok {
				lastAuditRes2[k] = v
			}
		}
		if err := db.Instance().Get().Model(&product).UpdateColumns(models.Product{
			Status:        dbtypes.ProductStatusWattingaudit,
			Cate:          product.Cate,
			LastAuditRes2: lastAuditRes2,
			AuditRes2:     nil,
			Base: dbtypes.Base{
				UpdatedAt: dbtypes.SHNow(),
			},
		}).Error; err != nil {
			return nil, &configs.ApiError{Code: configs.ErrAddProductFailed.Error(), Msg: err.Error()}
		}
	case 2: //开始推广
		if product.Status != dbtypes.ProductStatusAuditpass {
			return nil, &configs.ApiError{Code: configs.ErrOptProductFailed.Error(), Msg: "当前状态无法开始推广"}
		}
		product.Status = dbtypes.ProductStatusPromote
		if err := db.Instance().Get().Model(&product).UpdateColumns(models.Product{
			Status: product.Status,
			Cate:   product.Cate,
			Base: dbtypes.Base{
				UpdatedAt: dbtypes.SHNow(),
			},
		}).Error; err != nil {
			return nil, &configs.ApiError{Code: configs.ErrAddProductFailed.Error(), Msg: err.Error()}
		}
	case 3: //取消推广
		if product.Status != dbtypes.ProductStatusPromote {
			return nil, &configs.ApiError{Code: configs.ErrOptProductFailed.Error(), Msg: "当前不在推广中，无需取消"}
		}
		if err := db.Instance().Get().Model(&product).UpdateColumns(models.Product{
			Status: dbtypes.ProductStatusAuditpass,
			Base: dbtypes.Base{
				UpdatedAt: dbtypes.SHNow(),
			},
		}).Error; err != nil {
			return nil, &configs.ApiError{Code: configs.ErrAddProductFailed.Error(), Msg: err.Error()}
		}
	case 4: //删除
		if product.Status == dbtypes.ProductStatusPromote {
			return nil, &configs.ApiError{Code: configs.ErrOptProductFailed.Error(), Msg: "当前产品在推广中，不能取消"}
		}
		if err := db.Instance().Get().Delete(&product).Error; err != nil {
			return nil, &configs.ApiError{Code: configs.ErrAddProductFailed.Error(), Msg: err.Error()}
		}
	}
	return &product, nil
}

func (service *ProductService) UpdateStatusToPassed(productId int, fileds map[string]interface{}) (*models.Product, error) {
	var output models.Product
	if err := db.Instance().Get().Find(&output, productId).Error; err != nil {
		return nil, err
	}
	if err := dbtypes.UpdateModelFromMap(&output, fileds); err != nil {
		return nil, err
	}

	output.Status = dbtypes.ProductStatusAuditpass
	output.AuditRes = nil
	output.AuditRes2 = nil
	output.LastAuditRes2 = nil
	output.ModifyFields = nil
	//service.TryFixRootCat(&output, false)
	output.Active = 1
	if err := db.Instance().Get().Save(&output).Error; err != nil {
		return nil, err
	} else {
		return &output, nil
	}
}

func (service *ProductService) UpdateStatusToNotPass(p *models.Product, reason string, fileds map[string]interface{}) {
	//service.TryFixRootCat(p, false)
	p.Status = dbtypes.ProductStatusAuditnotpass
	p.AuditRes = &reason
	log.Info("product not set cate", zap.Uint64("productId:", p.ID))
	p.AuditRes2 = fileds
	db.Instance().Get().Model(&p).UpdateColumns(models.Product{
		Status:    p.Status,
		AuditRes:  p.AuditRes,
		AuditRes2: p.AuditRes2,
		Base: dbtypes.Base{
			UpdatedAt: dbtypes.SHNow(),
		},
	})
}

func (service *ProductService) GetProductsOfCompanyId(companyId uint64, sortby, order string, limit, offset int) (int64, []models.Product, error) {
	var products []models.Product
	var query = db.Instance().Get().Model(&models.Product{})
	var cnt int64
	//query = query.Select("id", "name", "brand", "audit_res2", "status", "pic", "word")
	query = query.Where("company_id = ? and active=1", companyId)
	query.Count(&cnt)
	query = query.Order(fmt.Sprintf("%s %s", sortby, order))
	query = query.Limit(limit).Offset(offset)
	if err := query.Find(&products).Error; err != nil {
		return 0, nil, err
	} else {
		return cnt, products, nil
	}
}

func (service *ProductService) CountByStatusOfCompany(companyId uint64) (map[int]int, error) {
	var data = map[int]int{}
	// 查询每个商户今日已经发布的条数
	var productGroupByStatus []schemas.ProductGroupByStatus
	if err := db.Instance().Get().Raw("select status, count(*) as count from product where company_id =  ?  and active=1 group by status", companyId).Scan(&productGroupByStatus).Error; err != nil {
		return nil, err
	} else {
		total := 0
		for _, statusCount := range productGroupByStatus {
			total += statusCount.Count
			data[statusCount.Status] = statusCount.Count
		}
		data[0] = total
	}
	fullStatus := []int{
		dbtypes.ProductStatusDraft,
		dbtypes.ProductStatusWattingaudit,
		dbtypes.ProductStatusAuditpass,
		dbtypes.ProductStatusPromote,
		dbtypes.ProductStatusAuditnotpass,
		dbtypes.ProductStatusTitleout,
		dbtypes.ProductStatusContentOut,
		dbtypes.ProductStatusSubjectOut,
	}

	for _, status := range fullStatus {
		if _, ok := data[status]; !ok {
			data[status] = 0
		}
	}
	return data, nil
}

func (svc *ProductService) Search(keyword string, companyIds []uint64, status int,
	sortby, order string, limit, offset int) ([]*models.Product, error) {
	var products []*models.Product
	// Get all matched records
	var dbHelper = db.Instance().Get()
	//TODO to pq, user => "user"
	dbHelper = dbHelper.Joins("left join company on company.id = `product`.company_id")
	dbHelper = dbHelper.Joins("left join user on product.company_id = `user`.company_id")
	if len(companyIds) > 0 {
		dbHelper = dbHelper.Where("product.company_id in (?)", companyIds)
	}

	if dbtypes.IsProductStatusValid(status) {
		dbHelper = dbHelper.Where("product.status = ?", status)
	}

	dbHelper = dbHelper.Where("product.active=1")
	if keyword != "" {
		if service.IsPhone(keyword) {
			//dbHelper = dbHelper.Where(`JSON_SEARCH(company.phone, "all", ?)`, keyword)
			dbHelper = dbHelper.Where(`user.phone = ?`, keyword)
		} else {
			dbHelper = dbHelper.Where("product.name like ? or company.name like ?", "%"+service.EscapeLike(keyword)+"%", "%"+service.EscapeLike(keyword)+"%")
		}
	}

	dbHelper = dbHelper.Order(fmt.Sprintf("%s %s", sortby, order))
	dbHelper = dbHelper.Limit(limit).Offset(offset)

	if err := dbHelper.Find(&products).Error; err != nil {
		return nil, err
	}
	if len(products) > 0 {
		svc.patchProductsWithCompany(products)
		svc.patchProductsWithUser(products)
	}
	return products, nil
}

func (service *ProductService) patchProductsWithCompany(products []*models.Product) []*models.Product {
	if len(products) > 0 {
		companyMap := map[uint64]models.Company{}
		companies := []models.Company{}
		ids := []uint64{}
		for _, p := range products {
			companyMap[p.CompanyID] = models.Company{}
			ids = append(ids, p.CompanyID)

		}
		dbHelp := db.Instance().Get()
		dbHelp.Where("id in (?)", ids).Find(&companies)
		if len(companies) > 0 {
			for _, c := range companies {
				companyMap[c.ID] = c
			}
			for i, p := range products {
				c := companyMap[p.CompanyID]
				products[i].Company = &c
			}
		}
	}
	return products
}

func (service *ProductService) patchProductsWithUser(products []*models.Product) []*models.Product {
	if len(products) > 0 {
		userMap := map[uint64]models.User{}
		users := []models.User{}
		ids := []uint64{}
		for _, p := range products {
			userMap[p.CompanyID] = models.User{}
			ids = append(ids, p.CompanyID)

		}
		dbHelp := db.Instance().Get()
		dbHelp.Where("company_id in (?)", ids).Find(&users)
		if len(users) > 0 {
			for _, c := range users {
				userMap[c.CompanyID] = c
			}
			for i, p := range products {
				c := userMap[p.CompanyID]
				products[i].User = &c
			}
		}
	}
	return products
}

func (service *ProductService) GetProductsFilterByStatusOfCompany(status []int, companyId uint64) ([]models.Product, error) {
	var products []models.Product
	dbquery := db.Instance().Get().Where("company_id = ? and active=1", companyId)
	dbquery = dbquery.Where("status in (?) ", status)
	if err := dbquery.Find(&products).Error; err != nil {
		return nil, err
	}
	return products, nil
}

func (service *ProductService) GetProductsToPublish(companyId uint64) ([]models.Product, error) {
	var products []models.Product
	dbquery := db.Instance().Get().Joins("left join info on product.id = info.product_id")
	dbquery = dbquery.Where("product.company_id = ? and product.active = 1)", companyId)
	dbquery = dbquery.Where("info.status = ?", dbtypes.InfoStatusWattingpublish)
	dbquery = dbquery.Where("info.will_pub_at is not null and info.will_pub_at < ?", dbtypes.SHNow())
	if err := dbquery.Find(&products).Error; err != nil {
		return nil, err
	}
	return products, nil
}

func (service *ProductService) CountByCompanyId(companyId uint64) int64 {
	var total int64
	db.Instance().Get().Model(&models.Product{}).Where("company_id = ? and active=1", companyId).Count(&total)
	return total
}

func (service *ProductService) Get(productId uint64, withCompany bool) (*models.Product, error) {
	var product models.Product
	if err := db.Instance().Get().Find(&product, productId).Error; err != nil {
		return nil, err
	} else {
		if withCompany {
			tmp := []*models.Product{&product}
			tmp = service.patchProductsWithCompany(tmp)
			return tmp[0], nil
		}
		return &product, nil
	}

}

func (service *ProductService) NameExist(name string, companyId uint64, except ...uint64) bool {
	var total int64
	s := db.Instance().Get().Model(&models.Product{}).Where("company_id = ? and name = ? and active=1", companyId, name)
	if len(except) > 0 {
		s = s.Where("id not in (?)", except)
	}
	s.Count(&total)
	return total > 0
}

func (service *ProductService) CalBadWords(p *models.Product) {
	m := make(map[string]interface{})
	m["name"] = services.TrieSerivce.FindKeywords(p.Name)                            // 名字
	m["alias"] = services.TrieSerivce.FindKeywords(strings.Join(p.Alias, " "))       // 别名
	m["word"] = services.TrieSerivce.FindKeywords(strings.Join(p.Word, " "))         //  关键词
	m["material"] = services.TrieSerivce.FindKeywords(strings.Join(p.Material, " ")) // 素材
	m["faq"] = services.TrieSerivce.FindKeywords(p.Faq)                              // faq
	m["temp_content"] = services.TrieSerivce.FindKeywords(p.TempContent)             // faq
	for k, v := range p.Vars {
		if k != models.VarsKeyImages {
			for _, vv := range v.([]interface{}) {
				m["temp_content"] = append(m["temp_content"].([]string), services.TrieSerivce.FindKeywords(vv.(string))...)
			}
		}
	}
	p.BadWords = m
}
