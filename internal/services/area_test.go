package services

import (
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/test"
	"testing"
)

func TestAreaService_FindAllByLevel(t *testing.T) {
	test.InitTest(t)
	t.Run("province", func(t *testing.T) {
		if c, err := Area.FindProvince(); err != nil {
			t.<PERSON>rf("expected nil, real is:%v", err)
		} else if len(c) != 34 {
			t.<PERSON>("expected 34, real is %d", len(c))
		}
	})
}

func TestAreaService_FindSubsByPid(t *testing.T) {
	test.InitTest(t)
	t.Run("no subs", func(t *testing.T) {
		if c, err := Area.FindSubsByPid(22930); err != nil {
			t.<PERSON>rf("expected nil, real is:%v", err)
		} else if len(c) != 0 {
			t.<PERSON><PERSON>("expected 0, real is %d", len(c))
		}
	})

	t.<PERSON>("has subs", func(t *testing.T) {
		if c, err := Area.FindSubsByPid(1); err != nil {
			t.<PERSON>rf("expected nil, real is:%v", err)
		} else if len(c) != 19 {
			t.Errorf("expected 19, real is %d", len(c))
		}
	})
}

func TestAreaService_GetMapping(t *testing.T) {
	test.InitTest(t)
	t.Run("exist", func(t *testing.T) {
		if d, e := Area.GetMapping(30, models.PlatformBafang); e != nil {
			t.Error(e)
		} else if d.AreaID != 30 {
			t.Errorf("expected 30, real is %d", d.AreaID)
		}
	})

	t.Run("not exist", func(t *testing.T) {
		if _, e := Area.GetMapping(30000, models.PlatformBafang); e == nil {
			t.Error("expected error, but nil")
		}
	})
}

func TestAreaService_RandCityst(t *testing.T) {
	test.InitTest(t)
	t.Log(Area.RandCity(models.PlatformBafang, []models.AreaType{models.AreaTypeCountyOfDirectCity, models.AreaTypeCountyOfCity}))
}

func TestAreaService_GetName(t *testing.T) {
	test.InitTest(t)
	t.Log(Area.GetName(1))
}

func TestAreaService_AllAreas(t *testing.T) {
	test.InitTest(t)
	t.Log(Area.AllAreas())
}

func TestAreaService_GetCitiesForSouhaohuo(t *testing.T) {
	test.InitTest(t)
	t.Log(Area.GetCitiesForSouhaohuo([]string{"1", "50"}))            //2,3,11
	t.Log(Area.GetCitiesForSouhaohuo([]string{"5", "1851", "39406"})) //1984,1985,1991
	t.Log(Area.GetCitiesForSouhaohuo([]string{"5", "0", "0"}))        //5,0,0
}

func TestAreaService_RandLastCityByTip(t *testing.T) {
	test.InitTest(t)
	t.Log(Area.RandLastCityByTip(models.PlatformLiebiao, "香港", 0))
	t.Log(Area.RandLastCityByTip(models.PlatformLiebiao, "", 0))
	t.Log(Area.RandLastCityByTip(models.PlatformLiebiao, "北京市", 0))
	for _, city := range Area.AllNames() {
		t.Log(city)
		t.Log(Area.RandLastCityByTip(models.PlatformLiebiao, city, 0))
	}
}
