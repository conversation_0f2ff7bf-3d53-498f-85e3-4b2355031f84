package services

import (
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/test"
	"testing"
)

func TestStarOfYestoday(t *testing.T) {
	t.Log(StarOfYestoday())
}

func TestEndOfYestoday(t *testing.T) {
	t.Log(EndOfYestoday())
}

func TestDailyHistoryService_CountUser(t *testing.T) {
	test.InitTest(t)
	t.Log(NewDailyHistoryService(db.Instance().Get()).CountUser())
}

func TestDailyHistoryService_CountProduct(t *testing.T) {
	test.InitTest(t)
	t.Log(NewDailyHistoryService(db.Instance().Get()).CountProduct())
}

func TestDailyHistoryService_CountInfo(t *testing.T) {
	test.InitTest(t)
	t.Log(NewDailyHistoryService(db.Instance().Get()).CountInfo())
}
