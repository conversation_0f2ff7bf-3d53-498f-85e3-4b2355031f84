package services

import (
	"errors"
	"fmt"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/gorm"
	"strconv"
	"unicode/utf8"
)

const (
	DefaultName = "默认相册"
)

type AlbumServicer interface {
	// 相册是否存在
	Exist(name string, companyId uint64) (*models.Album, error)
	// 创建相册。如果同名 抛出错误。
	<PERSON><PERSON>(name, isOpen, desc string, companyId uint64) (*models.Album, error)
	// 上传之前得先判断是否超出限制。
	OverUploaded(companyId uint64) (bool, error)
	// 列出相册， 按id升序
	ListAlbum(companyId uint64, tries int) ([]models.Album, error)
	// 更新相册。动态更新
	Update(cid, id uint64, m map[string]interface{}) (models.Album, error)
	// 删除相册
	Delete(cid, id uint64) error

	// 添加图片
	AddImage(album *models.Album, img *models.Image) error

	DefaultAlbum(companyId uint64) (*models.Album, error)

	Find(id uint64) (*models.Album, error)
}

type AlbumService struct {
	db *gorm.DB
}

func (svc *AlbumService) Find(id uint64) (*models.Album, error) {
	var album models.Album
	if err := svc.db.Where("id=?", id).First(&album).Error; err != nil {
		return nil, err
	}
	return &album, nil
}

func (svc *AlbumService) DefaultAlbum(companyId uint64) (*models.Album, error) {
	if a, err := svc.Exist(DefaultName, companyId); err != nil {
		return nil, err
	} else {
		if a != nil {
			return a, nil
		}
		return svc.Create(DefaultName, "1", "", companyId)
	}
}

/**
在多文件上传时，这里会多线程调用。
*/
func (svc *AlbumService) AddImage(album *models.Album, img *models.Image) error {
	var err error
	var stat *models.UserStat
	album.Total += 1
	album.Sizes += img.Size
	album.DisplayTotal += 1
	album.DisplaySizes += 1
	if album.Cover == "" {
		album.Cover = img.Url
	}
	if err = svc.db.Exec("update albums set total=total+1, sizes = sizes+?, display_total=display_total+1, display_sizes=display_sizes+?, cover=? where id=?", img.Size, img.Size, album.Cover, album.ID).Error; err != nil {
		return err
	}
	if stat, err = NewUserStatService().GetStatByCompanyId(album.CompanyId); err != nil {
		return err
	} else {
		stat.UploadedCnt += 1
		stat.PhotoCnt += 1
		return svc.db.Exec("update user_stats set uploaded_cnt=uploaded_cnt+1, photo_cnt=photo_cnt+1 where company_id = ?", stat.CompanyID).Error
	}
	return nil
}

func (svc *AlbumService) Exist(name string, companyId uint64) (*models.Album, error) {
	var album models.Album
	if err := svc.db.Model(&models.Album{}).Where("company_id = ? and name= ?", companyId, name).First(&album).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	} else {
		return &album, nil
	}
}

func (svc *AlbumService) Update(cid, id uint64, m map[string]interface{}) (album models.Album, err error) {
	if err = svc.db.Where("id=?", id).First(&album).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("相册不存在")
			return
		}
		return
	}
	if album.CompanyId != cid {
		err = errors.New("非法操作，不是你的相册")
		return
	}
	delete(m, "id")
	if utf8.RuneCountInString(m["name"].(string)) > 8 {
		err = fmt.Errorf("名称太长，最多为8个字")
		return
	}

	if m["name"] != nil && album.Name == "默认相册" {
		if name, ok := m["name"].(string); ok && name == "默认相册" {
			err = errors.New("默认相册不支持重命名")
		}
	}
	if err = dbtypes.UpdateModelFromMap(&album, m); err != nil {
		return
	}
	keys := []interface{}{}
	for k, _ := range m {
		keys = append(keys, k)
	}
	if err = svc.db.Model(&album).Select(keys[0], keys[1:]...).Updates(&album).Error; err != nil {
		return
	}
	return
}

/**
真删除。
*/
func (svc *AlbumService) Delete(cid, id uint64) error {
	var album, defAlbum *models.Album
	var stat *models.UserStat
	var err error
	if album, err = svc.Find(id); err != nil {
		return err
	} else if album.CompanyId != cid {
		return errors.New("非法操作，相册不是你的")
	} else {
		if defAlbum, err = svc.DefaultAlbum(cid); err != nil {
			return err
		} else {
			defAlbum.Sizes += album.Sizes
			defAlbum.Total += album.Total
			defAlbum.DisplaySizes += album.DisplaySizes
			defAlbum.DisplayTotal += album.DisplayTotal
		}
		if stat, err = NewUserStatService().GetStatByCompanyId(cid); err != nil {
			return err
		}
		return svc.db.Transaction(func(tx *gorm.DB) error {
			if err := tx.Delete(album).Error; err != nil {
				return err
			}
			stat.AlbumCnt -= 1
			if err = tx.Save(stat).Error; err != nil {
				return err
			}
			if err = tx.Save(defAlbum).Error; err != nil {
				return err
			}
			if err = tx.Exec("update images set album_id=? where album_id = ?", defAlbum.ID, album.ID).Error; err != nil {
				return err
			}
			return nil
		})
	}
}

func (svc *AlbumService) ListAlbum(companyId uint64, tries int) ([]models.Album, error) {
	var albums []models.Album
	if err := svc.db.Order("id asc").Where("company_id=? ", companyId).Find(&albums).Error; err != nil {
		return nil, err
	}
	if len(albums) == 0 {
		svc.DefaultAlbum(companyId)
		if tries < 3 {
			return svc.ListAlbum(companyId, tries+1)
		}
	}
	return albums, nil
}

func (svc *AlbumService) OverUploaded(companyId uint64) (bool, error) {
	if stat, err := NewUserStatService().GetStatByCompanyId(companyId); err != nil {
		return true, err
	} else if stat.CanUploaded == 0 {
		return true, nil
	}
	return false, nil
}

func NewAlbumService(db *gorm.DB) AlbumServicer {
	return &AlbumService{db: db}
}

func (svc *AlbumService) Create(name, isOpen, desc string, companyId uint64) (*models.Album, error) {
	var album models.Album
	if a, err := svc.Exist(name, companyId); err != nil {
		return nil, err
	} else if a != nil {
		return a, errors.New("相册已存在")
	}
	album.CompanyId = companyId
	album.Name = name
	if open, err := strconv.Atoi(isOpen); err != nil {
		return nil, errors.New("参数非法，is_open 值无效")
	} else {
		album.IsOpen = uint8(open)
	}

	album.Description = desc
	if album.Name == DefaultName {
		album.Main = true
	} else {
		album.Main = false
	}

	if stat, err := NewUserStatService().GetStatByCompanyId(companyId); err == nil {
		stat.AlbumCnt += 1
		if stat.AlbumCnt > models.AlbumMax {
			return nil, fmt.Errorf("相册数超过最大值%d", models.AlbumMax)
		}
		NewUserStatService().Update(stat)
		if err := svc.db.Create(&album).Error; err != nil {
			return nil, err
		}
		return &album, nil
	} else {
		return nil, err
	}
}
