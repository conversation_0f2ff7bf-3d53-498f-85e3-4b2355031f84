package services

import (
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/test"
	"testing"
)

func TestNewAicaigouUser(t *testing.T) {
	test.InitTest(t)
	NewAicaigouUser(db.Instance().Get()).Find(480)
}

func TestAicaigouUser_Insert(t *testing.T) {
	test.InitTest(t)
	item := models.AicaigouUsers{
		CompanyName:       "aa",
		CompanyType:       "bb",
		SocialCreditCode:  "1323",
		LinkPerson:        "u213232",
		LinkPhone:         "3223",
		CompanyAreaIds:    nil,
		BusinessImg:       "afdsfas",
		LinkEmail:         "<EMAIL>",
		CompanyLogo:       "fdasfas",
		CompanyWeb:        "dafsdf",
		ContractBeginDate: "dfads",
		ContractEndDate:   "fdasfdas",
		ContractFile:      "dfasdfas",
		ProductType:       1,
		BrankName:         "xxx",
		BankAreaIds:       nil,
		OpenBranch:        "",
		InterBankNum:      "",
		CardNumber:        "",
		CompanyID:         100,
	}
	NewAicaigouUser(db.Instance().Get()).Insert(&item)
}
