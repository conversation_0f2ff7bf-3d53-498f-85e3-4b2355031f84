package services

import (
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services/service"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/gorm"
	"strconv"
	"time"
)

type DailyPaginator struct {
	Items     []models.DailyHistory `json:"items"`
	Paginator service.Paginator     `json:"paginator"`
}

type DailyHistoryService struct {
	db *gorm.DB
}

func NewDailyHistoryService(db *gorm.DB) *DailyHistoryService {
	return &DailyHistoryService{db: db}
}

func (s *DailyHistoryService) CountProduct() error {
	var data = map[int]int{}
	start := StarOfYestoday()
	end := EndOfYestoday()
	// 查询每个商户今日已经发布的条数
	var productGroupByStatus []schemas.ProductGroupByStatus
	if err := db.Instance().Get().Raw("select status, count(*) as count from product where updated_at between ? and  ? and active=1 group by status", start, end).Scan(&productGroupByStatus).Error; err != nil {
		return err
	} else {
		total := 0
		for _, statusCount := range productGroupByStatus {
			total += statusCount.Count
			data[statusCount.Status] = statusCount.Count
		}
		data[0] = total
	}
	fullStatus := []int{
		dbtypes.ProductStatusDraft,
		dbtypes.ProductStatusWattingaudit,
		dbtypes.ProductStatusAuditpass,
		dbtypes.ProductStatusPromote,
		dbtypes.ProductStatusAuditnotpass,
		dbtypes.ProductStatusTitleout,
		dbtypes.ProductStatusContentOut,
		dbtypes.ProductStatusSubjectOut,
	}

	for _, status := range fullStatus {
		if _, ok := data[status]; !ok {
			data[status] = 0
		}
	}
	var record models.DailyHistory
	record.CreatedAt = dbtypes.SHNow()
	record.UpdatedAt = dbtypes.SHNow()
	record.StatType = models.StatTypeProduct
	record.Data = map[string]interface{}{
		"pendingReview": data[dbtypes.ProductStatusWattingaudit],
		"submitted":     data[dbtypes.ProductStatusWattingaudit] + data[dbtypes.ProductStatusAuditpass] + data[dbtypes.ProductStatusAuditnotpass],
		"passed":        data[dbtypes.ProductStatusAuditpass],
		"rejected":      data[dbtypes.ProductStatusAuditnotpass],
		"promotion":     data[dbtypes.ProductStatusPromote],
	}
	if err := s.db.Save(&record).Error; err != nil {
		return err
	}
	return nil
}

func (s *DailyHistoryService) CountUser() error {
	start := StarOfYestoday()
	end := EndOfYestoday()
	var newUsers int64
	var loginUsers int64
	var editProductUsers int64
	var pubInfoUsers int64
	if err := s.db.Model(models.User{}).Where("created_at between ? and ?", start, end).Count(&newUsers).Error; err != nil {
		return err
	}
	if err := s.db.Model(models.User{}).Where("updated_at between ? and ?", start, end).Count(&loginUsers).Error; err != nil {
		return err
	}
	if err := s.db.Model(models.Product{}).Where("updated_at between ? and ?", start, end).Distinct("company_id").Count(&editProductUsers).Error; err != nil {
		return err
	}
	if err := s.db.Model(models.Info{}).Where("updated_at between ? and ?", start, end).Distinct("company_id").Count(&pubInfoUsers).Error; err != nil {
		return err
	}
	var record models.DailyHistory
	record.CreatedAt = dbtypes.SHNow()
	record.UpdatedAt = dbtypes.SHNow()
	record.StatType = models.StatTypeUser
	record.Data = map[string]interface{}{
		"newUsers":         newUsers,
		"loginUsers":       loginUsers,
		"editProductUsers": editProductUsers,
		"pubInfoUsers":     pubInfoUsers,
	}
	if err := s.db.Save(&record).Error; err != nil {
		return err
	}
	return nil
}

func (s *DailyHistoryService) CountInfo() error {
	start := StarOfYestoday()
	end := EndOfYestoday()
	// 查询每个商户昨日已经发布的条数
	var groups []schemas.InfoGroupByPlatform
	if err := db.Instance().Get().Raw("select platform, count(*) as count from info where status=2 and updated_at between ? and ? group by platform", start, end).Scan(&groups).Error; err != nil {
		return err
	}
	m := make(map[int]schemas.InfoGroupByPlatform)
	var record models.DailyHistory
	record.Data = make(map[string]interface{})
	for _, g := range groups {
		g.Name = models.PlatFormName(models.PlatForm(g.Platform))
		m[g.Platform] = g
		record.Data[strconv.Itoa(g.Platform)] = g
	}
	record.CreatedAt = dbtypes.SHNow()
	record.UpdatedAt = dbtypes.SHNow()
	record.StatType = models.StatTypeInfo
	if err := s.db.Save(&record).Error; err != nil {
		return err
	}
	return nil
}

func (s *DailyHistoryService) GetHistory(statType int, startTime, endTime time.Time, limit, offset int) (*DailyPaginator, error) {
	p := DailyPaginator{}
	var total int64
	err := s.db.Model(models.DailyHistory{}).Where("created_at between ? and ? and  stat_type=?", startTime, endTime, statType).Count(&total).Limit(limit).Offset(offset).Order("created_at desc").Find(&p.Items).Error
	if err != nil {
		return nil, err
	}
	p.Paginator = service.NewPaginatorByOffset(offset, limit, int(total))
	return &p, nil
}

func StarOfYestoday() time.Time {
	t := dbtypes.SHNow()
	t = t.AddDate(0, 0, -1)
	secondOffset := t.Unix() % (60 * 60 * 24)
	t = t.Add(-time.Duration(secondOffset) * time.Second)
	t = t.Add(-time.Duration(8*3600) * time.Second)
	return t
}

func EndOfYestoday() time.Time {
	start := StarOfYestoday()
	end := start.AddDate(0, 0, 1)
	end = end.Add(-time.Second)
	return end
}
