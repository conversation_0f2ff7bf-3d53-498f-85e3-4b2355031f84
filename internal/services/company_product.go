package services

import (
	"fmt"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services/service"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
)

type CompanyProductPaginator struct {
	Items     []models.CompanyProduct `json:"items"`
	Paginator service.Paginator       `json:"paginator"`
}
type CompanyProductServicer interface {
	GetCompanyProducts(companyIds []uint64, status int, keyword, sortby, order string, limit, offset int, withCompany bool) ([]models.CompanyProduct, error)
	GetCompanyProductsWithPaginator(companyIds []uint64, status int, keyword, sortby, order string, limit, offset int, withCompany bool) (*CompanyProductPaginator, error)
	patchCompanyProductsWithCompany(infos []models.CompanyProduct) []models.CompanyProduct
	GetItemsOfProduct(productId uint64, status int) ([]models.CompanyProduct, error)
	CountByCompanyId(companyId uint64) int64
	CountCompanyProducts(companyIds []uint64, status int, keyword string) int64
}

type CompanyProductService struct {
}

func NewCompanyProductService() CompanyProductServicer {
	return &CompanyProductService{}
}

func (svc *CompanyProductService) CountCompanyProducts(companyIds []uint64, status int, keyword string) int64 {
	var query = db.Instance().Get()
	if len(companyIds) > 0 {
		query = query.Where("company_id in (?)", companyIds)
	}

	if dbtypes.IsCompanyProductStatusValid(status) {
		query = query.Where("status = ?", status)
	}
	if keyword != "" {
		query = query.Where("subject like ?", "%"+service.EscapeLike(keyword)+"%")
	}
	var total int64
	query.Model(&models.CompanyProduct{}).Count(&total)
	return total
}

func (svc *CompanyProductService) GetCompanyProducts(companyIds []uint64, status int, keyword, sortby, order string, limit, offset int, withCompany bool) ([]models.CompanyProduct, error) {
	var items []models.CompanyProduct
	// Get all matched records
	var query = db.Instance().Get()
	if len(companyIds) > 0 {
		query = query.Where("company_id in (?)", companyIds)
	}

	if dbtypes.IsCompanyProductStatusValid(status) {
		query = query.Where("status = ?", status)
	}
	if keyword != "" {
		query = query.Where("subject like ?", "%"+service.EscapeLike(keyword)+"%")
	}

	query = query.Order(fmt.Sprintf("%s %s", sortby, order))
	query = query.Limit(limit).Offset(offset)
	if err := query.Find(&items).Error; err != nil {
		return nil, err
	}
	for i, info := range items {
		info := info
		items[i] = info
	}

	if withCompany {
		items = svc.patchCompanyProductsWithCompany(items)
	}
	return items, nil
}

func (svc *CompanyProductService) GetCompanyProductsWithPaginator(companyIds []uint64, status int, keyword, sortby, order string, limit, offset int, withCompany bool) (*CompanyProductPaginator, error) {
	if items, err := svc.GetCompanyProducts(companyIds, status, keyword, sortby, order, limit, offset, withCompany); err != nil {
		return nil, err
	} else {
		return &CompanyProductPaginator{
			Items:     items,
			Paginator: service.NewPaginatorByOffset(offset, limit, int(svc.CountCompanyProducts(companyIds, status, keyword))),
		}, nil
	}
}

func (service *CompanyProductService) patchCompanyProductsWithCompany(items []models.CompanyProduct) []models.CompanyProduct {
	if len(items) > 0 {
		companyMap := map[uint64]models.Company{}
		companies := []models.Company{}
		ids := []uint64{}
		for _, info := range items {
			ids = append(ids, info.CompanyID)

		}
		cdb := db.Instance().Get()
		cdb.Where("id in (?)", ids).Find(&companies)
		if len(companies) > 0 {
			for _, c := range companies {
				companyMap[c.ID] = c
			}
			for i, info := range items {
				company := companyMap[info.CompanyID]
				items[i].Company = &company
			}
		}
	}
	return items
}

func (service *CompanyProductService) GetItemsOfProduct(productId uint64, status int) ([]models.CompanyProduct, error) {
	var items []models.CompanyProduct
	dbHelper := db.Instance().Get()
	dbHelper = dbHelper.Where("product_id =  ?", productId)
	dbHelper = dbHelper.Where("will_pub_at < ?", dbtypes.SHNow())
	dbHelper = dbHelper.Where("status = ?", status)
	if err := dbHelper.Find(&items).Error; err != nil {
		return nil, err
	}
	return service.patchCompanyProductsWithCompany(items), nil
}

func (service *CompanyProductService) CountByCompanyId(companyId uint64) int64 {
	var total int64
	db.Instance().Get().Model(&models.CompanyProduct{}).Where("company_id = ?", companyId).Count(&total)
	return total
}
