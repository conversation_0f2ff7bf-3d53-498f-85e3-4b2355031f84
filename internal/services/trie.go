package services

import (
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/stringx"
)

var TrieSerivce TrieService

type TrieService struct {
	trie   stringx.Trie
	loaded bool
}

func (svc *TrieService) loadBadWords() (ret []string) {
	var items []models.BadWords
	if err := db.Instance().Get().Find(&items).Error; err == nil {
		for _, item := range items {
			ret = append(ret, item.Word)
		}
	}
	return ret
}

func (svc *TrieService) FindKeywords(input string) []string {
	if !svc.loaded {
		words := svc.loadBadWords()
		svc.trie = stringx.NewTrie(words)
	}
	return svc.trie.FindKeywords(input)
}
