package services

import (
	"gitlab.com/all_publish/api/test"
	"strconv"
	"testing"
)

func TestRank_AddKeyword(t *testing.T) {
	test.InitTest(t)
	for i := 0; i < 100; i++ {
		t.Log(Rank.AddKeyword("test"+strconv.Itoa(i), EgSourceTypeBaidu))
	}
}

func TestRank_Getkeywords(t *testing.T) {
	test.InitTest(t)
	t.Log(Rank.Getkeywords(10))
}

func TestRank_Status(t *testing.T) {
	test.InitTest(t)
	//t.Log(Rank.Status("haha", "http://www.baidu.com", "BD", "108"))
	t.Log(Rank.Status("haha", "http://guanggao.huangye88.com/xinxi/d627kjm1q05754.html", "BD", "108"))
}

func TestRank_Insert(t *testing.T) {
	test.InitTest(t)
	t.Log(Rank.Insert("haha", "http://guanggao.huangye88.com/xinxi/d627kjm1q05754.html", "BD", "108", "http://www.baidu.com"))
}

func TestRank_GetRanks(t *testing.T) {
	test.InitTest(t)
	t.Log(Rank.GetRanks(12134, 1, 10, 0, -1))
}

func TestRank_GroupRanksByEg(t *testing.T) {
	test.InitTest(t)
	t.Log(Rank.GroupRanksByEg(12134))
}
