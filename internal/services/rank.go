package services

import (
	"errors"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/convert"
	"gitlab.com/all_publish/api/pkg/db"
	"strings"
	"time"
)

var Rank rank

const keywordQueue = "keywordqueue"

type EgSourceType string

const (
	EgSourceTypeBaidu    = EgSourceType("baidu") //source 定义。客户端得匹配这个来决定用哪个下载, 定义源在bin/php/fromse.php
	EgSourceTypeSogou    = EgSourceType("sogou") //
	EgSourceTypeHaosou   = EgSourceType("haosou")
	EgSourceTypeBaiduM   = EgSourceType("baidu_m")
	EgSourceTypeSogouM   = EgSourceType("sogou_m")
	EgSourceTypeShenma   = EgSourceType("shenma_m")
	EgSourceTypeToutiaoM = EgSourceType("toutiao_m")
	EgSourceTypeToutiao  = EgSourceType("toutiao")
)

type DomainType string

const (
	DomainTypeBaidu    = DomainType("baidu")
	DomainTypeBaiduM   = DomainType("baidu_m")
	DomainTypeSogou    = DomainType("sogou")
	DomainTypeSogouM   = DomainType("sogou_m")
	DomainTypeHaosou   = DomainType("haosou")
	DomainTypeShenma   = DomainType("shenma_m")
	DomainTypeToutiaoM = DomainType("toutiao_m")
	DomainTypeToutiao  = DomainType("toutiao")
	DomainTypeHuoyan   = DomainType("jjhuoyan")
)

type EgIndexType string

const (
	EgIndexBaidu    = EgIndexType("pc_baidu") //和数据库字段对应起来，messages_seek表字段
	EgIndexBaiduM   = EgIndexType("m_baidu")
	EgIndexHaosou   = EgIndexType("pc_haosou")
	EgIndexHaosouM  = EgIndexType("m_haosou")
	EgIndexSogou    = EgIndexType("pc_sogou")
	EgIndexSogouM   = EgIndexType("m_sogou")
	EgIndexShenma   = EgIndexType("m_shenma")
	EgIndexToutiaoM = EgIndexType("m_toutiao")
	EgIndexToutiao  = EgIndexType("pc_toutiao")
)

// 服务端定义的mapping 关系,排名表字段, 存到hystats_return_rankingword_detail.engine
type EgRankType string

const (
	EgRankBaidu    = EgRankType("BD")
	EgRankbaiduM   = EgRankType("BDM")
	EgRankShenma   = EgRankType("SM")
	EgRankHaosou   = EgRankType("360")
	EgRankSogou    = EgRankType("SG")
	EgRankSogouM   = EgRankType("SGM")
	EgRankToutiaoM = EgRankType("TTM")
	EgRankToutiao  = EgRankType("TT")
)

func GetRankTypeBySource(source EgSourceType) EgRankType {
	m := map[EgSourceType]EgRankType{
		EgSourceTypeBaidu:    EgRankBaidu,
		EgSourceTypeSogou:    EgRankSogou,
		EgSourceTypeHaosou:   EgRankHaosou,
		EgSourceTypeBaiduM:   EgRankbaiduM,
		EgSourceTypeSogouM:   EgRankSogouM,
		EgSourceTypeShenma:   EgRankShenma,
		EgSourceTypeToutiaoM: EgRankToutiaoM,
		EgSourceTypeToutiao:  EgRankToutiao,
	}
	return m[source]
}

func GetIndexTypeBySource(source EgSourceType) EgIndexType {
	m := map[EgSourceType]EgIndexType{
		EgSourceTypeBaidu:    EgIndexBaidu,
		EgSourceTypeSogou:    EgIndexSogou,
		EgSourceTypeHaosou:   EgIndexHaosou,
		EgSourceTypeBaiduM:   EgIndexBaiduM,
		EgSourceTypeSogouM:   EgIndexSogouM,
		EgSourceTypeShenma:   EgIndexShenma,
		EgSourceTypeToutiaoM: EgIndexToutiaoM,
		EgSourceTypeToutiao:  EgIndexToutiao,
	}
	return m[source]
}

func GetDirByEg(eg EgRankType) string {
	m := map[EgRankType]EgIndexType{
		EgRankBaidu:    EgIndexBaidu,
		EgRankbaiduM:   EgIndexBaiduM,
		EgRankShenma:   EgIndexShenma,
		EgRankHaosou:   EgIndexHaosou,
		EgRankSogou:    EgIndexSogou,
		EgRankSogouM:   EgIndexSogouM,
		EgRankToutiaoM: EgIndexToutiaoM,
		EgRankToutiao:  EgIndexToutiao,
	}
	return string(m[eg])
}

type rank struct {
}

func NewRankService() *rank {
	return &Rank
}

func (s *rank) AddKeyword(keyword string, source EgSourceType) error {
	rds := db.GetRedisConn()
	defer rds.Close()
	if _, err := rds.Do("zadd", keywordQueue, time.Now().Unix(), keyword+" "+string(source)); err != nil {
		return err
	}
	return nil
}

func (s *rank) Getkeywords(limit int) []string {
	rds := db.GetRedisConn()
	defer rds.Close()
	if limit > 100 {
		limit = 100
	}
	items := []string{}
	if data, err := rds.Do("zRange", keywordQueue, 0, limit-1); err == nil {
		for _, item := range data.([]interface{}) {
			word := string(item.([]uint8))
			if _, err := rds.Do("zRem", keywordQueue, word); err == nil {
				items = append(items, word)
			}
		}
	}
	return items
}

func (s *rank) Status(keyword, url string, eg EgRankType, rank string) (*models.Infos, error) {
	if info, err := NewInfosService().FindInfoByUrl(url); err != nil {
		return nil, err
	} else {
		item := models.Rank{}
		parts := strings.Split(url, "#")
		if len(parts) == 2 {
			url = parts[0]
		}
		if err := db.Instance().Get().Where("company_id=? and url=? and keyword=? and eg = ? and platform=? and created_at>?",
			info.CompanyID, url, keyword, eg, info.Platform, time.Now().Format("2006-01-02")).Find(&item).Error; err == nil && item.ID > 0 {
			return nil, errors.New("already ranked")
		} else if err != nil {
			return nil, err
		} else {
			return info, nil
		}
	}
}

func (s *rank) Insert(keyword, url string, eg EgRankType, rank, snap string) error {
	if info, err := s.Status(keyword, url, eg, rank); err != nil {
		return err
	} else {
		parts := strings.Split(url, "#")
		if len(parts) == 2 {
			url = parts[0]
		}
		item := models.Rank{}
		item.Keyword = keyword
		item.CompanyID = info.CompanyID
		item.Rank = convert.Str(rank).MustInt()
		item.Snap = snap
		item.Url = url
		item.Platform = info.Platform
		item.Eg = string(eg)
		return db.Instance().Get().Save(&item).Error
	}
}

func (s *rank) GetRanks(cid, page int, limit int, eg int, pf int) (items []models.Rank, total int64, err error) {
	query := db.Instance().Get().Model(models.Rank{}).Where("company_id=? ", cid)
	if eg > 0 {
		switch eg {
		case 1:
			query = query.Where("eg in ?", []EgRankType{EgRankBaidu, EgRankbaiduM})
		case 2:
			query = query.Where("eg in ?", []EgRankType{EgRankHaosou})
		case 3:
			query = query.Where("eg in ?", []EgRankType{EgRankSogou, EgRankSogouM})
		case 4:
			query = query.Where("eg in ?", []EgRankType{EgRankShenma})
		case 5:
			query = query.Where("eg in ?", []EgRankType{EgRankToutiao, EgRankToutiaoM})
		}
	}
	if pf != -1 {
		query = query.Where("platform=?", pf)
	}
	if err = query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	if err = query.Limit(limit).Offset((page - 1) * limit).Find(&items).Error; err != nil {
		return nil, 0, err
	}

	return
}

type EgCount struct {
	C  int    `json:"c"`
	Eg string `json:"eg"`
}

func (s *rank) GroupRanksByEg(cid uint64) (items []EgCount, err error) {
	var egCount []EgCount
	query := db.Instance().Get().Model(models.Rank{}).Where("company_id=? ", cid).Group("eg").Pluck("count(*) as c, eg", &egCount)

	if err = query.Find(&items).Error; err != nil {
		return nil, err
	}
	return egCount, nil
}
