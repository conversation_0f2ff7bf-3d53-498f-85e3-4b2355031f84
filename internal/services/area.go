package services

import (
	"encoding/json"
	"errors"
	_ "errors"
	"fmt"
	"strconv"
	"strings"
	"sync"

	"github.com/gomodule/redigo/redis"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/pkg/convert"
	"gitlab.com/all_publish/api/pkg/db"
)

var Area = &areaService{names: make(map[int]string)}

// 搜好货才多了个虚拟层级。。直辖市当省用。
var VirtualCity = map[int]int{
	2:    3,    // 北京市
	20:   21,   // 天津市
	861:  862,  // 上海市
	2465: 2466, // 重庆市,有2 个，2466 市辖县，4796 郊县
}

type areaService struct {
	names map[int]string
	sync.Mutex
}

func (c *areaService) FindSubsByPid(pid uint64) ([]schemas.Area, error) {
	var items []schemas.Area
	err := db.Instance().Get().Select("id,area_name, area_en, parent_id").Where("parent_id=?", pid).Find(&items).Error
	return items, err
}

func (c *areaService) AllAreas() string {
	var items []models.Area
	var result = make(map[int]map[uint]map[uint]string)
	conn := db.GetRedisConn()
	defer conn.Close()
	const key = "js-allareas"
	if data, err := redis.String(conn.Do("get", key)); err == nil && data != "" {
		return data
	}
	err := db.Instance().Get().Select("id,area_name, type,area_en, parent_id").Order("parent_id ASC, order_id ASC").Find(&items).Error
	if err != nil {
		return ""
	}
	for _, item := range items {
		name := strings.ToLower(item.AreaName)
		levels := []int{1, 2, 1, 2, 3}
		level := 0
		if item.Type == 4 {
			level = 3
			name = fmt.Sprintf("%s:%d", name, item.ParentID)
		} else {
			level = levels[item.Type]
		}
		if _, ok := result[level]; !ok {
			result[level] = make(map[uint]map[uint]string)
		}
		pre := result[level]
		if v, ok := pre[item.ParentID]; ok {
			v[item.ID] = name
			pre[item.ParentID] = v
		} else {
			v := make(map[uint]string)
			v[item.ID] = name
			pre[item.ParentID] = v
		}
		result[level] = pre
	}
	v, _ := json.Marshal(result)
	js := "var allareas = " + string(v)
	if _, err := conn.Do("setex", key, 86400, js); err != nil {
		return ""
	}
	return js
}

func (c *areaService) GetTypeOfCity(id int) models.AreaType {
	if dta, err := c.Find(id); err != nil {
		return models.AreaTypeNone
	} else {
		return models.AreaType(dta.Type)
	}
}

func (c *areaService) IsDirectCity(id int) bool {
	ids := []int{1, 2, 3, 4, 32, 33, 34}
	for _, v := range ids {
		if id == v {
			return true
		}
	}
	return false
}

func (c *areaService) Find(id int) (item models.Area, err error) {
	err = db.Instance().Get().Model(item).Find(&item, id).Error
	if item.ID == 0 {
		err = errors.New("找不到分类")
	}
	return
}

func (c *areaService) FindProvince() ([]schemas.Area, error) {
	return c.FindSubsByPid(0)
}

func (c *areaService) GetMapping(id uint64, form models.PlatForm) (models.AreaMappings, error) {
	var mapping models.AreaMappings
	err := db.Instance().Get().Where("area_id=? and plat_form= ?", id, form).First(&mapping).Error
	return mapping, err
}

func (c *areaService) FindCityAndNext(areaIds []string, platform models.PlatForm) (fareamapping models.AreaMappings, areamapping models.AreaMappings, err error) {
	areaId := 0
	if len(areaIds) == 0 {
		areaIds = append(areaIds, "0") // 兼容下空数据
	}
	fareaId, _ := strconv.Atoi(areaIds[0]) // 第一个城市 可能是直辖市/省
	if fareaId == 0 {                      // 面向全国
		fareamapping, err = c.RandCity(platform, []models.AreaType{models.AreaTypeDirectyCity, models.AreaTypeCity})
		areamapping = fareamapping
		return
	}
	if firstcity, err := c.Find(fareaId); err != nil {
		return models.AreaMappings{}, models.AreaMappings{}, err
	} else if firstcity.Type == models.AreaTypeDirectyCity { // 直辖市
		fareamapping, err = c.GetMapping(uint64(firstcity.ID), platform)
		if len(areaIds) > 1 {
			areaId, _ = strconv.Atoi(areaIds[1])
		}
	} else { // 省
		if len(areaIds) == 1 || areaIds[1] == "0" { // 只选择了省, 随机选个市
			fareamapping, err = c.RandCityOfParent(platform, int(firstcity.ID))
		} else if len(areaIds) > 1 {
			fareamapping, err = c.GetMapping(convert.Str(areaIds[1]).MustUInt64(), platform)
			if len(areaIds) > 2 && areaIds[2] != "0" {
				areaId = convert.Str(areaIds[2]).MustInt()
			}
		}
	}

	if areaId > 0 {
		areamapping, err = c.GetMapping(uint64(areaId), platform)
	} else {
		areamapping = fareamapping
	}
	return
}
func (c *areaService) GetCitiesForSouhaohuo(cities []string) ([]int, error) {
	ids := make([]int, 0)
	err := db.Instance().Get().Model(&models.AreaMappings{}).Where(" plat_form= ? and area_id in (?)", models.PlatformSouHaoHuo, cities).Order("target_area_id asc").Pluck("cast(target_area_id as SIGNED) as target_area_id", &ids).Error
	if err != nil {
		return nil, err
	}
	if len(ids) == 0 {
		return nil, errors.New("找不到城市")
	}
	if v, ok := VirtualCity[ids[0]]; ok {
		// 如果是重庆，还需要特殊处理下
		if v == 2466 {
			// 进一步特殊处理
			// 2494 2493 2492 2501 2496 2499 2498 2502 2505 2500 2504 2503
			// 郊县列表
			subIds := []int{2494, 2493, 2492, 2501, 2496, 2499, 2498, 2502, 2505, 2500, 2504, 2503}
			// 郊县的父级城市id 是 4796

			for _, subId := range subIds {
				if ids[1] == subId {
					v = 4796
					break
				}
			}
			ids = append([]int{ids[0]}, append([]int{v}, ids[1:]...)...)
		} else {
			ids = append([]int{ids[0]}, append([]int{v}, ids[1:]...)...)
		}
	}
	for len(ids) < 3 {
		ids = append(ids, 0)
	}
	return ids, err
}

func (c *areaService) RandCity(form models.PlatForm, types []models.AreaType) (models.AreaMappings, error) {
	var mapping models.AreaMappings
	err := db.Instance().Get().Joins("inner join areas on areas.id = area_mappings.area_id").Where(" plat_form= ? and type in ?", form, types).Order("rand()").First(&mapping).Error
	return mapping, err
}

func (c *areaService) SearchCity(name string) (*models.Area, error) {
	var mappings []models.Area
	err := db.Instance().Get().Joins("inner join area_mappings on areas.id = area_mappings.area_id").Where(" area_name =  ?", name).Find(&mappings).Error
	if err != nil {
		return nil, err
	}
	if len(mappings) == 0 {
		return nil, nil
	}
	return &mappings[0], err
}

func (c *areaService) RandCityOfParent(form models.PlatForm, pid int) (models.AreaMappings, error) {
	var mapping models.AreaMappings
	err := db.Instance().Get().Joins("inner join areas on areas.id = area_mappings.area_id").Where(" plat_form= ? and areas.parent_id = ?", form, pid).Order("rand()").First(&mapping).Error
	return mapping, err
}

func (c *areaService) GetName(id int) string {
	c.Lock()
	defer c.Unlock()
	if len(c.names) == 0 {
		var items []models.Area
		if err := db.Instance().Get().Find(&items).Error; err != nil {
			return ""
		}
		if len(items) == 0 {
			return ""
		}
		for _, item := range items {
			c.names[int(item.ID)] = item.AreaName
		}
	}
	return c.names[id]
}

func (c *areaService) FindNames(ids []string) (ret []string) {
	for _, id := range ids {
		ret = append(ret, c.GetName(convert.Str(id).MustInt()))
	}
	return
}

// 先判断 names, 如果有直接取，否则查数据库, 同时更新names
func (c *areaService) AllNames() (ret []string) {
	c.Lock()
	defer c.Unlock()
	if len(c.names) > 0 {
		for _, name := range c.names {
			ret = append(ret, name)
		}
		return
	}
	var items []models.Area
	if err := db.Instance().Get().Find(&items).Error; err != nil {
		return
	}
	if len(items) == 0 {
		return
	}
	for _, item := range items {
		ret = append(ret, item.AreaName)
		c.names[int(item.ID)] = item.AreaName
	}
	return
}

// http://newjira.huangye88.net/browse/HY-7919
func (c *areaService) RandLastCityByTip(form models.PlatForm, name string, provinceId int) (models.AreaMappings, error) {
	if name == "" {
		return c.RandCity(form, []models.AreaType{models.AreaTypeCity, models.AreaTypeDirectyCity})
	} else {
		city, err := c.SearchCity(name)
		if err != nil {
			return models.AreaMappings{}, err
		} else if city == nil {
			if provinceId == 0 {
				return c.RandCity(form, []models.AreaType{models.AreaTypeCity, models.AreaTypeDirectyCity})
			} else {
				if subcity, err := c.RandCityOfParent(form, provinceId); err != nil {
					return c.RandCity(form, []models.AreaType{models.AreaTypeCity, models.AreaTypeDirectyCity})
				} else {
					cityId := uint(subcity.AreaID)
					return c.GetMapping(uint64(cityId), form)
				}
			}
		} else {
			cityId := city.ID
			if city.Type == models.AreaTypeProvince {
				if subcity, err := c.RandCityOfParent(form, int(city.ID)); err != nil {
					return c.RandCity(form, []models.AreaType{models.AreaTypeCity, models.AreaTypeDirectyCity})
				} else {
					cityId = uint(subcity.AreaID)
				}
			} else if city.Type == models.AreaTypeCountyOfDirectCity || city.Type == models.AreaTypeCountyOfCity {
				cityId = uint(city.ParentID)
			}
			return c.GetMapping(uint64(cityId), form)
		}
	}
}
