package services

import (
	"fmt"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services/service"
	"gitlab.com/all_publish/api/pkg/db"
)

type UserService struct {
}

func NewUserService() *UserService {
	return &UserService{}
}

func (service *UserService) GetUserById(id uint64) (*models.User, error) {
	var user models.User
	if err := db.Instance().Get().Find(&user, id).Error; err != nil {
		return nil, err
	}
	if err := db.Instance().Get().Find(&user.Company, user.CompanyID).Error; err != nil {
		return nil, err
	}
	posts, _ := NewUserPlatformService().GetPlatForms(user.CompanyID)
	for _, p := range posts {
		user.PostNames = append(user.PostNames, models.PostName{Id: int(p), Name: models.PlatFormName(p)})
	}
	return &user, nil
}

func (service *UserService) GetUserByOem(oemId int) (*models.User, error) {
	var user models.User
	if err := db.Instance().Get().Where("oem_id = ?", oemId).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (service *UserService) GetUserByPhone(phone int) (*models.User, error) {
	var user models.User
	if err := db.Instance().Get().Where("phone = ?", phone).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (service *UserService) GetUserByCompany(company models.Company) (*models.User, error) {
	if user, err := service.GetUserByCompanyId(company.ID); err != nil {
		return nil, err
	} else {
		user.Company = company
		return user, nil
	}
}

func (service *UserService) GetUserByCompanyId(companyId uint64) (*models.User, error) {
	var user models.User
	if err := db.Instance().Get().Where("company_id = ? ", companyId).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (svc *UserService) Search(keyword string, advisorId uint64,
	sortby, order string, limit, offset int) ([]models.User, error) {
	var users []models.User
	// Get all matched records
	var dbHelper = db.Instance().Get()
	//TODO to pq, user => "user"
	dbHelper = dbHelper.Joins("left join company on company.id = `user`.company_id")
	if keyword != "" {
		if service.IsPhone(keyword) {
			dbHelper = dbHelper.Where("`user`.phone = ?", keyword)
		} else if service.IsUserName(keyword) {
			dbHelper = dbHelper.Where("`user`.username like ?", "%"+keyword+"%")
		} else {
			dbHelper = dbHelper.Where("name like ?", "%"+keyword+"%")
		}
	}
	if advisorId > 0 {
		dbHelper = dbHelper.Where("advisor_id = ?", advisorId)
	}
	dbHelper = dbHelper.Order(fmt.Sprintf("%s %s", sortby, order))
	dbHelper = dbHelper.Limit(limit).Offset(offset)

	if err := dbHelper.Find(&users).Error; err != nil {
		return nil, err
	}
	if len(users) > 0 {
		svc.patchUsersWithCompany(users)
	}
	return users, nil
}

func (service *UserService) patchUsersWithCompany(users []models.User) []models.User {
	if len(users) > 0 {
		companyMap := map[uint64]models.Company{}
		companies := []models.Company{}
		ids := []uint64{}
		for _, u := range users {
			ids = append(ids, u.CompanyID)

		}
		cdb := db.Instance().Get()
		cdb.Where("id in (?)", ids).Find(&companies)
		if len(companies) > 0 {
			for _, c := range companies {
				companyMap[c.ID] = c
			}
			for i, u := range users {
				company := companyMap[u.CompanyID]
				if company.AdvisorID > 0 {
					systemUser, err := NewSystemUserService().LoadWithCache(company.AdvisorID)
					if err == nil {
						company.SystemUser = *systemUser
					}

				}
				users[i].Company = company
			}
		}
	}
	return users
}
