package services

import (
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
)

type UserPlatformServicer interface {
	GetFuncListByCompanyId(cid uint64) ([]models.UserPlatform, error)
	GetFuncListBy(cid uint64, platform models.PlatForm) ([]models.UserPlatform, error)
	UpdateFunction(u models.UserPlatform)
	GetAutoPostedPlatForms(cid uint64) ([]models.PlatForm, error)
	GetPlatForms(cid uint64) ([]models.PlatForm, error)
}

type UserPlatformService struct {
}

func NewUserPlatformService() UserPlatformServicer {
	return &UserPlatformService{}
}

func (service *UserPlatformService) GetFuncListByCompanyId(cid uint64) ([]models.UserPlatform, error) {
	var userplatforms []models.UserPlatform
	if err := db.Instance().Get().Where("company_id = ? and funcname!=?", cid, "seek").Find(&userplatforms).Error; err != nil {
		return nil, err
	}
	for i, item := range userplatforms {
		userplatforms[i].PlatFormName = models.PlatFormName(item.Platform)
	}
	return userplatforms, nil
}

func (service *UserPlatformService) GetFuncListBy(uid uint64, platform models.PlatForm) ([]models.UserPlatform, error) {
	var userplatforms []models.UserPlatform
	if err := db.Instance().Get().Where("company_id = ? and platform=?", uid, platform).Find(&userplatforms).Error; err != nil {
		return nil, err
	}
	for i, item := range userplatforms {
		userplatforms[i].PlatFormName = models.PlatFormName(item.Platform)
	}
	return userplatforms, nil
}

func (service *UserPlatformService) UpdateFunction(u models.UserPlatform) {
	if u.ExpireTime == nil {
		u.Status = models.UserPlatformStatusNotOpened
	} else {
		u.Status = models.UserPlatformStatusOpened
	}
	//查列表 是为了可以用的sqlcache
	if userplatforms, error := service.GetFuncListBy(u.CompanyID, u.Platform); error == nil {
		found := false
		for _, v := range userplatforms {
			if v.Funcname == u.Funcname {
				found = true
				if v.ExpireTime != u.ExpireTime {
					v.ExpireTime = u.ExpireTime
					db.Instance().Get().Model(&v).Select("expire_time").Updates(&v)
				}
			}
		}
		if !found {
			db.Instance().Get().Save(&u)
		}
	}
}

func (service *UserPlatformService) GetAutoPostedPlatForms(cid uint64) ([]models.PlatForm, error) {
	var userplatforms []models.UserPlatform
	if err := db.Instance().Get().Where("company_id = ? and funcname=? and expire_time>=?", cid, models.FuncnamePost, dbtypes.SHNow()).Find(&userplatforms).Error; err != nil {
		return nil, err
	}
	var result []models.PlatForm
	for _, item := range userplatforms {
		result = append(result, item.Platform)
	}
	return result, nil
}
func (service *UserPlatformService) GetPlatForms(cid uint64) ([]models.PlatForm, error) {
	var userplatforms []models.UserPlatform
	if err := db.Instance().Get().Where("company_id = ? and funcname=?", cid, models.FuncnamePost).Find(&userplatforms).Error; err != nil {
		return nil, err
	}
	var result []models.PlatForm
	for _, item := range userplatforms {
		result = append(result, item.Platform)
	}
	return result, nil
}
