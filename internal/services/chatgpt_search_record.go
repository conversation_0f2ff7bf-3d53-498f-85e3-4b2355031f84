package services

import (
	"git.paihang8.com/lib/goutils"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/pkg/db"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

type SearchRecordInfoService struct {
}

func NewSearchRecordInfoService() *SearchRecordInfoService {
	return &SearchRecordInfoService{}
}

func (svc *SearchRecordInfoService) Create(record *models.ChatSearchRecord) error {
	record.KwHash = goutils.Md5str(record.Keyword)
	return db.Instance().Get().Clauses(clause.OnConflict{
		DoUpdates: clause.Assignments(map[string]interface{}{"cnt": gorm.Expr("cnt+1"), "updated_at": time.Now()}),
	}).Create(record).Error
}

func (svc *SearchRecordInfoService) Allowed(companyId uint64, AiType int) bool {
	return true
}
func (svc *SearchRecordInfoService) Histories(companyId uint64, aiType int) ([]*models.ChatSearchRecord, error) {
	var items []*models.ChatSearchRecord
	err := db.Instance().Get().Model(models.ChatSearchRecord{}).Where("company_id=? and ai_type=?", companyId, aiType).Order("updated_at desc").Limit(10).Find(&items).Error
	return items, err
}
