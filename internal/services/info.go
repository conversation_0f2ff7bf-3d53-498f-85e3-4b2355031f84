package services

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services/service"
	"gitlab.com/all_publish/api/pkg/datex"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
)

type InfoPaginator struct {
	Items     []models.Info     `json:"items"`
	Paginator service.Paginator `json:"paginator"`
}

type InfoService struct {
}

func NewInfoService() *InfoService {
	return &InfoService{}
}

func (svc *InfoService) CountInfos(companyIds []uint64, status int, keyword, pubType string, platform models.PlatForm, synced bool) int64 {
	var query = db.Instance().Get()
	if len(companyIds) > 0 {
		query = query.Where("company_id in (?)", companyIds)
	}

	if dbtypes.IsInfoStatusValid(status) {
		query = query.Where("status = ?", status)
	}
	if keyword != "" {
		query = query.Where("title like ?", "%"+service.EscapeLike(keyword)+"%")
	}
	if platform != models.PlatformAll {
		query = query.Where("platform=?", platform)
	}
	if pubType == "auto" {
		query = query.Where("product_id is not null")
	} else if pubType == "manual" {
		query = query.Where("product_id is null")
	}
	if synced {
		query = query.Where("pub_res->'$.ext.synced'=true")
	}
	var total int64
	query.Model(&models.Info{}).Count(&total)
	return total
}

func (svc *InfoService) CountOtherInfos(companyId uint64) int64 {
	var query = db.Instance().Get()
	query = query.Where("company_id = ?", companyId)

	query = query.Where("status = ?", dbtypes.InfoStatusPublishSuccessed)
	query = query.Where("platform !=?", models.PlatformHy88)
	var total int64
	query.Model(&models.Info{}).Count(&total)
	return total
}

func (svc *InfoService) GetInfos(companyIds []uint64, status int, keyword, pubType, sortby, order string, limit, offset int, platform models.PlatForm, synced bool, withCompany bool) ([]models.Info, error) {
	var infos []models.Info
	// Get all matched records
	var query = db.Instance().Get()
	if len(companyIds) > 0 {
		//if !dbtypes.IsInfoStatusValid(status) {
		//	query = query.Clauses(hints.ForceIndex("com_create"))
		//}
		query = query.Where("company_id in (?)", companyIds)
	}

	if dbtypes.IsInfoStatusValid(status) {
		query = query.Where("status = ?", status)
	}
	if keyword != "" {
		query = query.Where("title like ?", "%"+service.EscapeLike(keyword)+"%")
	}
	if pubType == "auto" {
		query = query.Where("product_id is not null")
	} else if pubType == "manual" {
		query = query.Where("product_id is null")
	}
	if platform != models.PlatformAll {
		query = query.Where("platform=?", platform)
	}
	if synced {
		query = query.Where("JSON_EXTRACT(pub_res,'$.ext.synced')=true")
	}

	query = query.Order(fmt.Sprintf("%s %s", sortby, order))
	query = query.Limit(limit).Offset(offset)
	if err := query.Find(&infos).Error; err != nil {
		return nil, err
	}
	for i, info := range infos {
		info := info
		infos[i] = info
	}

	if withCompany {
		infos = svc.patchInfosWithCompany(infos)
	}
	for i, item := range infos {
		infos[i].PlatFormName = models.PlatFormName(item.Platform)
	}
	return infos, nil
}

func (svc *InfoService) GetInfosWithPaginator(companyIds []uint64, status int, keyword, pubType, sortby, order string, limit, offset int, platform models.PlatForm, synced bool, withCompany bool) (*InfoPaginator, error) {
	if infos, err := svc.GetInfos(companyIds, status, keyword, pubType, sortby, order, limit, offset, platform, synced, withCompany); err != nil {
		return nil, err
	} else {
		return &InfoPaginator{
			Items:     infos,
			Paginator: service.NewPaginatorByOffset(offset, limit, int(svc.CountInfos(companyIds, status, keyword, pubType, platform, synced))),
		}, nil
	}
}

func (svc *InfoService) patchInfosWithCompany(infos []models.Info) []models.Info {
	if len(infos) > 0 {
		companyMap := map[uint64]models.Company{}
		companies := []models.Company{}
		set := make(map[uint64]bool)
		ids := []uint64{}
		for _, info := range infos {
			if set[info.CompanyID] {
				continue
			}
			ids = append(ids, info.CompanyID)
			set[info.CompanyID] = true
		}
		cdb := db.Instance().Get()
		cdb.Where("id in (?)", ids).Find(&companies)
		if len(companies) > 0 {
			for _, c := range companies {
				companyMap[c.ID] = c
			}
			for i, info := range infos {
				company := companyMap[info.CompanyID]
				infos[i].Company = &company
			}
		}
	}
	return infos
}

func (svc *InfoService) GetItemsOfProduct(productId uint64, status int) ([]models.Info, error) {
	var infos []models.Info
	dbHelper := db.Instance().Get()
	dbHelper = dbHelper.Where("product_id =  ?", productId)
	dbHelper = dbHelper.Where("will_pub_at < ?", dbtypes.SHNow())
	dbHelper = dbHelper.Where("status = ?", status)
	if err := dbHelper.Find(&infos).Error; err != nil {
		return nil, err
	}
	return svc.patchInfosWithCompany(infos), nil
}

func (svc *InfoService) CountByCompanyId(companyId uint64) int64 {
	var total int64
	db.Instance().Get().Model(&models.Info{}).Where("company_id = ?", companyId).Count(&total)
	return total
}

func (svc *InfoService) SuccessCountByCompanyId(companyId uint64, onlyYestoday bool) int64 {
	var total int64
	q := db.Instance().Get().Model(&models.Info{}).Where("company_id = ? and status=2", companyId)
	if onlyYestoday {
		start, end := datex.GetYesterdayTime()
		q = q.Where("updated_at between ? and ?", start, end)
	}
	q.Count(&total)
	return total
}

func (svc *InfoService) GroupByMerchant(productIds []uint64, t *time.Time) ([]models.InfoGroupByMerchant, error) {
	var infoGroupByMerchants []models.InfoGroupByMerchant
	if err := db.Instance().Get().Raw("select pub_res->'$.merchant_id' as merchant, count(*) as count from info where product_id in (?)"+
		" and created_at > ? group by merchant ", productIds, t).Scan(&infoGroupByMerchants).Error; err != nil {
		return nil, err
	}
	return infoGroupByMerchants, nil
}

func (svc *InfoService) Create(info *models.Info) error {
	ext := models.InfoExt{
		MID:         0,
		Pic:         info.Pic,
		TitlePic:    info.TitlePic,
		Description: info.Description,
	}
	if err := db.Instance().Get().Create(info).Error; err == nil {
		ext.MID = info.ID
		return db.Instance().Get().Table(ext.TableNameById(info.ID)).Create(&ext).Error
	} else {
		return err
	}
}

func (svc *InfoService) PatchExt(info *models.Info) error {
	var ext models.InfoExt
	if info.Description != "" {
		return nil
	}
	if err := db.Instance().Get().Table(ext.TableNameById(info.ID)).Where("m_id=?", info.ID).First(&ext).Error; err == nil {
		info.Pic = ext.Pic
		info.TitlePic = ext.TitlePic
		info.Description = ext.Description
		return nil
	} else {
		return err
	}
}

func (svc *InfoService) Find(id int) (info *models.Info, err error) {
	err = db.Instance().Get().Find(&info, id).Error
	if info.ID == 0 {
		info = nil
		err = errors.New("数据不存在")
	}
	if err == nil {
		svc.PatchExt(info)
		info.PlatFormName = models.PlatFormName(info.Platform)
		// 获取产品信息
		product := models.Product{}
		db.Instance().Get().Where("id = ?", info.ProductID).First(&product)
		info.ProductName = product.Name
		if len(product.Alias) > 0 {
			info.ProductName += "," + strings.Join(product.Alias, ",")
		}
		return
	}
	return
}

func (service *InfoService) GroupByPlatforms(companyId uint64) (map[int]schemas.InfoGroupByPlatform, error) {
	// 查询每个商户昨日已经发布的条数
	var groups []schemas.InfoGroupByPlatform
	n := time.Now()
	if err := db.Instance().Get().Raw("select platform, count(*) as count from info where company_id =  ?  and status=2 and updated_at between ? and ? group by platform", companyId, n.Add(-86400*time.Second).Format("2006-01-02"), n.Format("2006-01-02")).Scan(&groups).Error; err != nil {
		return nil, err
	}
	m := make(map[int]schemas.InfoGroupByPlatform)
	for _, g := range groups {
		m[g.Platform] = g
	}
	return m, nil
}

func (service *InfoService) GroupByPlatformAndStatus(companyId uint64, onlyYesterday bool) (map[int]map[int]int, error) {
	// 查询每个商户昨日已经发布的条数
	var groups []schemas.InfoGroupByPlatformStatus
	n := time.Now()
	if onlyYesterday {
		if err := db.Instance().Get().Raw("select platform,status, count(*) as count from info where company_id =  ? and updated_at between ? and ? group by platform, status", companyId, n.Add(-86400*time.Second).Format("2006-01-02"), n.Format("2006-01-02")).Scan(&groups).Error; err != nil {
			return nil, err
		}
	} else {
		if err := db.Instance().Get().Raw("select platform, status, count(*) as count from info where company_id =  ?  group by platform, status", companyId).Scan(&groups).Error; err != nil {
			return nil, err
		}
	}

	m := make(map[int]map[int]int)
	for _, g := range groups {
		var item map[int]int
		if pre, ok := m[g.Platform]; !ok {
			item = map[int]int{
				dbtypes.InfoStatusPublishSuccessed: 0,
				dbtypes.InfoStatusPublishFailed:    0,
				dbtypes.InfoStatusWattingpublish:   0,
			}
		} else {
			item = pre
		}
		item[g.Status] = g.Count
		m[g.Platform] = item
	}
	return m, nil
}
