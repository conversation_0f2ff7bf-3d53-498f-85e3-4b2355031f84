package services

import (
	"gitlab.com/all_publish/api/test"
	"testing"
)

func TestUserService_GetUserByOem(t *testing.T) {
	test.InitTest(t)
	t.Run("exist", func(t *testing.T) {
		if u, e := NewUserService().GetUserByOem(1530371); e != nil {
			t.<PERSON>rror(e)
		} else if u.OemId != 1530371 {
			t.<PERSON><PERSON><PERSON>("not match, expected 1530371, real is %d", u.OemId)
		}
	})
	t.Run("not exist", func(t *testing.T) {
		if _, e := NewUserService().GetUserByOem(1); e == nil {
			t.Error("expect error, real is nil")
		}
	})
}
