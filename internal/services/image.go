package services

import (
	"errors"
	"fmt"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services/service"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/gorm"
	"unicode/utf8"
)

type ImageServicer interface {
	AddToAlbum(image *models.Image, albumId uint64) error
	//获取图片列表
	ListOfAlbum(limit, offset int, albumId uint64) ([]models.Image, error)

	//去重，只针对当前用户。万一将来需要支持删除图片功能呢？
	Exist(hash string, companyId uint64) (*models.Image, error)

	GetByUrl(url string) (*models.Image, error)
	SetMappings(url string, targetId int, targetUrl string, platform models.PlatForm) error
	GetMappings(url string, platform models.PlatForm) (*models.ImageMappings, error)

	Delete(cid, aid uint64, ids []uint64) error
	Moveto(cid uint64, ids []uint64, fromAlbumId, toAlbumId uint64) error
	SetUsed(url string) error
	// 更新图片。动态更新
	Update(cid, id uint64, m map[string]interface{}) (models.Image, error)
	Search(cid uint64, albumId uint64, kw string, limit, offset int) (result []models.Image, err error)
}

type ImageService struct {
}

func (svc *ImageService) SetMappings(url string, targetId int, targetUrl string, platform models.PlatForm) error {
	if img, err := svc.GetByUrl(url); err != nil {
		return err
	} else {
		var mapping models.ImageMappings
		mapping.PlatForm = int8(platform)
		mapping.ImageID = img.ID
		mapping.ImageUrl = url
		mapping.TargetImageID = targetId
		mapping.TargetUrl = targetUrl
		return db.Instance().Get().Model(&mapping).Create(mapping).Error
	}
}

func (svc *ImageService) GetMappings(url string, platform models.PlatForm) (*models.ImageMappings, error) {
	var img models.ImageMappings
	if err := db.Instance().Get().Model(&models.ImageMappings{}).Where("image_url = ? and plat_form=?", url, platform).First(&img).Error; err != nil {
		return nil, err
	}
	return &img, nil
}

func (svc *ImageService) GetByUrl(url string) (*models.Image, error) {
	var img models.Image
	if err := db.Instance().Get().Model(&models.Image{}).Where("url = ?", url).First(&img).Error; err != nil {
		return nil, err
	}
	return &img, nil
}

func (svc *ImageService) Exist(hash string, companyId uint64) (*models.Image, error) {
	var img models.Image
	img.CompanyId = companyId
	if err := db.Instance().Get().Model(&models.Image{}).Where("company_id = ? and hash = ?", companyId, hash).First(&img).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &img, nil
}

func (svc *ImageService) ListOfAlbum(limit, offset int, albumId uint64) ([]models.Image, error) {
	if _, err := NewAlbumService(db.Instance().Get()).Find(albumId); err != nil {
		return nil, err
	} else {
		var imgs []models.Image
		if err := db.Instance().Get().Where("album_id=? and status=?", albumId, models.ImageStatusEnable).Order("id desc").Limit(limit).Offset(offset).Find(&imgs).Error; err != nil {
			return nil, err
		}
		return imgs, nil
	}

}

func NewImageService() ImageServicer {
	return &ImageService{}
}

func (svc *ImageService) AddToAlbum(image *models.Image, albumId uint64) error {
	var album *models.Album
	var err error
	if albumId > 0 {
		if album, err = NewAlbumService(db.Instance().Get()).Find(albumId); err != nil {
			return err
		}
	} else {
		if album, err = NewAlbumService(db.Instance().Get()).DefaultAlbum(image.CompanyId); err != nil {
			return err
		}
	}
	if album.CompanyId != image.CompanyId {
		return fmt.Errorf("非法操作, 相册不是你的")
	}
	if exits, err := svc.Exist(image.Hash, image.CompanyId); err != nil {
		return err
	} else if exits != nil {
		var err error
		// 之前关闭的再打开，需要更新图片数
		if exits.Status == 1 {
			err = svc.Recover(exits.CompanyId, exits.AlbumId, []uint64{exits.ID})
		}
		if exits.AlbumId != albumId {
			err = svc.Moveto(exits.CompanyId, []uint64{exits.ID}, exits.AlbumId, albumId)
		}
		return err
	}
	return db.Instance().Get().Transaction(func(tx *gorm.DB) error {
		return svc.addToAlbum(tx, image, album)
	})
}

func (svc *ImageService) Delete(cid, aid uint64, ids []uint64) error {
	var album *models.Album
	var err error
	if album, err = NewAlbumService(db.Instance().Get()).Find(aid); err != nil {
		return err
	}
	if album.CompanyId != cid {
		return fmt.Errorf("非法操作，指定的相册不是你的")
	}
	var imgs []models.Image
	if err = db.Instance().Get().Model(&models.Image{}).Where("album_id = ? and  id in ? and status=?", album.ID, ids, models.ImageStatusEnable).Find(&imgs).Error; err != nil {
		return err
	}
	var total, sizes int
	for _, img := range imgs {
		total += 1
		sizes += img.Size
	}
	if total == 0 {
		return nil
	}
	return db.Instance().Get().Transaction(func(tx *gorm.DB) error {
		tx = tx.Exec("update images set status=? where album_id = ? and id in ?", models.ImageStatusDisable, album.ID, ids)
		if tx.Error != nil {
			return err
		}
		if tx.RowsAffected > 0 {
			deleted := tx.RowsAffected
			err = tx.Exec("update user_stats set photo_cnt=photo_cnt-? where company_id=? ", deleted, cid).Error
			if err != nil {
				return err
			}
			// 相册的数据得更新下
			err = tx.Exec("update albums set display_total=display_total-?, display_sizes=display_sizes-? where id=? ", deleted, sizes, aid).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

/**
Delete的反向操作
*/
func (svc *ImageService) Recover(cid, aid uint64, ids []uint64) error {
	var album *models.Album
	var err error
	if album, err = NewAlbumService(db.Instance().Get()).Find(aid); err != nil {
		return err
	}
	if album.CompanyId != cid {
		return fmt.Errorf("非法操作，指定的相册不是你的")
	}
	var imgs []models.Image
	if err = db.Instance().Get().Model(&models.Image{}).Where("album_id = ? and  id in ? and status=?", album.ID, ids, models.ImageStatusDisable).Find(&imgs).Error; err != nil {
		return err
	}
	var total, sizes int
	for _, img := range imgs {
		total += 1
		sizes += img.Size
	}
	if total == 0 {
		return nil
	}
	return db.Instance().Get().Transaction(func(tx *gorm.DB) error {
		tx = tx.Exec("update images set status=? where album_id = ? and id in ?", models.ImageStatusEnable, album.ID, ids)
		if tx.Error != nil {
			return err
		}
		if tx.RowsAffected > 0 {
			deleted := tx.RowsAffected
			err = tx.Exec("update user_stats set photo_cnt=photo_cnt+? where company_id=? ", deleted, cid).Error
			if err != nil {
				return err
			}
			// 相册的数据得更新下
			err = tx.Exec("update albums set display_total=display_total+?, display_sizes=display_sizes+? where id=? ", deleted, sizes, aid).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

func (svc *ImageService) Moveto(cid uint64, ids []uint64, frid, toid uint64) error {
	var imgs []models.Image
	var err error
	if err = db.Instance().Get().Model(&models.Image{}).Where("company_id = ? and album_id = ? and  id in ?", cid, frid, ids).Find(&imgs).Error; err != nil {
		return err
	}
	cnt := len(imgs)
	size := 0
	for _, v := range imgs {
		size += v.Size
	}
	return db.Instance().Get().Transaction(func(tx *gorm.DB) error {
		tx = tx.Exec("update images set album_id=? where company_id = ? and album_id = ? and id in ?", toid, cid, frid, ids)
		if tx.Error != nil {
			return err
		}
		if tx.RowsAffected > 0 {
			err = tx.Exec("update albums set total=total-?, sizes=sizes-?,display_total=display_total-?, display_sizes=display_sizes-?  where id=? ", cnt, size, cnt, size, frid).Error
			if err != nil {
				return err
			}
			err = tx.Exec("update albums set total=total+?, sizes=sizes+?,display_total=display_total+?, display_sizes=display_sizes+?  where id=? ", cnt, size, cnt, size, toid).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

func (svc *ImageService) SetUsed(url string) error {
	if img, err := svc.GetByUrl(url); err != nil {
		return err
	} else if img.Using == uint8(models.ImageUsingTrue) {
		return nil
	} else {
		img.Using = models.ImageUsingTrue
		return db.Instance().Get().Select("using").Updates(&img).Error
	}
}

/*
 addToAlbum 1. 插入image表。2 更新album表, 3更新userstat统计表
*/
func (svc *ImageService) addToAlbum(tx *gorm.DB, image *models.Image, album *models.Album) error {
	image.AlbumId = album.ID

	if err := db.Instance().Get().Create(&image).Error; err != nil {
		return err
	}
	if err := NewAlbumService(tx).AddImage(album, image); err != nil {
		return err
	}
	return nil
}

func (svc *ImageService) Update(cid, id uint64, m map[string]interface{}) (image models.Image, err error) {
	if err = db.Instance().Get().Where("id=?", id).First(&image).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("图片不存在")
			return
		}
		return
	}
	if image.CompanyId != cid {
		err = errors.New("非法操作，不是你的图片")
		return
	}
	delete(m, "id")
	if name, ok := m["name"]; ok {
		if utf8.RuneCountInString(name.(string)) > 8 {
			err = fmt.Errorf("名称太长，最多为8个字")
			return
		}
	}

	if err = dbtypes.UpdateModelFromMap(&image, m); err != nil {
		return
	}
	if err = db.Instance().Get().Select("name", "tag").Updates(&image).Error; err != nil {
		return
	}
	return
}

func (svc *ImageService) Search(cid uint64, albumId uint64, kw string, limit, offset int) (result []models.Image, err error) {
	err = db.Instance().Get().Where("company_id = ? and album_id = ? and name like ?", cid, albumId, "%"+service.EscapeLike(kw)+"%").Limit(limit).Offset(offset).Find(&result).Error
	return

}
