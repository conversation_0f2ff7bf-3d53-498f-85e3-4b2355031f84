package services

import (
	"errors"
	"gitlab.com/all_publish/api/internal/models"
	"gorm.io/gorm"
	"time"
)

type notify struct {
	db *gorm.DB
}

func NewNotifyService(db *gorm.DB) *notify {
	return &notify{db: db}
}

func (s *notify) MarkNotified(companyId int, typ models.NotifyType) error {
	var item models.Notify
	err := s.db.Model(models.Notify{}).Assign(models.Notify{
		LastNotifyTime: time.Now(),
	}).FirstOrCreate(&item, models.Notify{
		NotifyType: typ,
		CompanyId:  companyId,
	}).Error
	return err
}

func (s *notify) LastNotified(companyId int, typ models.NotifyType) (*models.Notify, error) {
	var item models.Notify
	if err := s.db.Model(models.Notify{}).Where("company_id=? and notify_type=?", companyId, typ).First(&item).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	} else {
		return &item, nil
	}
}
