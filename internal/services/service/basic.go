package service

import (
	"errors"
	"regexp"
	"strconv"
	"strings"
)

func IsPhone(keyword string) bool {
	pattern := `^[0-9]{11}$` //反斜杠要转义
	result, _ := regexp.MatchString(pattern, keyword)
	if result {
		return true
	}
	return false
}

func IsUserName(keyword string) bool {
	pattern := `[0-9a-zA-Z]+` //反斜杠要转义
	result, _ := regexp.MatchString(pattern, keyword)
	if result {
		return true
	}
	return false
}

func EscapeLike(name string) string {
	name = strings.Replace(name, "%", `\%`, -1)
	name = strings.Replace(name, "_", `\_`, -1)
	name = strings.Replace(name, `\`, `\\`, -1)
	return name
}

type Paginator struct {
	ItemsTotal int  `json:"items_total"` //总数
	PageSize   int  `json:"page_size"`   //每页多少条
	PageTotal  int  `json:"page_total"`  //多少页
	HasNext    bool `json:"has_next"`    //是否有下一页
	HasPrev    bool `json:"has_prev"`    // 是否有上一页
	Page       int  `json:"page"`        //当前第几页
}

func NewPaginatorByOffset(offset int, pageSize int, total int) Paginator {
	page := offset/pageSize + 1
	return NewPaginatorByPage(page, pageSize, total)
}

func NewPaginatorByPage(page int, pageSize int, total int) Paginator {
	paginator := Paginator{
		ItemsTotal: total,
		PageSize:   pageSize,
		Page:       page,
	}
	paginator.PageTotal = total / pageSize
	if total%pageSize > 0 {
		paginator.PageTotal += 1
	}

	if page > 1 {
		paginator.HasPrev = true
	}

	if page < paginator.PageTotal {
		paginator.HasNext = true
	}
	return paginator
}

/**
0 相等
-1 小于
1 大于
*/
func VersionCompare(versions string, v1, v2, v3 int) (int, error) {
	if versions != "" {
		vv := strings.Split(versions, ".")
		if len(vv) == 3 {
			vv0, _ := strconv.Atoi(vv[0])
			vv1, _ := strconv.Atoi(vv[1])
			vv2, _ := strconv.Atoi(vv[2])
			if vv0 <= v1 && vv1 <= v2 && vv2 <= v3 {
				if vv0 == v1 && vv1 == v2 && vv2 == v3 {
					return 0, nil
				}
				return -1, nil
			}
			return 1, nil
		}
		return 0, errors.New("versions not valid")
	}
	return 0, errors.New("version is empty")
}
