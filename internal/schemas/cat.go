package schemas

type Cat struct {
	ID         uint   `json:"id"`
	CatN<PERSON>    string `json:"cat_name"`
	CatEn      string `json:"cat_en"`
	CatTrace   string `json:"cat_trace"`
	CatTraceEn string `json:"cat_trace_en"`
	ParentId   int    `json:"parent_id"`
}

type CatField struct {
	Displayname  string        `json:"displayname"`
	Fieldtype    string        `json:"fieldtype"`
	Fieldname    string        `json:"fieldname"`
	Fieldoptions []interface{} `json:"fieldoptions"`
	Sort         int           `json:"-"`
	Required     bool          `json:"required"`
}
