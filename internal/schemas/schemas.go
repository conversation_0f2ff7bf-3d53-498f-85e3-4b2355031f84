package schemas

import (
	"time"

	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gopkg.in/go-playground/validator.v9"
	"gorm.io/datatypes"
)

type Album struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	IsOpen      string `json:"is_open"`
}

type AlbumItem publisher.Album

type AlbumResponse struct {
	Albumid string `json:"albumid"`
}

type AuditCompanyFields struct {
	License     *string  `json:"license,omitempty"`
	Cate        []string `json:"cate,omitempty"`
	Site        *string  `json:"site,omitempty"`
	Address     string   `json:"address,omitempty"`
	Logo        *string  `json:"logo,omitempty"`
	Qrcode      *string  `json:"qrcode,omitempty"`
	Introduce   string   `json:"introduce,omitempty"`
	ContactName string   `json:"contact_name,omitempty"`
	Gender      *int     `json:"gender,omitempty"`
	AdvisorID   *int     `json:"advisor_id,omitempty"`
	Phone       []string `json:"phone,omitempty"`
	Qq          *string  `json:"qq,omitempty"`
	AreaId      int      `json:"area_id,omitempty"`
	CateId      int      `json:"cate_id,omitempty"`
}

type CheckModifyCompany struct {
	ID             int                    `json:"id" validate:"required"`
	AuditingFields map[string]interface{} `json:"auditing_fields" validate:"required"`
}

type AuditNotPass struct {
	AuditRes  string                 `json:"audit_res"`
	AuditRes2 map[string]interface{} `json:"audit_res2"`
}

type Login struct {
	GrantType string `json:"grant_type" enums:"user,system" default:"user" required:"false"`
	Username  string `json:"username" ` //系统登录使用
	Mobile    string `json:"mobile"`    //黄页88登录必须
	Password  string `json:"password" validate:"required,min=1"`
}

func LoginStructLevelValidation(sl validator.StructLevel) {
	login := sl.Current().Interface().(Login)

	if login.GrantType == "user" && len(login.Mobile) != 11 {
		sl.ReportError(login.Mobile, "Mobile", "mobile", "mobile", "")
	}
	if login.GrantType == "system" && len(login.Username) < 3 {
		sl.ReportError(login.Username, "Username", "username", "username", "length of username should >= 6")
	}
}

type LoginResult struct {
	AccessToken string   `json:"access_token,omitempty"`
	Exp         int64    `json:"exp,omitempty"`
	Scopes      []string `json:"scopes,omitempty"`
	Role        string   `json:"role,omitempty"`
	Id          uint64   `json:"id,omitempty"`
	FromKuyiso  bool     `json:"fromKuyiso"` // 是否酷易搜注册的
	OemId       uint64   `json:"oem_id"`
}

type ModifyPassword struct {
	Old string `json:"old" validate:"required"`
	New string `json:"new" validate:"required"`
}

type ModifySystemUser struct {
	Name   string   `json:"name"`
	Role   *string  `json:"role"`
	Phone  *string  `json:"phone"`
	Email  *string  `json:"email"`
	Ban    bool     `json:"ban"`
	Scopes []string `json:"scopes"`
}

type RanksData struct {
	publisher.RanksData
	Statics RankStatics `json:"statics"`
}

type RankStatics struct {
	InfoTotal int `json:"info_total"` // 信息总数
	RankTotal int `json:"rank_total"` // 排名总数
	BaiDu     int `json:"baidu"`      // 百度
	Sougou    int `json:"sougou"`     // 搜狗
	ShenMa    int `json:"shenma"`     // 神马
	HaoSou    int `json:"haosou"`     // 好搜
	TouTiao   int `json:"toutiao"`    // 头条
}

type Similar struct {
	Index int      `json:"index"`
	Data  []string `json:"data"`
}

type ProductGroupByStatus struct {
	Status int `json:"status"`
	Count  int `json:"count"`
}

type StatusCount map[int]int

type UploadApiResponse struct {
	Code  int            `json:"code"`
	Error string         `json:"error"`
	Data  UploadResponse `json:"data"`
}

type UploadResponse struct {
	Url  string `json:"url"`
	Etag string `json:"etag"` //md5值
	Size int    `json:"size"` //文件大小
	Msg  string `json:"msg"`
}

type Product struct {
	dbtypes.Base
	ID        uint64                 `gorm:"primary_key" json:"id"`
	Name      string                 `json:"name,editable"`
	Brand     string                 `json:"brand,editable"` //必填项
	AuditRes2 map[string]interface{} `gorm:"-" json:"audit_res2"`
	Status    uint8                  `json:"status"`
	Mode      int                    `json:"mode"`
	Platforms datatypes.JSONMap      `json:"platforms"`
}

type AdminProduct struct {
	dbtypes.Base
	ID           uint64                 `gorm:"primary_key" json:"id"`
	Name         string                 `json:"name,editable"`
	CompanyID    uint64                 `json:"company_id"`
	Company      Company                `json:"company,editable"`
	Phone        string                 `json:"phone"`
	Status       uint8                  `json:"status"`
	AuditRes     *string                `json:"audit_res"`
	AuditRes2    map[string]interface{} `gorm:"-" json:"audit_res2"`
	ModifyFields []string               `gorm:"-" json:"modify_fields"`
	Platforms    datatypes.JSONMap      `json:"platforms"`
	ChangedAt    time.Time              `json:"changed_at"`
	Mode         uint8                  `json:"mode"`
}

type Company struct {
	dbtypes.Base
	ID             uint64            `gorm:"primary_key" json:"id"`
	Name           string            `json:"name"`                                                     // 企业名称
	AdvisorID      uint64            `gorm:"not null;default:1", json:"advisor_id,omitempty,editable"` // 管理员id
	AuditingFields datatypes.JSONMap `gorm:"type:json;column:auditing_fields" json:"auditing_fields"`  // 拒绝字段
	AuditRes       *string           `json:"audit_res,omitempty"`                                      // 拒绝原因
	Cate           dbtypes.JSONArray `gorm:"type:json;column:cate" json:"cate,omitempty,editable"`
	ContactName    string            `json:"contact_name,omitempty,editable" validate:"required"`                        // 公司联系人, 必填字段
	Phone          dbtypes.JSONArray `gorm:"type:json;column:phone" json:"phone,omitempty,editable" validate:"required"` // 联系电话, 必填字段 [手机,公司座机，座机]

}

type DelImages struct {
	Ids  []uint64 `json:"ids"`
	From uint64   `json:"from"` // 来自哪个相册
}

type MoveImages struct {
	Ids  []uint64 `json:"ids"`
	From uint64   `json:"from"` // 来自哪个相册
}
