package schemas

type UserOverView struct {
	Product  ProductStat      `json:"product"`
	Info     InfoOverview     `json:"info"`
	Rank     RankOverview     `json:"rank"`
	Platform PlatformOverview `json:"platform"`
}

type ProductStat struct {
	Total int `json:"total"` // 产品总数
	// 推广中个数
	Promotions int `json:"promotions"`
}

type InfoOverview struct {
	Total     int64 `json:"total"`     // 产品总数
	Yesterday int64 `json:"yesterday"` // 昨日新增
}

type RankOverview struct {
	Total int64 `json:"total"`
	HY88  int64 `json:"hy_88"`
}

type PlatformOverview struct {
	Activated int `json:"activated"`
	Total     int `json:"total"`
}
