package schemas

import "gitlab.com/all_publish/api/pkg/dbtypes"

type EditMerchant struct {
	Pic string `json:"pic" validate:"required"`
}

type MerchantStep struct {
	Name  string `json:"name"`
	Steps []Step `json:"steps"`
}

type StepStatus int
type Action int

const (
	StepStatusDone    = 1
	StepStatusTodo    = 0
	StepStatusUnReach = -1
)

type Step struct {
	Text   string         `json:"text"`   // 描述
	Action dbtypes.Action `json:"action"` //编辑去哪里 需要进一步处理 0-不需要处理 1-上传营业执照 2-上传企业声明 3-编辑爱采购
	Status StepStatus     `json:"status"` // 状态 1 完成。0 待完成。-1 未到达
	Reason string         `json:"reason"`
}

type BindKuyiso struct {
	Mobile   int    `json:"mobile" validate:"required" label:"手机号"`        //手机号
	Password string `json:"password" validate:"required,gte=6" label:"密码"` //密码
}
