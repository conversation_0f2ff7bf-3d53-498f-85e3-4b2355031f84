package schemas

import (
	"git.paihang8.com/lib/goutils"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/datatypes"
)

type InfoGroupByPlatform struct {
	Platform int    `json:"platform"`
	Name     string `json:"name"`
	Count    int    `json:"count"`
}

type InfoGroupByPlatformStatus struct {
	Platform int `json:"platform"`
	Count    int `json:"count"`
	Status   int `json:"status"`
}

type InfoGroupForClient struct {
	Platform int    `json:"platform"`
	Name     string `json:"name"`
	Count    string `json:"count"`
	Enabled  bool   `json:"enabled"`
}

type InfoStat map[int]InfoGroupForClient

type PubInfo struct {
	MerchantIds []int      `json:"merchant_ids"` // 商铺id列表。
	Fields      InfoFields `json:"fields"`
}

type InfoFields struct {
	Title       string            `json:"title,editable"`
	Cate        dbtypes.JSONArray `json:"cate,omitempty,editable"`
	AreaIds     dbtypes.JSONArray `gorm:"type:json;column:areaids" json:"areaids,editable"`
	Word        dbtypes.JSONArray `json:"word,omitempty"`
	Brand       string            `json:"brand,editable"` //必填项
	Price       float32           `json:"price"`
	Unit        string            `json:"unit"`
	MinOrder    *int              `json:"min_order,editable"`
	Inventory   *int              `json:"inventory,editable"`
	Properties  datatypes.JSONMap `json:"properties,omitempty,editable"`
	TitlePic    dbtypes.JSONArray `json:"title_pic,omitempty"`
	Description string            `json:"description"`
}

func (fields InfoFields) GetHy88Properties() map[string]interface{} {
	if len(fields.Properties) == 0 {
		return fields.Properties
	}
	m := make(map[string]interface{})
	for k, v := range fields.Properties {
		if goutils.IsNumeric(k) {
			m[k] = v
		}
	}
	return m
}
