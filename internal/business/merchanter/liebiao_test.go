package merchanter

import (
	"git.paihang8.com/lib/goutils/sites/liebiao"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/test"
	"testing"
)

func TestLiebiaoMerchant_SelectAreaId(t *testing.T) {
	test.InitTest(t)
	m := LiebiaoMerchant{
		name:     "列表网",
		platform: models.PlatformLiebiao,
		api:      liebiao.LiebiaoPublisher,
	}
	t.Log(m.SelectAreaId([]int{12}, "交通设施水马围挡特点"))
}
