package merchanter

import (
	"errors"
	"fmt"
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"git.paihang8.com/lib/goutils/sites/sole"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/cat"
	"gitlab.com/all_publish/api/pkg/convert"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"regexp"
	"strconv"
	"strings"
)

func init() {
	item := NewSoleMerchanter()
	models.RegisterMerchant(item.PlatForm(), item)
}

type SoleMerchant struct {
	name     string
	platform models.PlatForm
	account  merchants.SoleAccount
	api      sole.PublishApi
	merchant *models.Merchant
}

func NewSoleMerchanter() models.Merchanter {
	return &SoleMerchant{
		name:     "搜了网",
		platform: models.PlatformSole,
		api:      sole.DefaultPublisher,
	}
}
func (m *SoleMerchant) GetApi() interface{} {
	return m.api
}

func (m *SoleMerchant) initMerchant(cid uint64) {
	if m.merchant == nil || m.merchant.CompanyID != cid {
		db.Instance().Get().Model(models.Merchant{}).Where("company_id=? and plat_form=?", cid, m.platform).First(&m.merchant)
	}
}

func (m *SoleMerchant) PublishInfo(info models.Info) (publisher.SimpleMessage, error) {
	var res publisher.SimpleMessage
	if !configs.ApiConfig.Web.IsProduction {
		return publisher.SimpleMessage{
			Url: "https://www.51sole.com/",
			ID:  "0",
		}, nil
	}
	start := dbtypes.SHNow().UnixNano()
	services.NewInfoService().PatchExt(&info)
	if len(info.TitlePic) == 0 {
		info.TitlePic = info.Pic
	}

	if len(info.TitlePic) < 3 {
		return publisher.SimpleMessage{
			Url: "https://www.51sole.com/",
			ID:  "0",
		}, fmt.Errorf("标题图片太少, 要求至少3个，实际只有%d个", len(info.TitlePic))
	}

	var categoryId int
	if len(info.Product.Cate) > 0 {
		categoryId, _ = strconv.Atoi(info.Product.Cate[len(info.Product.Cate)-1])
	}
	if categoryId == 0 {
		categoryId = info.Company.GetCatID()
	}
	classmapping, err := cat.NewCatService().GetMapping(uint64(categoryId), models.PlatformSole)
	if err != nil {
		return publisher.SimpleMessage{}, err
	}
	var soleCate models.SoleCat
	if soleCate, err = cat.NewCatService().GetSoleCate(convert.Str(classmapping.TargetCatID).MustInt()); err != nil {
		return publisher.SimpleMessage{}, err
	}
	if soleCate.Level != 3 {
		return publisher.SimpleMessage{}, errors.New("对应的分类不是最后一级")
	}
	//插入图片HY-4227
	preg := regexp.MustCompile("<br/>")
	description := ""
	matched := preg.FindAllStringSubmatchIndex(info.Description, -1)

	if len(matched) > 0 {
		var pre = 0
		var text = ""
		for i, match := range matched {
			if i > len(info.Pic)-1 {
				text = ""
			} else {
				text = `<br/><img src="` + info.Pic[i] + `" />`
			}
			description += info.Description[pre:match[0]] + text + info.Description[match[0]:match[1]]
			pre = match[1]
		}
		description += info.Description[pre:]
		//填充多余的图片
		if len(matched) < len(info.Pic) {
			leftPic := info.Pic[len(matched):]
			for _, p := range leftPic {
				description += `<br/><img src="` + p + `" />`
			}
		}
	} else if len(info.Pic) > 1 {
		description = `<img src="` + info.Pic[1] + `" />` + info.Description
	} else {
		description = info.Description
	}

	cats := strings.Split(soleCate.CatTrace, ",")
	msg := sole.PostProductReq{
		UserName:        m.account.UserName,
		Password:        m.account.Password,
		ProductName:     *info.Title,
		BigIndustryId:   convert.Str(cats[0]).MustInt(),
		MidIndustryId:   convert.Str(cats[1]).MustInt(),
		SmallIndustryId: convert.Str(cats[2]).MustInt(),
		ProductKey:      info.Product.Name,
		ProductPrice:    strconv.FormatFloat(float64(*info.Product.Price), 'f', 2, 64),
		Brand:           info.Product.Brand,
		Description:     description,
		Productimg:      info.TitlePic[0],
		Productimg2:     info.TitlePic[1],
		Productimg3:     info.TitlePic[2],
	}
	if info.Product.Brand == "" {
		msg.Brand = info.Company.ShortName
	}
	if len(info.TitlePic) >= 4 {
		msg.Productimg4 = info.TitlePic[3]
	}
	if len(info.TitlePic) >= 5 {
		msg.Productimg5 = info.TitlePic[4]
	}
	if len(info.TitlePic) >= 6 {
		msg.Productimg6 = info.TitlePic[5]
	}

	if len(info.Product.Properties) > 0 {
		var m = map[string]interface{}{}
		var cats = cat.NewCatService()
		for k, v := range info.Product.Properties {
			m[cats.GetPropertyName(k)] = v
		}
		msg = msg.WithProperties(m)
	}

	response, err := m.api.PostProduct(msg)
	if err != nil {
		return res, err
	}
	end := dbtypes.SHNow().UnixNano()
	log.Info("pubInfo", zap.Int64("cost ms", (end-start)/1e+6))
	services.NewInfosService().Add(info.CompanyID, response.RetURL, uint64(response.ID), models.PlatformSole)
	return publisher.SimpleMessage{
		ID:  strconv.Itoa(response.ID),
		Url: response.RetURL,
	}, nil
}

func (m *SoleMerchant) Name() string {
	return m.name
}

func (m *SoleMerchant) PlatForm() models.PlatForm {
	return m.platform
}

func (m *SoleMerchant) Clone() models.Merchanter {
	copyed := *m
	copyed.account = merchants.SoleAccount{}
	return &copyed
}

func (m *SoleMerchant) Meta() models.MerchantMeta {
	return models.MerchantMeta{
		Name:         m.name,
		Description:  "搜了网",
		PlatForm:     m.platform,
		PlatFormName: models.PlatFormName(m.platform),
		RequiredAccount: map[string]models.AccountItem{
			"username": {
				Type: "string",
				Desc: "用户名",
			},
			"password": {
				Type: "string",
				Desc: "密码",
			},
			"id": {
				Type: "string",
				Desc: "公司id",
			},
		},
		HomeUrl: "https://cn.china.cn/",
		LogoUrl: "https://himg.china.cn/img/common/logo/200x44.png",
	}
}

func (m *SoleMerchant) Account() map[string]interface{} {
	return m.account.ToMap()
}

func (m *SoleMerchant) UpdateAccount(key string, value interface{}) {
}

func (b *SoleMerchant) LoginBy(account map[string]interface{}) (models.User, error) {
	b.account.Init(account)
	return models.User{}, nil
}

func (b *SoleMerchant) IsErrorShouldStopAutoPub(err string) bool {
	if strings.Contains(err, "发布产品数量达到上限") {
		return true
	}
	if strings.Contains(err, "用户不存在或已失效") {
		return true
	}

	return false
}

func (m *SoleMerchant) Reg(c models.Company) (interface{}, error) {
	u := fmt.Sprintf("u%s", c.Phone[0])
	pwd := dbtypes.GenPassword(true, true, true, false, 8)
	if c.GetAreaID() == 0 {
		return nil, errors.New("公司地址没编辑，不能注册")
	}
	classmapping, err := cat.NewCatService().GetMapping(uint64(c.GetCatID()), models.PlatformSole)
	if err != nil {
		return publisher.SimpleMessage{}, errors.New("找不到对应的分类，不能注册")
	}
	if c.GetCatID() == 0 {
		return nil, errors.New("公司分类没编辑，不能注册")
	}
	var soleCate models.SoleCat
	if soleCate, err = cat.NewCatService().GetSoleCate(convert.Str(classmapping.TargetCatID).MustInt()); err != nil {
		return publisher.SimpleMessage{}, err
	}
	if soleCate.Level != 3 {
		return publisher.SimpleMessage{}, errors.New("对应的分类不是最后一级")
	}

	var areamapping, pAreaMapping models.AreaMappings
	areamapping, err = services.Area.GetMapping(uint64(c.GetAreaID()), models.PlatformSole)
	if err != nil {
		return publisher.SimpleMessage{}, errors.New("找不到对应的地区，不能注册")
	}

	if err != nil {
		return publisher.SimpleMessage{}, err
	}
	if item, err := services.Area.Find(areamapping.AreaID); err == nil {
		if pAreaMapping, err = services.Area.GetMapping(uint64(item.ParentID), models.PlatformSole); err != nil {
			return nil, err
		}
	}
	cats := strings.Split(soleCate.CatTrace, ",")
	p := sole.RegReq{
		UserName:        u,
		Pwd:             pwd,
		NiName:          c.ContactName,
		CompanyName:     c.Name,
		MobilePhone:     c.Phone[0],
		CompanyType:     c.CompanyType,
		WorkingModel:    c.WorkingModel,
		CompanyDesc:     c.Introduce,
		CompanyEmail:    c.Email,
		Area_t:          convert.Str(areamapping.TargetAreaID).MustInt(),
		Area_p:          convert.Str(pAreaMapping.TargetAreaID).MustInt(),
		BigIndustryId:   convert.Str(cats[0]).MustInt(),
		MidIndustryId:   convert.Str(cats[1]).MustInt(),
		SmallIndustryId: convert.Str(cats[2]).MustInt(),
		MainProduct:     c.MainProduct,
		MainBrand:       c.MainBrand,
		CompanyAddress:  c.Address,
		CompanyIntro:    c.ShortName,
		QQ:              *c.Qq,
	}
	//log.AppLogger().Debug(p)
	//data := sole.CommonResponse{
	//	Status: 1,
	//	ID:     5608635,
	//	RetURL: "http://e.51sole.com/u85242/",
	//	RetMsg: "注册成功",
	//}
	if data, err := m.api.Reg(p); err != nil {
		return nil, err
	} else {
		m.account.UserName = u
		m.account.Password = pwd
		m.account.Id = data.ID
		m.account.Site = data.RetURL
		return data, nil
	}
}

func (m *SoleMerchant) SubmitCert(c models.Company, ext ...interface{}) (interface{}, error) {
	if c.License == "" {
		return nil, errors.New("需要补全" + ext[0].(string))
	}
	req := sole.SubmitCertReq{
		ActionReq: sole.ActionReq{UserName: m.account.UserName,
			Password: m.account.Password,
		},
		OrganizationCode: c.RegNo,
		JiGouCode:        c.RegNo,
	}
	validPeriod := strings.Split(c.ValidPeriod, " - ")
	long := false
	if len(validPeriod) != 2 {
		return nil, errors.New("营业执照有效期格式不正确")
	}
	if validPeriod[1] == "永续经营" {
		validPeriod[1] = ""
		long = true
	}
	req.WithLicense(c.License, long, validPeriod[0], validPeriod[1]) // 营业执照
	if err := m.api.SubmitCert(req); err != nil {
		return nil, err
	} else {
		return nil, nil
	}
}

func (m *SoleMerchant) GetCert(cid, id int) (interface{}, error) {
	return m.api.GetCert(sole.ActionReq{UserName: m.account.UserName,
		Password: m.account.Password,
	})
}

func (m *SoleMerchant) IsErrorMsgRunOut(err string) bool {
	if strings.Contains(err, "发布产品数量达到上限") {
		return true
	}
	return false
}

func (m *SoleMerchant) RenewWhenNeeded(cid uint64) bool {
	m.initMerchant(cid)
	m.LoginBy(m.merchant.Account)
	if m.merchant.Total > m.merchant.BaseCnt+m.merchant.RenewCount*m.merchant.RenewAddCnt {

		db.Instance().Get().Transaction(func(tx *gorm.DB) error {
			m.merchant.RenewCount += 1
			err := services.Merchant.SaveWithDb(tx, m.merchant)
			if err != nil {
				return err
			}
			if _, err := m.api.Renew(sole.ActionReq{UserName: m.account.UserName,
				Password: m.account.Password,
			}); err != nil {
				return err
			} else {
				return nil
			}
			return nil
		})

	}
	return false
}
