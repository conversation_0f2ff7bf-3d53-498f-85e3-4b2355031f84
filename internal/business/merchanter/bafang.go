package merchanter

import (
	"encoding/base64"
	"errors"
	"fmt"
	"git.paihang8.com/lib/goutils"
	"git.paihang8.com/lib/goutils/request"
	"git.paihang8.com/lib/goutils/sites/b2b168"
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/cat"
	"gitlab.com/all_publish/api/pkg/convert"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"regexp"
	"strconv"
	"strings"
)

func init() {
	bafang := NewBafangMerchanter()
	models.RegisterMerchant(bafang.PlatForm(), bafang)
}

type BafangMerchant struct {
	name     string
	platform models.PlatForm
	account  merchants.BafangAccount
	api      b2b168.PublishApi
	merchant *models.Merchant
}

func NewBafangMerchanter() models.Merchanter {
	return &BafangMerchant{
		name:     "八方资源",
		platform: models.PlatformBafang,
		api:      b2b168.B2B168Publisher,
	}
}

func (m *BafangMerchant) GetApi() interface{} {
	return m.api
}
func (m *BafangMerchant) initMerchant(cid uint64) {
	if m.merchant == nil || m.merchant.CompanyID != cid {
		db.Instance().Get().Model(models.Merchant{}).Where("company_id=? and plat_form=?", cid, m.platform).First(&m.merchant)
	}
}

func (m *BafangMerchant) PublishInfo(info models.Info) (publisher.SimpleMessage, error) {
	var res publisher.SimpleMessage
	if !configs.ApiConfig.Web.IsProduction {
		return publisher.SimpleMessage{
			Url: "https://www.b2b168.com/",
			ID:  "0",
		}, nil
	}
	start := dbtypes.SHNow().UnixNano()
	services.NewInfoService().PatchExt(&info)
	var imagepaths []string
	if len(info.TitlePic) == 0 {
		info.TitlePic = info.Pic
	}
	for _, img := range info.TitlePic {
		if strings.Contains(img, "paihang8.com") {
			if dta, err := services.NewImageService().GetMappings(img, models.PlatformBafang); errors.Is(err, gorm.ErrRecordNotFound) {
				if v, e := request.HTTPGet(img); e == nil {
					data := base64.StdEncoding.EncodeToString(v)
					if data, e := m.api.UploadPhotoToAlbum(b2b168.PhotoReq{
						CommonParams: b2b168.CommonParams{CompanyId: m.account.Id},
						Value:        base64.StdEncoding.EncodeToString([]byte("title.jpeg")) + ":" + data,
					}); e == nil {
						id, _ := strconv.Atoi(data.ID)
						services.NewImageService().SetMappings(img, id, data.Url, models.PlatformBafang)
						parts := strings.Split(data.Url, "/")
						imagepaths = append(imagepaths, parts[len(parts)-1])
					} else {
						return publisher.SimpleMessage{}, fmt.Errorf("上传到八方资源失败:%w", e)
					}
				}
			} else if dta != nil && dta.TargetUrl != "" {
				parts := strings.Split(dta.TargetUrl, "/")
				imagepaths = append(imagepaths, parts[len(parts)-1])
			}
		}
	}
	var contentPic []string
	if len(info.TitlePic) > 0 {
		for _, img := range info.Pic {
			if strings.Contains(img, "paihang8.com") {
				if dta, err := services.NewImageService().GetMappings(img, models.PlatformBafang); errors.Is(err, gorm.ErrRecordNotFound) {
					if v, e := request.HTTPGet(img); e == nil {
						data := base64.StdEncoding.EncodeToString(v)
						if data, e := m.api.UploadPhotoToAlbum(b2b168.PhotoReq{
							CommonParams: b2b168.CommonParams{CompanyId: m.account.Id},
							Value:        base64.StdEncoding.EncodeToString([]byte("content.jpeg")) + ":" + data,
						}); e == nil {
							id, _ := strconv.Atoi(data.ID)
							services.NewImageService().SetMappings(img, id, data.Url, models.PlatformBafang)
							contentPic = append(contentPic, data.Url)
						} else {
							return publisher.SimpleMessage{}, fmt.Errorf("上传到八方资源失败:%w", e)
						}
					}
				} else if dta != nil && dta.TargetUrl != "" {
					contentPic = append(contentPic, dta.TargetUrl)
				}
			}
		}
	}
	var categoryId int
	if len(info.Product.Cate) > 0 {
		categoryId, _ = strconv.Atoi(info.Product.Cate[len(info.Product.Cate)-1])
	}
	if categoryId == 0 {
		categoryId = info.Company.GetCatID()
	}
	classmapping, err := cat.NewCatService().GetMapping(uint64(categoryId), models.PlatformBafang)
	if err != nil {
		return publisher.SimpleMessage{}, err
	}

	//插入图片HY-4227
	preg := regexp.MustCompile("<br/>")
	description := ""
	matched := preg.FindAllStringSubmatchIndex(info.Description, -1)

	if len(matched) > 0 {
		var pre = 0
		var text = ""
		for i, match := range matched {
			if i > len(contentPic)-1 {
				text = ""
			} else {
				text = `<br/><img src="` + contentPic[i] + `" />`
			}
			description += info.Description[pre:match[0]] + text + info.Description[match[0]:match[1]]
			pre = match[1]
		}
		description += info.Description[pre:]
		//填充多余的图片
		if len(matched) < len(contentPic) {
			leftPic := contentPic[len(matched):]
			for _, p := range leftPic {
				description += `<br/><img src="` + p + `" />`
			}
		}
	} else if len(contentPic) > 1 {
		description = `<img src="` + contentPic[1] + `" />` + info.Description
	} else {
		description = info.Description
	}
	areaId := info.Company.GetAreaID()
	var areamapping models.AreaMappings
	if areaId == 0 {
		areamapping, err = services.Area.RandCity(models.PlatformBafang, []models.AreaType{models.AreaTypeCountyOfDirectCity, models.AreaTypeCountyOfCity})
		if err != nil {
			return publisher.SimpleMessage{}, err
		}
	} else {
		areamapping, err = services.Area.GetMapping(uint64(areaId), models.PlatformBafang)
		if err != nil {
			return publisher.SimpleMessage{}, err
		}
	}

	msg := b2b168.SupplyReq{
		CommonParams: b2b168.CommonParams{
			CompanyId: m.account.Id,
		},
		Id:          0,
		Name:        *info.Title,
		Class:       convert.Str(classmapping.TargetCatID).MustInt(),
		Price:       info.Price,
		ProUnit:     info.Unit,
		Region:      convert.Str(areamapping.TargetAreaID).MustInt(),
		Tags:        strings.Join(info.Word, ","),
		MainDetails: description,
	}
	//if info.Product.MinOrder != nil {
	//	msg.ProNum = *info.Product.MinOrder
	//}
	if len(imagepaths) > 0 {
		msg.ImageFlag = imagepaths[0]
	}

	if len(imagepaths) > 1 {
		msg.ImageUrl2 = imagepaths[1]
	}

	if len(imagepaths) > 2 {
		msg.ImageUrl3 = imagepaths[2]
	}

	if info.Product != nil {
		if len(info.Word) < 4 {
			msg.Tags += "," + info.Product.Name
		}
	}
	if info.Product.Price != nil {
		msg.Price = *info.Product.Price
	}
	for k, v := range info.Product.Properties {
		if goutils.IsNumeric(k) {
			msg.MainDetails += fmt.Sprintf("%s:%v<br/>", cat.NewCatService().GetPropertyName(k), v)
		} else {
			msg.MainDetails += fmt.Sprintf("%s:%v<br/>", k, v)
		}
	}
	response, err := m.api.PostSupply(msg)
	if err != nil {
		return res, err
	}
	end := dbtypes.SHNow().UnixNano()
	log.Info("pubInfo", zap.Int64("cost ms", (end-start)/1e+6))
	id, _ := strconv.Atoi(response.ID)
	services.NewInfosService().Add(info.CompanyID, response.URL, uint64(id), models.PlatformBafang)
	return publisher.SimpleMessage{
		ID:  response.ID,
		Url: response.URL,
	}, nil
}

func (m *BafangMerchant) Name() string {
	return m.name
}

func (m *BafangMerchant) PlatForm() models.PlatForm {
	return m.platform
}

func (m *BafangMerchant) Clone() models.Merchanter {
	copyed := *m
	copyed.account = merchants.BafangAccount{}
	return &copyed
}

func (m *BafangMerchant) Meta() models.MerchantMeta {
	return models.MerchantMeta{
		Name:         m.name,
		Description:  "八方资源",
		PlatForm:     m.platform,
		PlatFormName: models.PlatFormName(m.platform),
		RequiredAccount: map[string]models.AccountItem{
			"username": {
				Type: "string",
				Desc: "用户名",
			},
			"password": {
				Type: "string",
				Desc: "密码",
			},
			"id": {
				Type: "string",
				Desc: "公司id",
			},
		},
		HomeUrl: "https://www.b2b168.com/",
		LogoUrl: "https://i.b2b168.com/PIC/indexpic/f_pic.png",
	}
}

func (m *BafangMerchant) Account() map[string]interface{} {
	return m.account.ToMap()
}

func (m *BafangMerchant) UpdateAccount(key string, value interface{}) {
}

func (m *BafangMerchant) LoginBy(account map[string]interface{}) (models.User, error) {
	m.account.Id = int(account["id"].(float64))
	return models.User{}, nil
}

func (m *BafangMerchant) IsErrorShouldStopAutoPub(err string) bool {
	if strings.Contains(err, "信息发布超过") {
		return true
	}
	if strings.Contains(err, "管理员禁止") {
		return true
	}
	return false
}

func (m *BafangMerchant) Reg(c models.Company) (interface{}, error) {
	u := fmt.Sprintf("u%s", c.Phone[0])
	pwd := goutils.Md5str(u)[0:8]
	if c.GetAreaID() == 0 {
		return nil, errors.New("公司地址没编辑，不能注册")
	}
	classmapping, err := cat.NewCatService().GetMapping(uint64(c.GetCatID()), models.PlatformBafang)
	if err != nil {
		return publisher.SimpleMessage{}, errors.New("找不到对应的分类，不能注册")
	}
	if c.GetCatID() == 0 {
		return nil, errors.New("公司分类没编辑，不能注册")
	}
	areamapping, err := services.Area.GetMapping(uint64(c.GetAreaID()), models.PlatformBafang)
	if err != nil {
		return publisher.SimpleMessage{}, errors.New("找不到对应的地区，不能注册")
	}
	p := b2b168.CompanyReq{
		CommonParams: b2b168.CommonParams{
			CompanyId: 0,
		},
		UserName:    u,
		Password:    pwd,
		Name:        c.Name,
		LinkMan:     c.ContactName,
		Mobile:      c.Phone[0],
		Class:       convert.Str(classmapping.TargetCatID).MustInt(),
		Region:      convert.Str(areamapping.TargetAreaID).MustInt(),
		Addr:        c.Address,
		MainProduct: c.MainProduct,
	}
	if data, err := m.api.ModifyCompany(p); err != nil {
		return nil, err
	} else {
		m.account.UserName = u
		m.account.Password = pwd
		m.account.Id = convert.Str(data.ID).MustInt()
		m.account.Url = data.URL
		return data, nil
	}
}

func (m *BafangMerchant) SubmitCert(c models.Company, ext ...interface{}) (interface{}, error) {
	if c.License == "" {
		return nil, errors.New("需要补全" + ext[0].(string))
	}
	if v, e := request.HTTPGet(c.License); e == nil {
		data := base64.StdEncoding.EncodeToString(v)
		req := b2b168.CertReq{
			CommonParams: b2b168.CommonParams{
				CompanyId: m.account.Id,
			},
			Id:           0,
			Name:         ext[0].(string),
			RegName:      c.Name,
			Type:         ext[1].(b2b168.CertType),
			RegNo:        c.RegNo,
			RegAddr:      c.RegAddr,
			Legal:        c.Legal,
			Business:     c.Business,
			ValidPeriod:  c.ValidPeriod,
			RegDate:      c.RegDate,
			RegMoney:     c.RegMoney,
			CorpType:     c.CorpType,
			RegAuthority: c.RegAuthority,
			File1:        base64.StdEncoding.EncodeToString([]byte("WechatIMG128.jpeg")) + ":" + data,
		}
		if data, err := m.api.SubmitCert(req); err != nil {
			return nil, err
		} else {

			return data, nil
		}
		return nil, nil
	} else {
		return nil, e
	}

}

func (m *BafangMerchant) GetCert(cid, id int) (interface{}, error) {
	return m.api.GetCert(cid, id)
}

func (m *BafangMerchant) RenewWhenNeeded(cid uint64) bool {
	m.initMerchant(cid)
	m.LoginBy(m.merchant.Account)
	shouldRenew := false
	if com, err := m.api.GetCompany(m.account.Id); err != nil {
		// 已过期，无法调用接口
		log.Error("bafang", err)
		if m.merchant.Total > m.merchant.BaseCnt+m.merchant.RenewCount*m.merchant.RenewAddCnt {
			shouldRenew = true
		}

	} else {
		if m.merchant.Total > convert.Str(com.InfoTotals).MustInt() {
			shouldRenew = true
		}
	}
	if shouldRenew {
		db.Instance().Get().Transaction(func(tx *gorm.DB) error {
			m.merchant.RenewCount += 1
			err := services.Merchant.SaveWithDb(tx, m.merchant)
			if err != nil {
				return err
			}
			if ok, err := m.api.Renew(m.account.Id); err != nil {
				return err
			} else if ok {
				return nil
			}
			return nil
		})
	}
	return false
}
