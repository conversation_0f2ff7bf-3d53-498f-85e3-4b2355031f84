package merchanter

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"mime/multipart"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"gitlab.com/all_publish/api/pkg/convert"

	"git.paihang8.com/lib/goutils/request"
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/cat"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

func init() {
	publishApi := NewHy88Merchanter()
	models.RegisterMerchant(publishApi.PlatForm(), publishApi)
}

type Hy88Merchant struct {
	name     string
	platform models.PlatForm
	account  merchants.Hy88Account
	api      publisher.PublishApi
}

func NewHy88Merchanter() models.Merchanter {
	return &Hy88Merchant{
		name:     "黄页88",
		platform: models.PlatformHy88,
		api:      publisher.HY88Publisher,
	}
}
func (m *Hy88Merchant) GetApi() interface{} {
	return m.api
}
func (m *Hy88Merchant) Clone() models.Merchanter {
	copyed := *m
	copyed.account = merchants.Hy88Account{}
	return &copyed
}

func (m *Hy88Merchant) Name() string {
	return m.name
}

func (m *Hy88Merchant) PlatForm() models.PlatForm {
	return m.platform
}

func (m *Hy88Merchant) Account() map[string]interface{} {
	return m.account.ToMap()
}

func (m *Hy88Merchant) UpdateAccount(key string, value interface{}) {
	switch key {
	case "id":
		m.account.Id = value.(string)
	}
}

func (m *Hy88Merchant) IsErrorShouldStopAutoPub(err string) bool {
	//您已发完0条金牌信息
	//您已发完1200条信息总量
	//该用户已经过期，请联系客服。
	if strings.HasPrefix(err, "您已发完") {
		return true
	}
	if strings.HasPrefix(err, "该用户已经过期") {
		return true
	}
	if strings.HasPrefix(err, "该VIP已经过期") {
		return true
	}
	if strings.Contains(err, "密码不正确") {
		return true
	}
	if strings.Contains(err, "账户被封禁") {
		return true
	}

	if strings.Contains(err, "手机号码不存在") {
		return true
	}

	if strings.Contains(err, "没有通过认证") {
		return true
	}

	//if strings.Contains(err, "最大图片数") {
	//	return true
	//}
	return false
}

func (m *Hy88Merchant) Meta() models.MerchantMeta {
	return models.MerchantMeta{
		Name:         m.name,
		Description:  "黄页88网",
		PlatForm:     m.platform,
		PlatFormName: models.PlatFormName(m.platform),
		RequiredAccount: map[string]models.AccountItem{
			"mobile": {
				Type: "string",
				Desc: "手机号",
			},
			"password": {
				Type: "string",
				Desc: "密码",
			},
			"id": {
				Type: "string",
				Desc: "用户id",
			},
		},
		HomeUrl: "http://www.huangye88.com/",
		LogoUrl: "http://static.huangye88.cn/images/new/logoi.png",
	}
}

func (m *Hy88Merchant) LoginBy(account map[string]interface{}) (models.User, error) {
	return m.Login(account["mobile"].(string), account["password"].(string))
}

func (m *Hy88Merchant) VerifyAccount(account map[string]interface{}) bool {
	if mobile, ok := account["mobile"]; !ok {
		return false
	} else if _, ok = mobile.(string); !ok {
		return false
	}
	if password, ok := account["password"]; !ok {
		return false
	} else if _, ok = password.(string); !ok {
		return false
	}
	return true
}

func (m *Hy88Merchant) Login(username, password string) (models.User, error) {
	username = strings.TrimSpace(username)
	m.account.Mobile = username
	m.account.Password = password

	var user models.User
	mobile, _ := strconv.Atoi(username)
	u, err := m.api.FaFaLogin(mobile, password)
	if err != nil {
		return user, err
	}
	user.Username = u.User.UserName
	user.FromKuyiso = u.User.UserFrom == "channel_10006"
	user.Email = u.User.UserEmail
	user.Phone = u.User.UserMobile
	user.OemId = u.User.ID
	user.CompanyID = uint64(u.Company.Cid)
	user.DigKeywordLimit = int(u.DigCount.Keywords.IntPart())
	user.DigMaterialsLimit = int(u.DigCount.Material.IntPart())
	m.account.Id = strconv.Itoa(user.OemId)
	m.account.Token = u.Token
	user.DialyItems = u.Dialyitems
	user.TotalItems = u.Totalitems
	expireTime := time.Unix(int64(u.ExpireTime), 0)
	user.ExpireTime = &expireTime
	postExpireTime, _ := publisher.String2Time(u.Permission.Post)
	user.PostExpireTime = &postExpireTime
	rankExpireTime, _ := publisher.String2Time(u.Permission.Rank)
	user.RankExpireTime = &rankExpireTime
	seekExpireTime, _ := publisher.String2Time(u.Permission.Seek)
	user.SeekExpireTime = &seekExpireTime
	permissionUpdateTime, _ := publisher.String2Time(u.Permission.UpdateTime)
	if postExpireTime.Sub(permissionUpdateTime) > time.Hour*24*180 {
		user.PostBuyTime = &permissionUpdateTime
	}
	if rankExpireTime.Sub(permissionUpdateTime) > time.Hour*24*180 {
		user.RankBuyTime = &permissionUpdateTime
	}
	if rankExpireTime.Sub(permissionUpdateTime) > time.Hour*24*180 {
		user.SeekBuyTime = &permissionUpdateTime
	}
	user.PostsMap = u.Permission.PostsMap
	found := false
	if user.PostsMap == nil {
		user.PostsMap = make(map[string]publisher.PlatformConfig)
	}
	for k, _ := range user.PostsMap {
		if k == "huangye88.com" {
			found = true
			break
		}
	}
	if !found {
		user.PostsMap["huangye88.com"] = publisher.PlatformConfig{
			Name:        "黄页88",
			Expiredtime: u.Permission.Post,
			MaxItem:     0,
			BaseItem:    0,
		}
	}

	return user, nil
}

func (m *Hy88Merchant) authorize() (string, error) {
	return m.api.Authorize(configs.ApiConfig.Hy88.CHNID, configs.ApiConfig.Hy88.CHNUser, configs.ApiConfig.Hy88.CHNPassword)
}

func (m *Hy88Merchant) GetCompanyInfo(uid string) (models.Company, error) {
	var company models.Company
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return company, err
		}
	}
	c, err := m.api.GetCompanyInfo(convert.Str(uid).MustInt())
	if err != nil {
		return company, err
	}
	merchantCompany := c
	company.UpdatedAt = dbtypes.SHNow()
	company.CreatedAt = dbtypes.SHNow()
	company.Name = merchantCompany.Coname
	company.ID = uint64(c.Cid)
	company.Cate = []string{}
	company.Address = merchantCompany.Address
	company.Site = merchantCompany.Companyurl
	company.Logo = merchantCompany.Weblogo
	company.Introduce = merchantCompany.Description
	company.ContactName = merchantCompany.Realname
	company.AreaIds = append(company.AreaIds, strconv.FormatInt(int64(merchantCompany.Province), 10), strconv.FormatInt(int64(merchantCompany.City), 10), strconv.FormatInt(int64(merchantCompany.District), 10))
	company.Phone = append(company.Phone, merchantCompany.ConMobile, merchantCompany.ConPhone, merchantCompany.Phone)
	company.Qq = &merchantCompany.ConQq
	return company, nil
}
func (m *Hy88Merchant) GetPersonInfo(uid string) (*publisher.Person, error) {
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return nil, err
		}
	}
	id, _ := strconv.Atoi(uid)
	return m.api.GetPersonInfoByUid(id)
}

func (m *Hy88Merchant) CreateAlbum(album schemas.Album) (schemas.AlbumResponse, error) {
	var response schemas.AlbumResponse
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return response, err
		}
	}
	hyAlbum, err := m.api.CreateAlbum(publisher.Album{
		UID:         dbtypes.MustInt(m.account.Id),
		Name:        album.Name,
		Description: album.Description,
		Isopen:      dbtypes.MustInt(album.IsOpen),
	})
	if err != nil {
		return response, err
	}
	response.Albumid = strconv.FormatInt(int64(hyAlbum.ID), 10)
	return response, nil
}

func (m *Hy88Merchant) ModifyAlbum(albumId string, album schemas.Album) error {
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return err
		}
	}
	err := m.api.ModifyAlbum(publisher.Album{
		UID:         convert.Str(m.account.Id).MustInt(),
		ID:          convert.Str(albumId).MustInt(),
		Name:        album.Name,
		Description: album.Description,
		Isopen:      convert.Str(album.IsOpen).MustInt(),
	})
	if err != nil {
		return err
	}
	return nil
}

func (m *Hy88Merchant) GetAlbums() ([]schemas.AlbumItem, error) {
	var albums []schemas.AlbumItem
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return albums, err
		}
	}
	if tmp, err := m.api.GetAlbums(convert.Str(m.account.Id).MustInt()); err != nil {
		return albums, err
	} else {
		for _, item := range tmp {
			albums = append(albums, schemas.AlbumItem(item))
		}
		return albums, err
	}
}
func (m *Hy88Merchant) GetImagesOfAlbum(albumId string, limit, offset int) ([]string, error) {
	var imgs []string
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return imgs, err
		}
	}
	imgMap, err := m.api.GetImagesOfAlbum(convert.Str(m.account.Id).MustInt(), convert.Str(albumId).MustInt(), limit, offset)
	if err != nil {
		return imgs, err
	}
	for _, item := range imgMap {
		imgs = append(imgs, item.URL)
	}
	return imgs, nil
}

func (m *Hy88Merchant) UploadImagesToAlbum(albumId string, fileHeaders []*multipart.FileHeader) ([]string, error) {
	var urls []string
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return urls, err
		}
	}
	var postImages []publisher.PostImage
	for _, header := range fileHeaders {
		f, _ := header.Open()
		data, _ := ioutil.ReadAll(f)
		postImages = append(postImages, publisher.PostImage{
			Data: publisher.Base64EncodedImage(base64.StdEncoding.EncodeToString(data)),
			Tag:  header.Filename,
		})
	}
	if imageItems, err := m.api.UploadImgToUserAlbum(convert.Str(m.account.Id).MustInt(), convert.Str(albumId).MustInt(), postImages); err != nil {
		return urls, err
	} else {
		for _, item := range imageItems {
			urls = append(urls, item.URL)
		}
		return urls, nil
	}
}

func (m *Hy88Merchant) toDomainPath(urlstr string) string {
	u, _ := url.Parse(urlstr)
	domain := strings.Split(u.Host, ".")[0]
	return fmt.Sprintf("%s|%s", domain, strings.Replace(u.Path, "/", "", 1))
}

// 处理成黄页88的链接
func (m *Hy88Merchant) fixUrl(urls []string, onlyPath bool) ([]string, error) {
	host := ".huangye88.net"
	var imagepaths []string
	for _, img := range urls {
		u, _ := url.Parse(img)
		if strings.Contains(u.Host, host) {
			if onlyPath {
				imagepaths = append(imagepaths, m.toDomainPath(img))
			} else {
				imagepaths = append(imagepaths, img)
			}
		} else if strings.Contains(img, "paihang8.com") {
			if dta, err := services.NewImageService().GetMappings(img, models.PlatformHy88); errors.Is(err, gorm.ErrRecordNotFound) {
				if v, e := request.HTTPGet(img); e == nil {
					var postImages []publisher.PostImage
					postImages = append(postImages, publisher.PostImage{
						Data: publisher.Base64EncodedImage(base64.StdEncoding.EncodeToString(v)),
						Tag:  "發發助手",
					})
					if imageItems, err := m.api.UploadImgToUserAlbum(convert.Str(m.account.Id).MustInt(), 0, postImages); err != nil {
						return imagepaths, err
					} else {
						if len(imageItems) > 0 {
							id, _ := strconv.Atoi(imageItems[0].ID)
							services.NewImageService().SetMappings(img, id, imageItems[0].URL, models.PlatformHy88)
							if onlyPath {
								imagepaths = append(imagepaths, m.toDomainPath(imageItems[0].URL))
							} else {
								imagepaths = append(imagepaths, imageItems[0].URL)
							}
						}
					}
				}
			} else if dta != nil && dta.TargetUrl != "" {
				if onlyPath {
					imagepaths = append(imagepaths, m.toDomainPath(dta.TargetUrl))
				} else {
					imagepaths = append(imagepaths, dta.TargetUrl)
				}
			}
		}
	}
	return imagepaths, nil
}

func (m *Hy88Merchant) PublishInfo(info models.Info) (publisher.SimpleMessage, error) {

	var res publisher.SimpleMessage
	if !configs.ApiConfig.Web.IsProduction {
		return publisher.SimpleMessage{
			Url: "http://www.huangye88.com",
			ID:  "0",
		}, nil
	}
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return res, err
		}
	}
	services.NewInfoService().PatchExt(&info)
	if res_url, ok := info.PubRes["res_url"]; ok {
		res := res_url.(map[string]interface{})
		if messageId, ok := res["messageid"]; ok {
			id, _ := strconv.Atoi(messageId.(string))
			if detail, err := m.api.GetMessage(id); err == nil {
				return m.fixInfo(detail, info)
			} else {
				fmt.Println(err)
			}
		}
	}
	start := dbtypes.SHNow().UnixNano()
	var phone, mobile string
	if len(info.Company.Phone) > 0 {
		mobile = info.Company.Phone[0]
	}
	if len(info.Company.Phone) > 1 {
		phone = info.Company.Phone[1]
	}
	var titlepic []string
	var contentpic []string
	var err error
	if len(info.TitlePic) == 0 {
		info.TitlePic = info.Pic
	}
	titlepic, err = m.fixUrl(info.TitlePic, true)
	if err != nil {
		return publisher.SimpleMessage{}, fmt.Errorf("上传到黄页失败：%w", err)
	}
	contentpic, err = m.fixUrl(info.Pic, false)
	if err != nil {
		return publisher.SimpleMessage{}, fmt.Errorf("上传到黄页失败：%w", err)
	}
	var categoryId = info.Product.GetCatID()
	if categoryId == 0 {
		categoryId = info.Company.GetCatID()
	}

	//插入图片HY-4227
	preg := regexp.MustCompile("<br/>")
	description := ""
	matched := preg.FindAllStringSubmatchIndex(info.Description, -1)

	if len(matched) > 0 {
		var pre = 0
		var text = ""
		for i, match := range matched {
			if i > len(contentpic)-1 {
				text = ""
			} else {
				text = `<br/><img src="` + contentpic[i] + `@4e_1c_750w_750h_90Q" />`
			}
			description += info.Description[pre:match[0]] + text + info.Description[match[0]:match[1]]
			pre = match[1]
		}
		description += info.Description[pre:]
		//填充多余的图片
		if len(matched) < len(contentpic) {
			leftPic := contentpic[len(matched):]
			for _, p := range leftPic {
				description += `<br/><img src="` + p + `@4e_1c_750w_750h_90Q" />`
			}
		}
	} else if len(contentpic) > 1 {
		description = `<img src="` + contentpic[1] + `@4e_1c_750w_750h_90Q" />` + info.Description
	} else {
		description = info.Description
	}
	areaId := "0"
	if len(info.Product.AreaIds) > 0 {
		for i := len(info.Product.AreaIds) - 1; i >= 0; i-- {
			if info.Product.AreaIds[i] != "0" {
				areaId = info.Product.AreaIds[i]
				break
			}
		}
	}
	msg := publisher.PostMessage{
		Uid:        m.account.Id,
		Categoryid: strconv.Itoa(categoryId),
		Areaid:     areaId,
		Subject:    *info.Title,
		Phone:      phone,
		Mobile:     mobile,
		Tags:       info.Word,
		Content:    "<p>" + description + "</p>",
		ImagesPath: titlepic,
		Video:      info.Product.RandVideo(),
	}
	var customerProperties map[string]interface{}
	msg.Properties, customerProperties = info.Product.SplitHy88Properties()
	customerPropertiesContent := ""
	for k, v := range customerProperties {
		customerPropertiesContent += fmt.Sprintf(`<span class="ssx01">%s</span><span class="ssx02">%s</span>`, k, v)
	}
	if len(customerPropertiesContent) > 0 {
		msg.Content = "<p>" + `<p class="fa_shuxing">` + customerPropertiesContent + "</p>" + description + "</p>"
	} else {
		msg.Content = "<p>" + description + "</p>"
	}

	if info.Product != nil {
		if len(info.Word) < 4 {
			msg.Tags = append(msg.Tags, info.Product.Name)
		}
		msg.Productname = info.Product.Name
		if len(info.Product.Alias) > 0 {
			alias := strings.Join(info.Product.Alias, ",")
			msg.Productname += "," + alias
		}
		//msg.Brandname = info.Product.Brand, 这个字段已经废弃。 品牌已经放入properties里管理
	}
	if info.Product.Price != nil {
		msg.Price = fmt.Sprintf("%0.2f", *info.Product.Price)
	}

	response, err := m.api.PostMessage(msg)
	if err != nil {
		return res, err
	}
	end := dbtypes.SHNow().UnixNano()
	log.Info("pubInfo", zap.Int64("cost ms", (end-start)/1e+6))
	services.NewInfosService().Add(info.CompanyID, response.Url, convert.Str(response.ID).MustUInt64(), models.PlatformHy88)
	return response, nil
}

func (m *Hy88Merchant) PubInfo(field schemas.InfoFields, com models.Company) (publisher.SimpleMessage, error) {
	var res publisher.SimpleMessage
	if !configs.ApiConfig.Web.IsProduction {
		return publisher.SimpleMessage{
			Url: "http://www.huangye88.com",
			ID:  "0",
		}, nil
	}
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return res, err
		}
	}
	start := dbtypes.SHNow().UnixNano()
	var phone, mobile string
	if len(com.Phone) > 0 {
		mobile = com.Phone[0]
	}
	if len(com.Phone) > 1 {
		phone = com.Phone[1]
	}
	var titlepic []string
	var err error
	titlepic, err = m.fixUrl(field.TitlePic, true)
	if err != nil {
		return publisher.SimpleMessage{}, fmt.Errorf("上传到黄页失败：%w", err)
	}
	var categoryId = field.Cate.GetCatID()
	if categoryId == 0 {
		categoryId = com.GetCatID()
	}

	areaId := "0"
	if len(field.AreaIds) > 0 {
		for i := len(field.AreaIds) - 1; i >= 0; i-- {
			if field.AreaIds[i] != "0" {
				areaId = field.AreaIds[i]
				break
			}
		}
	}
	msg := publisher.PostMessage{
		Uid:        m.account.Id,
		Categoryid: strconv.Itoa(categoryId),
		Areaid:     areaId,
		Subject:    field.Title,
		Phone:      phone,
		Mobile:     mobile,
		Tags:       field.Word,
		Content:    "<p>" + field.Description + "</p>",
		ImagesPath: titlepic,
	}
	msg.Price = fmt.Sprintf("%0.2f", field.Price)
	msg.Properties = field.GetHy88Properties()
	response, err := m.api.PostMessage(msg)
	if err != nil {
		return res, err
	}
	end := dbtypes.SHNow().UnixNano()
	log.Info("pubInfo", zap.Int64("cost ms", (end-start)/1e+6))
	services.NewInfosService().Add(com.ID, response.Url, convert.Str(response.ID).MustUInt64(), models.PlatformHy88)
	return response, nil
}

func (m *Hy88Merchant) fixInfo(info publisher.MessageDetail, myinfo models.Info) (publisher.SimpleMessage, error) {
	println("go to fix:", info.URL.Pc)
	if info.Isshow != "1" {
		println("isshow!=1, ignore")
		return publisher.SimpleMessage{}, nil
	}

	var res publisher.SimpleMessage
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return res, err
		}
	}
	start := dbtypes.SHNow().UnixNano()
	areaId := info.District
	if areaId == "0" {
		areaId = info.City
	}
	if areaId == "0" {
		areaId = info.Province
	}
	msg := publisher.PatchMessage{
		ID:          info.ID,
		Uid:         info.UID,
		Categoryid:  info.CatID,
		Areaid:      info.District,
		Subject:     info.Subject,
		Content:     info.Content,
		Price:       info.Price,
		Productname: info.Productname,
	}
	if imagepath, err := m.fixUrl(myinfo.TitlePic, true); err == nil {
		msg.ImagesPath = imagepath
		ok := false
		for _, img := range imagepath {
			if strings.Contains(img, "jmage") {
				ok = true
				break
			}
		}
		if !ok {
			println("old img, ignore")
			return publisher.SimpleMessage{}, nil
		}
	}
	msg.Properties = info.Properties
	response, err := m.api.PatchMessage(msg)
	if err != nil {
		return res, err
	}
	end := dbtypes.SHNow().UnixNano()
	println("fixed url ", info.URL.Pc)
	log.Info("modifyInfo", zap.Int64("cost ms", (end-start)/1e+6))
	return response, nil
}

func (m *Hy88Merchant) PublishProduct(product models.CompanyProduct) (*publisher.SimpleProduct, error) {
	if !configs.ApiConfig.Web.IsProduction {
		return &publisher.SimpleProduct{"1", "http://www.huangye88.com"}, nil
	}
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return nil, err
		}
	}
	var contentpic []string
	var err error
	contentpic, err = m.fixUrl(product.Pic, false)
	if err != nil {
		return nil, fmt.Errorf("上传到黄页失败：%w", err)
	}
	titlepic, err := m.fixUrl(product.Pic, true)

	attributes, _ := json.Marshal(map[string]string{"品牌": product.Product.Brand})
	//插入图片HY-4409
	preg := regexp.MustCompile("<br/>")
	description := ""
	matched := preg.FindAllStringSubmatchIndex(product.Content, -1)

	if len(matched) > 0 {
		var pre = 0
		var text = ""
		for i, match := range matched {
			if (i+1) >= len(contentpic) || i > 4 {
				text = ""
			} else {
				text = `<br/><img src="` + contentpic[i+1] + `@4e_1c_750w_750h_90Q" />`
			}
			description += product.Content[pre:match[0]] + text + product.Content[match[0]:match[1]]
			pre = match[1]
		}
		description += product.Content[pre:]
	} else if len(contentpic) > 1 {
		description = `<img src="` + contentpic[1] + `@4e_1c_750w_750h_90Q" />` + product.Content
	} else {
		description = product.Content
	}

	companyProduct := publisher.PostProduct{
		Uid:        m.account.Id,
		Subject:    product.Subject,
		Content:    "<p>" + description + "</p>",
		Unit:       product.Unit,
		ImagesPath: titlepic,
		Attributes: string(attributes),
	}
	if product.Price > 0 {
		companyProduct.Price = product.Price
	}
	response, err := m.api.PostProduct(companyProduct)
	if err != nil {
		return nil, err
	}
	return &response, nil
}

func (m *Hy88Merchant) GetCatByID(id int) (detail publisher.CategoryDetail, err error) {
	if !m.api.Authorized() {
		if _, err1 := m.authorize(); err1 != nil {
			err = err1
			return
		}
	}
	return m.api.GetCatByID(id)
}

func (m *Hy88Merchant) GetAllCatsByPID(pid int) ([]publisher.Category, error) {
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return nil, err
		}
	}
	return m.api.GetAllCatsByPID(pid)
}

func (m *Hy88Merchant) GetSeekCount() (*publisher.SeekCountData, error) {
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return nil, err
		}
	}
	return m.api.GetSeekCount(convert.Str(m.account.Id).MustInt())
}

func (m *Hy88Merchant) GetRanks(eg int, page int) (*publisher.RanksData, error) {
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return nil, err
		}
	}
	return m.api.GetRanks(convert.Str(m.account.Id).MustInt(), eg, page)
}

func (m *Hy88Merchant) GetSeeks(eg string, page int) (*publisher.SeeksData, error) {
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return nil, err
		}
	}
	return m.api.GetSeeks(convert.Str(m.account.Id).MustInt(), eg, page)
}

func (m *Hy88Merchant) GetVideos(limit, offset int) ([]publisher.GetVideoResponseData, error) {
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return nil, err
		}
	}
	return m.api.GetVideos(convert.Str(m.account.Id).MustInt(), limit, offset)
}

func (m *Hy88Merchant) GetAreasByPid(pid int) ([]publisher.Area, error) {
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return nil, err
		}
	}
	return m.api.GetAllAreasByPid(pid)
}

func (m *Hy88Merchant) GetCatProperties(cid int) ([]publisher.Property, error) {
	if !m.api.Authorized() {
		if _, err := m.authorize(); err != nil {
			return nil, err
		}
	}
	return m.api.GetCatProperties(cid)
}

func IsLastCategory(c models.Company) bool {
	if subs, err := cat.NewCatService().FindSubsByPid(uint64(c.GetCatID()), 0, 1); err == nil {
		if len(subs) == 0 {
			return true
		}
		return false
	}
	return true
}
func (m *Hy88Merchant) Reg(p models.Company) (interface{}, error) {
	return nil, nil
}

func (m *Hy88Merchant) SubmitCert(c models.Company, ext ...interface{}) (interface{}, error) {
	return nil, nil
}

func (m *Hy88Merchant) GetCert(cid, id int) (interface{}, error) {
	return nil, nil
}

func (m *Hy88Merchant) IsErrorMsgRunOut(err string) bool {
	if strings.Contains(err, "您已发完") && !strings.Contains(err, "今天") {
		return true
	}
	return false
}

func (m *Hy88Merchant) RenewWhenNeeded(cid uint64) bool {
	return false
}
