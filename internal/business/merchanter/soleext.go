package merchanter

import (
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"git.paihang8.com/lib/goutils/sites/sole"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/db"
	"gorm.io/gorm"
)

func init() {
	item := NewSoleExtMerchanter()
	models.RegisterMerchant(item.PlatForm(), item)
}

type SoleExtMerchant struct {
	name     string
	platform models.PlatForm
	account  merchants.AicaigouAccount
	api      sole.PublishApi
	SoleMerchant
}

func NewSoleExtMerchanter() models.Merchanter {
	client, _ := models.GetMerchant(models.PlatformSole)
	return &SoleExtMerchant{
		name:         "搜了网+爱采购",
		platform:     models.PlatformSoleExt,
		api:          sole.DefaultPublisher,
		SoleMerchant: *client.(*SoleMerchant),
	}
}
func (m *SoleExtMerchant) GetApi() interface{} {
	return m.api
}

func (m *SoleExtMerchant) PublishInfo(info models.Info) (publisher.SimpleMessage, error) {
	return m.SoleMerchant.PublishInfo(info)
}

func (m *SoleExtMerchant) Name() string {
	return m.name
}

func (m *SoleExtMerchant) PlatForm() models.PlatForm {
	return m.platform
}

func (m *SoleExtMerchant) Clone() models.Merchanter {
	copyed := *m
	client, _ := models.GetMerchant(models.PlatformSole)
	copyed.SoleMerchant = *client.(*SoleMerchant)
	return &copyed
}

func (m *SoleExtMerchant) Meta() models.MerchantMeta {
	return models.MerchantMeta{
		Name:         m.name,
		Description:  "搜了网+爱采购",
		PlatForm:     m.platform,
		PlatFormName: models.PlatFormName(m.platform),
		RequiredAccount: map[string]models.AccountItem{
			"username": {
				Type: "string",
				Desc: "用户名",
			},
			"password": {
				Type: "string",
				Desc: "密码",
			},
			"id": {
				Type: "string",
				Desc: "公司id",
			},
		},
		HomeUrl: "https://cn.china.cn/",
		LogoUrl: "https://himg.china.cn/img/common/logo/200x44.png",
	}
}

func (m *SoleExtMerchant) Account() map[string]interface{} {
	return m.SoleMerchant.Account()
}

func (m *SoleExtMerchant) UpdateAccount(key string, value interface{}) {
	m.SoleMerchant.UpdateAccount(key, value)
}

func (m *SoleExtMerchant) LoginBy(account map[string]interface{}) (models.User, error) {
	return m.SoleMerchant.LoginBy(account)
}

func (m *SoleExtMerchant) IsErrorShouldStopAutoPub(err string) bool {
	return m.SoleMerchant.IsErrorShouldStopAutoPub(err)
}

func (m *SoleExtMerchant) Reg(c models.Company) (interface{}, error) {
	return m.SoleMerchant.Reg(c)
}

func (m *SoleExtMerchant) SubmitCert(c models.Company, ext ...interface{}) (interface{}, error) {
	item, _ := services.NewAicaigouUser(db.Instance().Get()).Find(c.ID)
	err := m.api.UploadContract(sole.UploadContractReq{
		ActionReq: sole.ActionReq{
			UserName: m.SoleMerchant.account.UserName,
			Password: m.SoleMerchant.account.Password,
		},
		ContractBeginDate: item.ContractBeginDate,
		ContractEndDate:   item.ContractEndDate,
		ContractFile:      item.ContractFile,
	})
	return nil, err
}

func (m *SoleExtMerchant) GetCert(cid, id int) (interface{}, error) {
	return m.api.GetContractStatus(sole.ActionReq{
		UserName: m.SoleMerchant.account.UserName,
		Password: m.SoleMerchant.account.Password,
	})
}

func (m *SoleExtMerchant) initMerchant(cid uint64) {
	if m.merchant == nil || m.merchant.CompanyID != cid {
		db.Instance().Get().Model(models.Merchant{}).Where("company_id=? and plat_form=?", cid, m.platform).First(&m.merchant)
	}
}

func (m *SoleExtMerchant) RenewWhenNeeded(cid uint64) bool {
	m.initMerchant(cid)
	m.LoginBy(m.merchant.Account)
	if m.merchant.Total > m.merchant.BaseCnt+m.merchant.RenewCount*m.merchant.RenewAddCnt {

		db.Instance().Get().Transaction(func(tx *gorm.DB) error {
			m.merchant.RenewCount += 1
			err := services.Merchant.SaveWithDb(tx, m.merchant)
			if err != nil {
				return err
			}
			if _, err := m.api.Renew(sole.ActionReq{UserName: m.SoleMerchant.account.UserName,
				Password: m.SoleMerchant.account.Password,
			}); err != nil {
				return err
			} else {
				return nil
			}
			return nil
		})

	}
	return false
}
