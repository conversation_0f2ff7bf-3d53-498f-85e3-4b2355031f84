package merchanter

import (
	"encoding/base64"
	"errors"
	"fmt"
	"git.paihang8.com/lib/goutils"
	"git.paihang8.com/lib/goutils/request"
	"git.paihang8.com/lib/goutils/sites/china"
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/cat"
	"gitlab.com/all_publish/api/pkg/convert"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"regexp"
	"strconv"
	"strings"
)

func init() {
	item := NewChinaMerchanter()
	models.RegisterMerchant(item.PlatForm(), item)
}

type ChinaMerchant struct {
	name     string
	platform models.PlatForm
	account  merchants.ChinaAccount
	api      china.PublishApi
	merchant *models.Merchant
}

func NewChinaMerchanter() models.Merchanter {
	return &ChinaMerchant{
		name:     "中国供应商",
		platform: models.PlatformChina,
		api:      china.ChinaPublisher,
	}
}
func (m *ChinaMerchant) GetApi() interface{} {
	return m.api
}

func (m *ChinaMerchant) initMerchant(cid uint64) {
	if m.merchant == nil || m.merchant.CompanyID != cid {
		db.Instance().Get().Model(models.Merchant{}).Where("company_id=? and plat_form=?", cid, m.platform).First(&m.merchant)
	}
}

func (m *ChinaMerchant) PublishInfo(info models.Info) (publisher.SimpleMessage, error) {
	var res publisher.SimpleMessage
	if !configs.ApiConfig.Web.IsProduction {
		return publisher.SimpleMessage{
			Url: "https://cn.china.cn/",
			ID:  "0",
		}, nil
	}
	start := dbtypes.SHNow().UnixNano()
	services.NewInfoService().PatchExt(&info)
	var imagedata []string
	if len(info.TitlePic) == 0 {
		info.TitlePic = info.Pic
	}
	for _, img := range info.TitlePic {
		//if strings.Contains(img, "paihang8.com") {
		//	if dta, err := services.NewImageService().GetMappings(img, models.PlatformChina); errors.Is(err, gorm.ErrRecordNotFound) {
		if v, e := request.HTTPGet(img); e == nil {
			data := base64.StdEncoding.EncodeToString(v)
			imagedata = append(imagedata, data)
			//if data, e := m.api.UploadPhotoToAlbum(b2b168.PhotoReq{
			//	CommonParams: b2b168.CommonParams{CompanyId: m.account["id"].(int)},
			//	Value:        base64.StdEncoding.EncodeToString([]byte("title.jpeg")) + ":" + data,
			//}); e == nil {
			//	id, _ := strconv.Atoi(data.ID)
			//	services.NewImageService().SetMappings(img, id, data.Url, models.PlatformChina)
			//	parts := strings.Split(data.Url, "/")
			//	imagepaths = append(imagepaths, parts[len(parts)-1])
			//} else {
			//	return publisher.SimpleMessage{}, fmt.Errorf("上传到八方资源失败:%w", e)
			//}
		}
		//	} else if dta != nil && dta.TargetUrl != "" {
		//		parts := strings.Split(dta.TargetUrl, "/")
		//		imagepaths = append(imagepaths, parts[len(parts)-1])
		//	}
		//}
	}

	var categoryId int
	if len(info.Product.Cate) > 0 {
		categoryId, _ = strconv.Atoi(info.Product.Cate[len(info.Product.Cate)-1])
	}
	if categoryId == 0 {
		categoryId = info.Company.GetCatID()
	}
	classmapping, err := cat.NewCatService().GetMapping(uint64(categoryId), models.PlatformChina)
	if err != nil {
		return publisher.SimpleMessage{}, err
	}

	//插入图片HY-4227
	preg := regexp.MustCompile("<br/>")
	description := ""
	matched := preg.FindAllStringSubmatchIndex(info.Description, -1)

	if len(matched) > 0 {
		var pre = 0
		var text = ""
		for i, match := range matched {
			if i > len(info.Pic)-1 {
				text = ""
			} else {
				text = `<br/><img src="` + info.Pic[i] + `" />`
			}
			description += info.Description[pre:match[0]] + text + info.Description[match[0]:match[1]]
			pre = match[1]
		}
		description += info.Description[pre:]
		//填充多余的图片
		if len(matched) < len(info.Pic) {
			leftPic := info.Pic[len(matched):]
			for _, p := range leftPic {
				description += `<br/><img src="` + p + `" />`
			}
		}
	} else if len(info.Pic) > 1 {
		description = `<img src="` + info.Pic[1] + `" />` + info.Description
	} else {
		description = info.Description
	}
	//不需要。自动使用注册时的地区
	//areaId := 0
	//if len(info.Product.AreaIds) > 0 {
	//	for i := len(info.Product.AreaIds) - 1; i >= 0; i-- {
	//		if info.Product.AreaIds[i] != "0" {
	//			areaId, _ = strconv.Atoi(info.Product.AreaIds[i])
	//			break
	//		}
	//	}
	//}
	//
	//var areamapping models.AreaMappings
	//if areaId == 0 {
	//	areamapping, err = area.NewAreaService().RandCity(models.PlatformChina)
	//	if err != nil {
	//		return publisher.SimpleMessage{}, err
	//	}
	//} else {
	//	areamapping, err = area.NewAreaService().GetMapping(uint64(areaId), models.PlatformChina)
	//	if err != nil {
	//		return publisher.SimpleMessage{}, err
	//	}
	//}

	msg := china.PostProductReq{
		UserName:   m.account.UserName,
		Password:   m.account.Password,
		Categoryid: convert.Str(classmapping.TargetCatID).MustInt(),
		Caption:    *info.Title,
		Intro:      description,
		ProductOpt: china.ProductOpt{}.WithKeywords(info.Word),
	}
	if len(imagedata) > 3 {
		imagedata = imagedata[:3]
	}
	if len(imagedata) > 0 {
		data := strings.Join(imagedata, ",")
		msg.Pics = &data
	}

	if len(info.Product.Properties) > 0 {
		var m = map[string]interface{}{}
		var cats = cat.NewCatService()
		for k, v := range info.Product.Properties {
			m[cats.GetPropertyName(k)] = v
		}
		msg.ProductOpt = msg.ProductOpt.WithProps(m)
	}
	if info.Product.Price != nil {
		msg.ProductOpt = msg.ProductOpt.WithPrice(*info.Product.Price)
	}

	response, err := m.api.PostProduct(msg)
	if err != nil {
		return res, err
	}
	end := dbtypes.SHNow().UnixNano()
	log.Info("pubInfo", zap.Int64("cost ms", (end-start)/1e+6))
	services.NewInfosService().Add(info.CompanyID, response.Sellurl, uint64(response.Sellid), models.PlatformChina)
	return publisher.SimpleMessage{
		ID:  strconv.FormatInt(response.Sellid, 10),
		Url: response.Sellurl,
	}, nil
}

func (m *ChinaMerchant) Name() string {
	return m.name
}

func (m *ChinaMerchant) PlatForm() models.PlatForm {
	return m.platform
}

func (m *ChinaMerchant) Clone() models.Merchanter {
	copyed := *m
	copyed.account = merchants.ChinaAccount{}
	return &copyed
}

func (m *ChinaMerchant) Meta() models.MerchantMeta {
	return models.MerchantMeta{
		Name:         m.name,
		Description:  "中国供应商",
		PlatForm:     m.platform,
		PlatFormName: models.PlatFormName(m.platform),
		RequiredAccount: map[string]models.AccountItem{
			"username": {
				Type: "string",
				Desc: "用户名",
			},
			"password": {
				Type: "string",
				Desc: "密码",
			},
			"id": {
				Type: "string",
				Desc: "公司id",
			},
		},
		HomeUrl: "https://cn.china.cn/",
		LogoUrl: "https://himg.china.cn/img/common/logo/200x44.png",
	}
}

func (m *ChinaMerchant) Account() map[string]interface{} {
	return m.account.ToMap()
}

func (m *ChinaMerchant) UpdateAccount(key string, value interface{}) {
}

func (m *ChinaMerchant) LoginBy(account map[string]interface{}) (models.User, error) {
	m.account.Init(account)
	return models.User{}, nil
}

func (b ChinaMerchant) IsErrorShouldStopAutoPub(err string) bool {
	if strings.Contains(err, "商铺被审核拒绝") {
		return true
	}
	if strings.Contains(err, "达到产品发布数量上限") {
		return true
	}
	if strings.Contains(err, "用户已超出服务期") {
		return true
	}
	return false
}

func (m *ChinaMerchant) Reg(c models.Company) (interface{}, error) {
	u := fmt.Sprintf("u%s", c.Phone[0])
	pwd := goutils.Md5str(u)[0:8]
	if c.GetAreaID() == 0 {
		return nil, errors.New("公司地址没编辑，不能注册")
	}
	classmapping, err := cat.NewCatService().GetMapping(uint64(c.GetCatID()), models.PlatformChina)
	if err != nil {
		return publisher.SimpleMessage{}, errors.New("找不到对应的分类，不能注册")
	}
	if c.GetCatID() == 0 {
		return nil, errors.New("公司分类没编辑，不能注册")
	}
	areamapping, err := services.Area.GetMapping(uint64(c.GetAreaID()), models.PlatformChina)
	if err != nil {
		return publisher.SimpleMessage{}, errors.New("找不到对应的地区，不能注册")
	}
	p := china.UserReq{
		UserName:      u,
		Password:      pwd,
		Name:          c.ContactName,
		Corpname:      c.Name,
		Corpintro:     c.Introduce,
		Mobile:        c.Phone[0],
		Region:        areamapping.TargetAreaID,
		Corpsubdomain: u,
	}.WithCategorys([]int{convert.Str(classmapping.TargetCatID).MustInt()})
	if data, err := m.api.Reg(p); err != nil {
		return nil, err
	} else {
		m.account.UserName = u
		m.account.Password = pwd
		m.account.Id = data.Corpid
		return data, nil
	}
}

func (m *ChinaMerchant) SubmitCert(c models.Company, ext ...interface{}) (interface{}, error) {
	if c.License == "" {
		return nil, errors.New("需要补全" + ext[0].(string))
	}
	if v, e := request.HTTPGet(c.License); e == nil {
		data := base64.StdEncoding.EncodeToString(v)
		validPeriod := strings.Split(c.ValidPeriod, " - ")
		long := 0
		if len(validPeriod) != 2 {
			return nil, errors.New("中国供应商: 营业期限格式无效，示例: 2014-12-19 - 永续经营")
		}
		if validPeriod[1] == "永续经营" {
			validPeriod[1] = "9999-12-31"
			long = 1
		}
		req := china.SubmitQualificationReq{
			UserName:         m.account.UserName,
			Password:         m.account.Password,
			Pic:              data,
			RegCode:          c.RegNo,
			Organ:            c.RegAuthority,
			Corptype:         china.CorpTypeFromName(c.CompanyType),
			Artificialperson: c.Legal,
			Registerplace:    c.RegAddr,
			Setuptime:        c.RegDate,
			Managetimelong:   long,
			Managetimestart:  validPeriod[0],
			Managetimeend:    validPeriod[1],
			Managebound:      c.Business,
		}
		if data, err := m.api.SubmitQualification(req); err != nil {
			return nil, err
		} else {
			return data, nil
		}
		return nil, nil
	} else {
		return nil, e
	}

}

func (m *ChinaMerchant) GetCert(cid, id int) (interface{}, error) {
	return m.api.GetQualificationStatus(china.GetQualificationStatusReq{
		UserName: m.account.UserName,
	})
}

func (m *ChinaMerchant) RenewWhenNeeded(cid uint64) bool {
	m.initMerchant(cid)
	m.LoginBy(m.merchant.Account)
	if m.merchant.Total > m.merchant.BaseCnt+m.merchant.RenewCount*m.merchant.RenewAddCnt {
		db.Instance().Get().Transaction(func(tx *gorm.DB) error {
			m.merchant.RenewCount += 1
			err := services.Merchant.SaveWithDb(tx, m.merchant)
			if err != nil {
				return err
			}
			if res, err := m.api.Renew(china.RenewReq{UserName: m.account.UserName}); err != nil {
				return err
			} else if res.Succ {
				return nil
			} else {
				return errors.New("succ=false")
			}
			return nil
		})

	}
	return false
}
