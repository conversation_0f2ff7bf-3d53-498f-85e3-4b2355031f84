package merchanter

import (
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"git.paihang8.com/lib/goutils"
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"git.paihang8.com/lib/goutils/sites/souhaohuo"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/cat"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

func init() {
	item := NewSouhaohuoMerchanter()
	models.RegisterMerchant(item.PlatForm(), item)
}

type SouhaohuoMerchant struct {
	name     string
	platform models.PlatForm
	account  merchants.SouhaohuoAccount
	api      souhaohuo.PublishApi
	merchant *models.Merchant
}

func NewSouhaohuoMerchanter() models.Merchanter {
	return &SouhaohuoMerchant{
		name:     "搜好货",
		platform: models.PlatformSouHaoHuo,
		api:      souhaohuo.Publisher,
	}
}
func (m *SouhaohuoMerchant) GetApi() interface{} {
	return m.api
}

func (m *SouhaohuoMerchant) initMerchant(cid uint64) {
	if m.merchant == nil || m.merchant.CompanyID != cid {
		db.Instance().Get().Model(models.Merchant{}).Where("company_id=? and plat_form=?", cid, m.platform).First(&m.merchant)
	}
}

func (m *SouhaohuoMerchant) PublishInfo(info models.Info) (publisher.SimpleMessage, error) {
	var res publisher.SimpleMessage
	if !configs.ApiConfig.Web.IsProduction {
		return publisher.SimpleMessage{
			Url: "https://www.912688.com/",
			ID:  "0",
		}, nil
	}
	start := dbtypes.SHNow().UnixNano()
	services.NewInfoService().PatchExt(&info)
	if len(info.TitlePic) == 0 {
		info.TitlePic = info.Pic
	}

	if len(info.TitlePic) < 1 {
		return publisher.SimpleMessage{
			Url: "https://www.baixing.com/",
			ID:  "0",
		}, fmt.Errorf("标题图片太少, 要求至少1个，实际只有%d个", len(info.TitlePic))
	}

	var categoryId int
	if len(info.Product.Cate) > 1 {
		//categoryId, _ = strconv.Atoi(info.Product.GetCatID())
		categoryId = info.Product.GetCatID()
	} else {
		return publisher.SimpleMessage{}, errors.New("分类需要选到第二级")
	}
	classmapping, err := cat.NewCatService().GetMapping(uint64(categoryId), models.PlatformSouHaoHuo)
	if err != nil {
		return publisher.SimpleMessage{}, fmt.Errorf("分类错误:%w", err)
	}

	//插入图片HY-4227
	preg := regexp.MustCompile("<br/>")
	description := ""
	matched := preg.FindAllStringSubmatchIndex(info.Description, -1)

	if len(matched) > 0 {
		var pre = 0
		var text = ""
		for i, match := range matched {
			if i > len(info.Pic)-1 {
				text = ""
			} else {
				text = `<br/><img src="` + info.Pic[i] + `" />`
			}
			description += info.Description[pre:match[0]] + text + info.Description[match[0]:match[1]]
			pre = match[1]
		}
		description += info.Description[pre:]
		//填充多余的图片
		if len(matched) < len(info.Pic) {
			leftPic := info.Pic[len(matched):]
			for _, p := range leftPic {
				description += `<br/><img src="` + p + `" />`
			}
		}
	} else if len(info.Pic) > 1 {
		description = `<img src="` + info.Pic[1] + `" />` + info.Description
	} else {
		description = strings.ReplaceAll(info.Description, "<br/>", "\r\n")
	}

	msg := souhaohuo.ProductReq{
		CategoryId:    dbtypes.MustInt(classmapping.TargetCatID),
		CalCeil:       info.Product.Unit,
		PicPath:       strings.Join(info.TitlePic, ","),
		UserId:        m.account.Id,
		ProductName:   *info.Title,
		ProductDetail: description,
		Keywords:      strings.Join(info.Word, ","),
	}

	if len(info.Product.Properties) > 0 {
		attrs := []souhaohuo.ProductAttrs{}
		values := []souhaohuo.Attrvalue{}
		for k, v := range info.Product.Properties {
			k := k
			value := fmt.Sprintf("%v", v)
			if value == "" {
				continue
			}
			if goutils.IsNumeric(k) {
				k = cat.NewCatService().GetPropertyName(k)
			}
			attrs = append(attrs, souhaohuo.ProductAttrs{
				Isspec:   0,
				Attrname: k,
				Attrvalue: []souhaohuo.Attrvalue{
					{value},
				},
			})
			values = append(values, souhaohuo.Attrvalue{
				value,
			})
		}
		msg = msg.WithAttrs(attrs).WithValues(values)
	}
	productId, err := m.api.PubProduct(msg)
	if err != nil {
		return res, err
	}
	end := dbtypes.SHNow().UnixNano()
	log.Info("pubInfo", zap.Int64("cost ms", (end-start)/1e+6))
	linkUrl := fmt.Sprintf("https://www.912688.com/supply/%d.html", productId)
	services.NewInfosService().Add(info.CompanyID, linkUrl, uint64(productId), models.PlatformSouHaoHuo)
	return publisher.SimpleMessage{
		ID:  strconv.Itoa(productId),
		Url: linkUrl,
	}, nil
}

func (m *SouhaohuoMerchant) Name() string {
	return m.name
}

func (m *SouhaohuoMerchant) PlatForm() models.PlatForm {
	return m.platform
}

func (m *SouhaohuoMerchant) Clone() models.Merchanter {
	copyed := *m
	copyed.account = merchants.SouhaohuoAccount{}
	return &copyed
}

func (m *SouhaohuoMerchant) Meta() models.MerchantMeta {
	return models.MerchantMeta{
		Name:            m.name,
		Description:     "搜好货",
		PlatForm:        m.platform,
		PlatFormName:    models.PlatFormName(m.platform),
		RequiredAccount: map[string]models.AccountItem{},
		HomeUrl:         "https://www.912688.com/",
		LogoUrl:         "https://style.912688.com/_resources/index_v2/images/logo-slogan.png",
	}
}

func (m *SouhaohuoMerchant) Account() map[string]interface{} {
	return m.account.ToMap()
}

func (m *SouhaohuoMerchant) UpdateAccount(key string, value interface{}) {
}

func (b *SouhaohuoMerchant) LoginBy(account map[string]interface{}) (models.User, error) {
	b.account.Init(account)
	return models.User{}, nil
}

func (b *SouhaohuoMerchant) IsErrorShouldStopAutoPub(err string) bool {
	if strings.Contains(err, "发布产品数量达到上限") {
		return true
	}
	return false
}

func (m *SouhaohuoMerchant) Reg(c models.Company) (interface{}, error) {
	cates := cat.NewCatService().GetIdsForSouhaohuo(c.Cate)
	areas, err := services.Area.GetCitiesForSouhaohuo(c.AreaIds)
	if err != nil {
		return nil, err
	}
	param := souhaohuo.UserReq{
		Mobile:             c.Phone[0],
		Name:               c.Name,
		MajorIndustryIds:   strings.Join(cates, ","),
		MajorProd:          c.MainProduct,
		ProvinceId:         areas[0],
		CityId:             areas[1],
		AreaId:             areas[2],
		AddrDetail:         c.Address,
		ShortCut:           c.Introduce,
		EstablishDate:      c.RegDate,
		CreditNo:           c.RegNo,
		BusinessLicenseUrl: c.License,
	}
	if data, err := m.api.Reg(param); err != nil {
		return nil, err
	} else {
		m.account.UserName = param.Mobile
		m.account.Password = param.Mobile
		m.account.Id = data.UserId
		m.account.Site = data.ShopUrl
		return data, nil
	}
}

func (m *SouhaohuoMerchant) SubmitCert(c models.Company, ext ...interface{}) (interface{}, error) {
	return nil, nil
}

func (m *SouhaohuoMerchant) GetCert(cid, id int) (interface{}, error) {
	return nil, nil
}

func (m *SouhaohuoMerchant) IsErrorMsgRunOut(err string) bool {
	if strings.Contains(err, "发布产品数量达到上限") {
		return true
	}
	return false
}

func (m *SouhaohuoMerchant) RenewWhenNeeded(cid uint64) bool {
	m.initMerchant(cid)
	m.LoginBy(m.merchant.Account)
	if m.merchant.Total > m.merchant.BaseCnt+m.merchant.RenewCount*m.merchant.RenewAddCnt {
		db.Instance().Get().Transaction(func(tx *gorm.DB) error {
			m.merchant.RenewCount += 1
			err := services.Merchant.SaveWithDb(tx, m.merchant)
			if err != nil {
				return err
			}
			// Try to renew with just the user ID
			return m.api.Renew(m.account.Id, 1)
		})
		return true
	}
	return false
}
