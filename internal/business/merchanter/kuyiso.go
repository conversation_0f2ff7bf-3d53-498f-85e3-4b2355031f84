package merchanter

import (
	"errors"
	"fmt"
	"git.paihang8.com/lib/goutils"
	"git.paihang8.com/lib/goutils/request"
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"git.paihang8.com/lib/goutils/sites/kuyiso"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/cat"
	"gitlab.com/all_publish/api/pkg/convert"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"math/rand"
	"regexp"
	"strconv"
	"strings"
)

func init() {
	item := NewKuyisoMerchanter()
	models.RegisterMerchant(item.PlatForm(), item)
	rand.Seed(dbtypes.SHNow().UnixNano())
}

type KuyisoMerchant struct {
	name     string
	platform models.PlatForm
	account  merchants.KuyisoAccount
	api      kuyiso.PublishApi
	merchant *models.Merchant
}

func NewKuyisoMerchanter() models.Merchanter {
	return &KuyisoMerchant{
		name:     "酷易搜",
		platform: models.PlatformKuyiso,
		api:      kuyiso.New(),
	}
}
func (m *KuyisoMerchant) GetApi() interface{} {
	return m.api
}

func (m *KuyisoMerchant) initMerchant(cid uint64) {
	if m.merchant == nil || m.merchant.CompanyID != cid {
		db.Instance().Get().Model(models.Merchant{}).Where("company_id=? and plat_form=?", cid, m.platform).First(&m.merchant)
	}
}

func (m *KuyisoMerchant) PublishInfo(info models.Info) (publisher.SimpleMessage, error) {
	var res publisher.SimpleMessage
	if !configs.ApiConfig.Web.IsProduction {
		return publisher.SimpleMessage{
			Url: "http://www.kuyiso.com/",
			ID:  "0",
		}, nil
	}
	start := dbtypes.SHNow().UnixNano()
	services.NewInfoService().PatchExt(&info)
	var imageids []int
	if len(info.TitlePic) == 0 {
		info.TitlePic = info.Pic
	}
	for _, img := range info.TitlePic {
		if strings.Contains(img, "paihang8.com") {
			if dta, err := services.NewImageService().GetMappings(img, models.PlatformKuyiso); errors.Is(err, gorm.ErrRecordNotFound) {
				if v, e := request.HTTPGet(img); e == nil && len(v) > 0 {
					if data, e := m.api.UploadToAlbum(kuyiso.UploadToAlbumReq{
						UId:     m.account.Uid,
						Albumid: 0,
					}.AddImage(v, "發發助手")); e == nil {
						id, _ := strconv.Atoi(data[0].ImgID)
						services.NewImageService().SetMappings(img, id, data[0].ImgURL, models.PlatformKuyiso)
						imageids = append(imageids, id)
					} else {
						return publisher.SimpleMessage{}, fmt.Errorf("上传到酷易搜失败:%w", e)
					}
				}
			} else if dta != nil && dta.TargetUrl != "" {
				imageids = append(imageids, dta.TargetImageID)
			}
		}
	}
	var contentPic []string
	if len(info.TitlePic) > 0 {
		for _, img := range info.Pic {
			if strings.Contains(img, "paihang8.com") {
				if dta, err := services.NewImageService().GetMappings(img, models.PlatformKuyiso); errors.Is(err, gorm.ErrRecordNotFound) {
					if v, e := request.HTTPGet(img); e == nil && len(v) > 0 {
						if data, e := m.api.UploadToAlbum(kuyiso.UploadToAlbumReq{
							UId:     m.account.Uid,
							Albumid: 0,
						}.AddImage(v, "發發助手")); e == nil {
							id, _ := strconv.Atoi(data[0].ImgID)
							services.NewImageService().SetMappings(img, id, data[0].ImgURL, models.PlatformKuyiso)
							contentPic = append(contentPic, data[0].ImgURL)
						} else {
							return publisher.SimpleMessage{}, fmt.Errorf("上传到酷易搜失败:%w", e)
						}
					}
				} else if dta != nil && dta.TargetUrl != "" {
					contentPic = append(contentPic, dta.TargetUrl)
				}
			}
		}
	}
	var categoryId int
	if len(info.Product.Cate) > 0 {
		categoryId, _ = strconv.Atoi(info.Product.Cate[len(info.Product.Cate)-1])
	}
	if categoryId == 0 {
		categoryId = info.Company.GetCatID()
	}
	classmapping, err := cat.NewCatService().GetMapping(uint64(categoryId), models.PlatformKuyiso)
	if err != nil {
		return publisher.SimpleMessage{}, err
	}

	//插入图片HY-4227
	preg := regexp.MustCompile("<br/>")
	description := ""
	matched := preg.FindAllStringSubmatchIndex(info.Description, -1)

	if len(matched) > 0 {
		var pre = 0
		var text = ""
		for i, match := range matched {
			if i > len(contentPic)-1 {
				text = ""
			} else {
				text = `<br/><img src="` + contentPic[i] + `" />`
			}
			description += info.Description[pre:match[0]] + text + info.Description[match[0]:match[1]]
			pre = match[1]
		}
		description += info.Description[pre:]
		//填充多余的图片
		if len(matched) < len(contentPic) {
			leftPic := contentPic[len(matched):]
			for _, p := range leftPic {
				description += `<br/><img src="` + p + `" />`
			}
		}
	} else if len(contentPic) > 1 {
		description = `<img src="` + contentPic[1] + `" />` + info.Description
	} else {
		description = info.Description
	}
	areaId := 0
	level := 0
	if len(info.Product.AreaIds) > 0 {
		for i := len(info.Product.AreaIds) - 1; i >= 0; i-- {
			if info.Product.AreaIds[i] != "0" {
				areaId, _ = strconv.Atoi(info.Product.AreaIds[i])
				level = i + 1
				break
			}
		}
	}
	var areamapping models.AreaMappings
	if areaId == 0 {
		areamapping, err = services.Area.RandCity(models.PlatformKuyiso, []models.AreaType{models.AreaTypeCountyOfDirectCity, models.AreaTypeCountyOfCity})
		if err != nil {
			return publisher.SimpleMessage{}, err
		}
	} else {
		if level == 1 {
			if subs, err := services.Area.FindSubsByPid(uint64(areaId)); err != nil {
				return publisher.SimpleMessage{}, err
			} else {
				idx := rand.Intn(len(subs))
				areaId = int(subs[idx].ID)
				level = 2
			}
		}
		if level >= 2 {
			areamapping, err = services.Area.GetMapping(uint64(areaId), models.PlatformKuyiso)
			if err != nil {
				return publisher.SimpleMessage{}, err
			}
		}
	}
	var phone, mobile string
	if len(info.Company.Phone) > 0 {
		mobile = info.Company.Phone[0]
	}
	if len(info.Company.Phone) > 1 {
		phone = info.Company.Phone[1]
	}
	msg := kuyiso.MessageReq{
		UID:         m.account.Uid,
		Subject:     *info.Title,
		Categoryid:  convert.Str(classmapping.TargetCatID).MustInt(),
		AreaID:      convert.Str(areamapping.TargetAreaID).MustInt(),
		Phone:       phone,
		Mobile:      mobile,
		Author:      info.Company.ContactName,
		Coname:      info.Company.Name,
		ProductName: info.Product.Name,
	}
	if len(imageids) > 0 {
		msg.ImageIDs = imageids
	}
	if len(info.Word) > 0 {
		msg = msg.WithTags(info.Word)
	}

	if info.Product.Price != nil {
		msg.Price = *info.Product.Price
	}
	proterties := map[string]string{}
	for k, v := range info.Product.Properties {
		if goutils.IsNumeric(k) {
			proterties[cat.NewCatService().GetPropertyName(k)] = fmt.Sprintf("%v", v)
		} else {
			proterties[k] = fmt.Sprintf("%v", v)
		}
	}
	msg = msg.WithContent(proterties, description)
	response, err := m.api.PostMessage(msg)
	if err != nil {
		return res, err
	}
	end := dbtypes.SHNow().UnixNano()
	log.Info("pubInfo", zap.Int64("cost ms", (end-start)/1e+6))
	id, _ := strconv.Atoi(response.ID)
	services.NewInfosService().Add(info.CompanyID, response.Itemurl, uint64(id), models.PlatformKuyiso)
	return publisher.SimpleMessage{
		ID:  response.ID,
		Url: response.Itemurl,
	}, nil
}

func (m *KuyisoMerchant) Name() string {
	return m.name
}

func (m *KuyisoMerchant) PlatForm() models.PlatForm {
	return m.platform
}

func (m *KuyisoMerchant) Clone() models.Merchanter {
	copyed := *m
	copyed.account = merchants.KuyisoAccount{}
	copyed.api = kuyiso.New()
	return &copyed
}

func (m *KuyisoMerchant) Meta() models.MerchantMeta {
	return models.MerchantMeta{
		Name:         m.name,
		Description:  "酷易搜",
		PlatForm:     m.platform,
		PlatFormName: models.PlatFormName(m.platform),
		RequiredAccount: map[string]models.AccountItem{
			"uid": {
				Type: "int",
				Desc: "用户id",
			},
			"id": {
				Type: "int",
				Desc: "公司id",
			},
			"mobile": {
				Type: "string",
				Desc: "手机号",
			},
			"password": {
				Type: "string",
				Desc: "密码",
			},
		},
		HomeUrl: "http://www.kuyiso.com/",
		LogoUrl: "http://static.kuyiso.com/images/logo.png",
	}
}

func (m *KuyisoMerchant) Account() map[string]interface{} {
	return m.account.ToMap()
}

func (m *KuyisoMerchant) UpdateAccount(key string, value interface{}) {
}

func (m *KuyisoMerchant) LoginBy(account map[string]interface{}) (models.User, error) {
	m.account.Init(account)
	if _, err := m.api.Login(convert.Str(m.account.Mobile).MustInt(), m.account.Password); err != nil {
		return models.User{}, err
	}
	return models.User{}, nil
}

func (b KuyisoMerchant) IsErrorShouldStopAutoPub(err string) bool {
	if strings.Contains(err, "已达上限") {
		return true
	}
	if strings.Contains(err, "密码错误") {
		return true
	}
	return false
}

func (m *KuyisoMerchant) Reg(c models.Company) (interface{}, error) {
	u := fmt.Sprintf("u%s", c.Phone[0])
	pwd := dbtypes.GenPassword(true, true, true, true, 8)
	if c.GetAreaID() == 0 {
		return nil, errors.New("公司地址没编辑，不能注册")
	}
	classmapping, err := cat.NewCatService().GetMapping(uint64(c.GetCatID()), models.PlatformKuyiso)
	if err != nil {
		return publisher.SimpleMessage{}, errors.New("找不到对应的分类，不能注册")
	}
	if c.GetCatID() == 0 {
		return nil, errors.New("公司分类没编辑，不能注册")
	}
	areamapping, err := services.Area.GetMapping(uint64(c.GetAreaID()), models.PlatformKuyiso)
	if err != nil {
		return publisher.SimpleMessage{}, errors.New("找不到对应的地区，不能注册")
	}
	mobile, _ := strconv.Atoi(c.Phone[0])
	pp := kuyiso.RegReq{
		Mobile:   mobile,
		Password: pwd,
		Name:     u,
	}
	var uid int
	if m.account.Uid > 0 {
		uid = m.account.Uid
		mobile, _ = strconv.Atoi(m.account.Mobile)
		pwd = m.account.Password
	} else {
		if data, err := m.api.Reg(pp); err != nil {
			return publisher.SimpleMessage{}, err
		} else {
			uid, _ = strconv.Atoi(data.UID)
			m.account.Mobile = c.Phone[0]
			m.account.Password = pwd
			m.account.Uid = uid
		}
	}

	if _, err := m.api.Login(mobile, pwd); err != nil {
		return publisher.SimpleMessage{}, err
	}
	p := kuyiso.RegCompanyReq{
		UID:         uid,
		Coname:      c.Name,
		Realname:    c.ContactName,
		Con_mobile:  mobile,
		Categoryid:  convert.Str(classmapping.TargetCatID).MustInt(),
		Areaid:      convert.Str(areamapping.TargetAreaID).MustInt(),
		Mainproduct: c.MainProduct,
		Description: c.Introduce,
	}
	if data, err := m.api.RegCompany(p); err != nil {
		return nil, err
	} else {
		m.account.Mobile = c.Phone[0]
		m.account.Password = pwd
		m.account.Id = dbtypes.MustInt(data.Companyid)
		m.account.Uid = data.Userid
		return data, nil
	}
}

func (m *KuyisoMerchant) SubmitCert(c models.Company, ext ...interface{}) (interface{}, error) {
	if m.account.IsPersonal {
		var back []byte
		var hand []byte
		if c.BackImage == "" {
			return nil, errors.New("需要补全身份证反面")
		} else {
			if v, e := request.HTTPGet(c.BackImage); e == nil {
				back = v
			} else {
				return nil, e
			}
		}
		if c.HandImage == "" {
			return nil, errors.New("需要补全手持身份证照片a")
		} else {
			if v, e := request.HTTPGet(c.HandImage); e == nil {
				hand = v
			} else {
				return nil, e
			}
		}
		req := kuyiso.SubCertReq{
			UID:    m.account.Uid,
			Name:   c.ContactName,
			Idcard: c.IDCardNo,
			Type:   kuyiso.CertTypePerson,
		}.WithLicense([][]byte{back, hand})
		if data, err := m.api.SubPersonCert(req); err != nil {
			return nil, err
		} else {
			return data, nil
		}
		return nil, nil

	}
	if c.License == "" {
		return nil, errors.New("需要补全营业执照")
	} else {
		if v, e := request.HTTPGet(c.License); e == nil {
			req := kuyiso.SubCertReq{
				UID:  m.account.Uid,
				Name: c.Name,
				Type: kuyiso.CertTypeCompany,
			}.WithLicense([][]byte{v})
			if data, err := m.api.SubCompanyCert(req); err != nil {
				return nil, err
			} else {
				return data, nil
			}
			return nil, nil
		} else {
			return nil, e
		}
	}

}

func (m *KuyisoMerchant) GetCert(cid, id int) (interface{}, error) {
	if m.account.IsPersonal {
		return m.api.GetPersonCert(kuyiso.GetCertReq{
			UID:  m.account.Uid,
			Type: kuyiso.CertTypePerson,
		})
	}
	return m.api.GetCompanyCert(kuyiso.GetCertReq{
		UID:  m.account.Uid,
		Type: kuyiso.CertTypeCompany,
	})
}

func (m *KuyisoMerchant) RenewWhenNeeded(cid uint64) bool {
	m.initMerchant(cid)
	m.LoginBy(m.merchant.Account)
	if m.merchant.Total > m.merchant.BaseCnt+m.merchant.RenewCount*m.merchant.RenewAddCnt {
		if com, err := m.api.GetUserBoughtMsgCnt(m.account.Uid); err != nil {
			log.Error("kuyiso", err)
		} else {
			if m.merchant.Total > convert.Str(com.Maxitem).MustInt() {
				if ok, err := m.api.Renew(m.account.Uid); err != nil {
					log.Error("kuyiso", err)
				} else if ok {
					m.merchant.RenewCount += 1
					err := services.Merchant.Save(m.merchant)
					log.Error("kuyiso", err)
					return true
				}
			}
		}
	}
	return false
}
