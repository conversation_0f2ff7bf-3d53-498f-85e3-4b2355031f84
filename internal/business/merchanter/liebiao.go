package merchanter

import (
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"git.paihang8.com/lib/goutils/sites/liebiao"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/cat"
	"gitlab.com/all_publish/api/pkg/convert"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"go.uber.org/zap"
)

const (
	AuthTypePersonal = 0
	AuthTypeLicense  = 1
	AuthTypeOther    = 2
)

func init() {
	item := NewLiebiaoMerchanter()
	models.RegisterMerchant(item.PlatForm(), item)
}

type LiebiaoMerchant struct {
	name     string
	platform models.PlatForm
	account  merchants.LiebiaoAccount
	api      liebiao.PublishApi
}

func NewLiebiaoMerchanter() models.Merchanter {
	return &LiebiaoMerchant{
		name:     "列表网",
		platform: models.PlatformLiebiao,
		api:      liebiao.LiebiaoPublisher,
	}
}
func (m *LiebiaoMerchant) GetApi() interface{} {
	return m.api
}

func (m *LiebiaoMerchant) PublishInfo(info models.Info) (publisher.SimpleMessage, error) {
	var res publisher.SimpleMessage
	if !configs.ApiConfig.Web.IsProduction {
		return publisher.SimpleMessage{
			Url: "http://www.liebiao.com/",
			ID:  "0",
		}, nil
	}
	start := dbtypes.SHNow().UnixNano()
	services.NewInfoService().PatchExt(&info)
	if len(info.TitlePic) == 0 {
		info.TitlePic = info.Pic
	}

	if len(info.TitlePic) < 1 {
		return publisher.SimpleMessage{
			Url: "https://www.liebiao.com/",
			ID:  "0",
		}, fmt.Errorf("标题图片太少, 要求至少1个，实际只有%d个", len(info.TitlePic))
	}

	var categoryId int
	if len(info.Product.Cate) > 0 {
		categoryId, _ = strconv.Atoi(info.Product.Cate[len(info.Product.Cate)-1])
	}
	if categoryId == 0 {
		categoryId = info.Company.GetCatID()
	}
	classmapping, err := cat.NewCatService().GetMapping(uint64(categoryId), models.PlatformLiebiao)
	if err != nil {
		return publisher.SimpleMessage{}, err
	}

	//插入图片HY-4227
	preg := regexp.MustCompile("<br/>")
	description := ""
	matched := preg.FindAllStringSubmatchIndex(info.Description, -1)

	if len(matched) > 0 {
		var pre = 0
		var text = ""
		for i, match := range matched {
			if i > len(info.Pic)-1 {
				text = ""
			} else {
				text = `<br/><img src="` + info.Pic[i] + `" />`
			}
			description += info.Description[pre:match[0]] + text + info.Description[match[0]:match[1]]
			pre = match[1]
		}
		description += info.Description[pre:]
		//填充多余的图片
		if len(matched) < len(info.Pic) {
			leftPic := info.Pic[len(matched):]
			for _, p := range leftPic {
				description += `<br/><img src="` + p + `" />`
			}
		}
	} else if len(info.Pic) > 1 {
		description = `<img src="` + info.Pic[1] + `" />` + info.Description
	} else {
		description = info.Description
	}

	cats := strings.Split(classmapping.TargetCatID, ",")
	msg := liebiao.PostInfo{
		SourceId:  fmt.Sprintf("%d", info.ID),
		UserId:    m.account.Id,
		CateId:    convert.Str(cats[1]).MustInt(),
		Title:     *info.Title,
		Content:   description,
		Contacts:  info.Company.ContactName,
		Phone:     info.Company.Phone[0],
		ImageUrls: info.TitlePic,
		Weizhi:    info.Company.Address,
		Filter:    cats[0],
	}

	if cityIds, err := m.SelectAreaId(info.Product.GetAreaIds(), *info.Title); err != nil {
		return publisher.SimpleMessage{}, err
	} else {
		msg.CityId = cityIds[0]
		msg.Rid = cityIds[1]
	}
	response, err := m.api.AddPost(liebiao.PostReq{msg})
	if err != nil {
		return res, err
	}
	end := dbtypes.SHNow().UnixNano()
	log.Info("pubInfo", zap.Int64("cost ms", (end-start)/1e+6))
	services.NewInfosService().Add(info.CompanyID, response.PostUrl, uint64(response.PostId), models.PlatformLiebiao)
	return publisher.SimpleMessage{
		ID:  strconv.Itoa(response.PostId),
		Url: response.PostUrl,
	}, nil
}

// http://newjira.huangye88.net/browse/HY-8365
func (m *LiebiaoMerchant) SelectAreaId(areas []int, title string) (cityIds [2]int, err error) {
	cityInTitle := ""
	for _, w := range services.Area.AllNames() {
		if strings.Contains(title, w) {
			cityInTitle = w
		}
	}
	var areamapping models.AreaMappings
	var fareamapping models.AreaMappings

	if len(areas) == 0 || areas[0] == 0 || len(areas) == 1 {
		provinceId := 0
		if len(areas) == 1 {
			provinceId = areas[0]
		}
		fareamapping, err = services.Area.RandLastCityByTip(models.PlatformLiebiao, cityInTitle, provinceId)
		if err != nil {
			return
		}
		return [2]int{convert.Str(fareamapping.TargetAreaID).MustInt(), convert.Str(areamapping.TargetAreaID).MustInt()}, nil
	} else {
		if len(areas) == 2 {
			fareamapping, err = services.Area.GetMapping(uint64(areas[1]), models.PlatformLiebiao)
			if err != nil {
				return
			}
			return [2]int{convert.Str(fareamapping.TargetAreaID).MustInt(), convert.Str(areamapping.TargetAreaID).MustInt()}, nil
		} else {
			fareamapping, err = services.Area.GetMapping(uint64(areas[1]), models.PlatformLiebiao)
			if err != nil {
				return
			}
			areamapping, err = services.Area.GetMapping(uint64(areas[2]), models.PlatformLiebiao)
			if err != nil {
				return
			}
			return [2]int{convert.Str(fareamapping.TargetAreaID).MustInt(), convert.Str(areamapping.TargetAreaID).MustInt()}, nil
		}
	}
}

func (m *LiebiaoMerchant) Name() string {
	return m.name
}

func (m *LiebiaoMerchant) PlatForm() models.PlatForm {
	return m.platform
}

func (m *LiebiaoMerchant) Clone() models.Merchanter {
	copyed := *m
	copyed.account = merchants.LiebiaoAccount{}
	return &copyed
}

func (m *LiebiaoMerchant) Meta() models.MerchantMeta {
	return models.MerchantMeta{
		Name:            m.name,
		Description:     "列表网",
		PlatForm:        m.platform,
		PlatFormName:    models.PlatFormName(m.platform),
		RequiredAccount: map[string]models.AccountItem{},
		HomeUrl:         "https://cn.china.cn/",
		LogoUrl:         "https://himg.china.cn/img/common/logo/200x44.png",
	}
}

func (m *LiebiaoMerchant) Account() map[string]interface{} {
	return m.account.ToMap()
}

func (m *LiebiaoMerchant) UpdateAccount(key string, value interface{}) {
}

func (b *LiebiaoMerchant) LoginBy(account map[string]interface{}) (models.User, error) {
	b.account.Init(account)
	return models.User{}, nil
}

func (b *LiebiaoMerchant) IsErrorShouldStopAutoPub(err string) bool {
	if strings.Contains(err, "发布产品数量达到上限") {
		return true
	}
	return false
}

func (m *LiebiaoMerchant) Reg(c models.Company) (interface{}, error) {
	p := liebiao.UserReq{UserInfo: liebiao.UserInfo{
		SourceId: fmt.Sprintf("%d", c.ID),
		Phone:    c.Phone[0],
	}}

	if data, err := m.api.Register(p); err != nil {
		return nil, err
	} else {
		m.account.Id = data.UserId
		m.account.UserName = data.UserName
		m.account.SourceID = p.UserInfo.SourceId
		m.account.Phone = p.UserInfo.Phone
		m.account.Account = data.Account
		return data, nil
	}
}

func (m *LiebiaoMerchant) SubmitCert(c models.Company, ext ...interface{}) (interface{}, error) {

	//个人认证
	t := ext[0].(int)
	if t == AuthTypePersonal {
		req := liebiao.IdCardInfo{
			UserId: m.account.Id,
			Name:   c.ContactName,
			// Number:     c.IDCardNo,
			Gender: fmt.Sprintf("%d", *c.Gender),
			// FrontImage: c.FrontImage,
			// HandImage:  c.HandImage,
		}
		err := m.api.AuthorizeByIdCard(liebiao.IdCardReq{req})
		return nil, err
	} else if t == AuthTypeLicense {
		validPeriod := strings.Split(c.ValidPeriod, " - ")
		if len(validPeriod) != 2 {
			return nil, errors.New("列表网: 营业期限格式无效，示例: 2014-12-19 - 永续经营")
		}
		if validPeriod[1] == "永续经营" {
			validPeriod[1] = "9999-12-31"
		}
		req := liebiao.LicenseInfo{
			UserId:       m.account.Id,
			Company:      c.Name,
			License:      c.RegNo,
			DeadLine:     validPeriod[1],
			LicenseImage: c.License,
		}
		err := m.api.AuthorizeByBusinessLicense(liebiao.LicenseReq{req})
		return nil, err
	}
	return nil, nil
}

func (m *LiebiaoMerchant) GetCert(cid, id int) (interface{}, error) {
	return m.api.GetAuthorizedResult(liebiao.AuthorizedResultReq{UserId: m.account.Id})
}

func (m *LiebiaoMerchant) IsErrorMsgRunOut(err string) bool {
	if strings.Contains(err, "发布产品数量达到上限") {
		return true
	}
	return false
}

func (m *LiebiaoMerchant) RenewWhenNeeded(cid uint64) bool {
	return false
}
