package merchanter

import (
	"fmt"
	"git.paihang8.com/lib/goutils/sites/baixing"
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"gitlab.com/all_publish/api/configs"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/cat"
	"gitlab.com/all_publish/api/pkg/convert"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"go.uber.org/zap"
	"strings"
)

func init() {
	item := NewBaixingMerchanter()
	models.RegisterMerchant(item.PlatForm(), item)
}

type BaixingMerchant struct {
	name     string
	platform models.PlatForm
	account  merchants.BaixingAccount
	api      baixing.PublishApi
}

func NewBaixingMerchanter() models.Merchanter {
	return &BaixingMerchant{
		name:     "百姓网",
		platform: models.PlatformBaixing,
		api:      baixing.BaixingPublisher,
	}
}
func (m *BaixingMerchant) GetApi() interface{} {
	return m.api
}

func (m *BaixingMerchant) PublishInfo(info models.Info) (publisher.SimpleMessage, error) {
	var res publisher.SimpleMessage
	if !configs.ApiConfig.Web.IsProduction {
		return publisher.SimpleMessage{
			Url: "http://www.baixing.com/",
			ID:  "0",
		}, nil
	}
	start := dbtypes.SHNow().UnixNano()
	services.NewInfoService().PatchExt(&info)
	if len(info.TitlePic) == 0 {
		info.TitlePic = info.Pic
	}

	if len(info.TitlePic) < 1 {
		return publisher.SimpleMessage{
			Url: "https://www.baixing.com/",
			ID:  "0",
		}, fmt.Errorf("标题图片太少, 要求至少1个，实际只有%d个", len(info.TitlePic))
	}

	//var categoryId int
	//if len(info.Product.Cate) > 1 {
	//	//categoryId, _ = strconv.Atoi(info.Product.GetCatID())
	//	categoryId = info.Product.GetCatID()
	//} else {
	//	return publisher.SimpleMessage{}, errors.New("分类需要选到第二级")
	//}
	//classmapping, err := cat.NewCatService().GetMapping(uint64(categoryId), models.PlatformBaixing)
	//if err != nil {
	//	return publisher.SimpleMessage{}, fmt.Errorf("分类错误:%w", err)
	//}

	fareamapping, areamapping, err := services.Area.FindCityAndNext(info.Product.AreaIds, models.PlatformBaixing)
	if err != nil {
		return publisher.SimpleMessage{}, fmt.Errorf("地区错误:%w", err)
	}

	//插入图片HY-4227
	//preg := regexp.MustCompile("<br/>")
	description := ""
	//matched := preg.FindAllStringSubmatchIndex(info.Description, -1)

	//if len(matched) > 0 {
	//	var pre = 0
	//	var text = ""
	//	for i, match := range matched {
	//		if i > len(info.Pic)-1 {
	//			text = ""
	//		} else {
	//			//text = `<br/><img src="` + info.Pic[i] + `" />`
	//		}
	//		description += info.Description[pre:match[0]] + text + info.Description[match[0]:match[1]]
	//		pre = match[1]
	//	}
	//	description += info.Description[pre:]
	//	//填充多余的图片
	//	if len(matched) < len(info.Pic) {
	//		leftPic := info.Pic[len(matched):]
	//		for _, p := range leftPic {
	//			//description += `<br/><img src="` + p + `" />`
	//		}
	//	}
	//} else if len(info.Pic) > 1 {
	//	description = `<img src="` + info.Pic[1] + `" />` + info.Description
	//} else {
	description = strings.ReplaceAll(info.Description, "<br/>", "\r\n")
	//}
	images := []baixing.ImageUrl{}
	for _, v := range info.TitlePic {
		v = strings.ReplaceAll(v, "https://", "http://")
		images = append(images, baixing.ImageUrl{Url: v})
	}
	msg := baixing.MsgReq{
		UserMobile:   info.Company.Phone[0],
		CityName:     fareamapping.Ext,
		CategoryId:   cat.OtherService,
		AreaId:       areamapping.TargetAreaID,
		Title:        *info.Title,
		Content:      description,
		Contact:      info.Company.Phone[0],
		UploadImages: images,
	}

	//response, err := m.api.PostMsgByBafang(msg, cat.NewCatService().FillBaixingProperties(classmapping.TargetCatID, areamapping.TargetAreaID, info.Product.Properties))
	response, err := m.api.PostMsgByBafang(msg, cat.NewCatService().FillBaixingProperties(cat.OtherService, areamapping.TargetAreaID, info.Product.Properties))
	if err != nil {
		return res, err
	}
	end := dbtypes.SHNow().UnixNano()
	log.Info("pubInfo", zap.Int64("cost ms", (end-start)/1e+6))
	services.NewInfosService().Add(info.CompanyID, response.Link, uint64(convert.Str(response.Id).MustInt()), models.PlatformBaixing)
	return publisher.SimpleMessage{
		ID:  response.Id,
		Url: response.Link,
	}, nil
}

func (m *BaixingMerchant) Name() string {
	return m.name
}

func (m *BaixingMerchant) PlatForm() models.PlatForm {
	return m.platform
}

func (m *BaixingMerchant) Clone() models.Merchanter {
	copyed := *m
	copyed.account = merchants.BaixingAccount{}
	return &copyed
}

func (m *BaixingMerchant) Meta() models.MerchantMeta {
	return models.MerchantMeta{
		Name:            m.name,
		Description:     "百姓网",
		PlatForm:        m.platform,
		PlatFormName:    models.PlatFormName(m.platform),
		RequiredAccount: map[string]models.AccountItem{},
		HomeUrl:         "https://www.baixing.com/",
		LogoUrl:         "https://file.baixing.net/201709/4916aa54f4b4c69b4c01591fe6a87046.png",
	}
}

func (m *BaixingMerchant) Account() map[string]interface{} {
	return m.account.ToMap()
}

func (m *BaixingMerchant) UpdateAccount(key string, value interface{}) {
}

func (b *BaixingMerchant) LoginBy(account map[string]interface{}) (models.User, error) {
	b.account.Init(account)
	return models.User{}, nil
}

func (b *BaixingMerchant) IsErrorShouldStopAutoPub(err string) bool {
	if strings.Contains(err, "发布产品数量达到上限") {
		return true
	}
	return false
}

func (m *BaixingMerchant) Reg(c models.Company) (interface{}, error) {
	return nil, nil
}

func (m *BaixingMerchant) SubmitCert(c models.Company, ext ...interface{}) (interface{}, error) {
	return nil, nil
}

func (m *BaixingMerchant) GetCert(cid, id int) (interface{}, error) {
	return nil, nil
}

func (m *BaixingMerchant) IsErrorMsgRunOut(err string) bool {
	if strings.Contains(err, "发布产品数量达到上限") {
		return true
	}
	return false
}

func (m *BaixingMerchant) RenewWhenNeeded(cid uint64) bool {
	return false
}
