package eventtrigger

import (
	"errors"
	"fmt"
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"git.paihang8.com/lib/goutils/sites/souhaohuo"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/cat"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/gorm"
	"math/rand"
	"strings"
	"time"
	"unicode/utf8"
)

type souhaohuoTrigger struct {
}

func init() {
	souhaohuo := new(souhaohuoTrigger)
	models.RegisterTrigger(models.PlatformSouHaoHuo, souhaohuo)
}

func (trigger *souhaohuoTrigger) OnCompanyEdit(c *models.Company) {
	if item, err := services.Merchant.GetByCompanyId(c.ID, models.PlatformSouHaoHuo); err == nil {
		if c.GetAreaID() > 0 && c.GetCatID() > 0 {
			if item.TargetCompanyID == 0 {
				if u, e := services.NewUserService().GetUserByCompanyId(c.ID); e == nil {
					u.Company = *c
					trigger.AutoReg(u, item)
				}
			} else {
				var account merchants.SouhaohuoAccount
				account.Init(item.Account)
				client, _ := models.GetMerchant(models.PlatformSouHaoHuo)
				//同步公司资料
				shouldCallModifyCompany := false
				for _, v := range c.AuditingFields {
					modifyCompanyKeys := []string{"cate", "phone", "name", "introduce", "areaids", "main_product", "cate", "license", "reg_no", "reg_date"}
					for _, vv := range modifyCompanyKeys {
						if v == vv {
							shouldCallModifyCompany = true
							break
						}
					}
				}
				if shouldCallModifyCompany {
					areas, _ := services.Area.GetCitiesForSouhaohuo(c.AreaIds)
					cates := cat.NewCatService().GetIdsForSouhaohuo(c.Cate)
					params := souhaohuo.ComReq{
						UserId:             account.Id,
						Name:               c.Name,
						ProvinceId:         areas[0],
						CityId:             areas[1],
						AreaId:             areas[2],
						BusinessLicenseUrl: c.License,
						MajorProd:          c.MainProduct,
						MajorIndustryIds:   strings.Join(cates, ","),
						AddrDetail:         c.Address,
						ShortCut:           c.Introduce,
						EstablishDate:      c.RegDate,
						CreditNo:           c.RegNo,
					}
					client.GetApi().(souhaohuo.PublishApi).ModifyCompany(params, c.Phone[0])
				}
			}
			trigger.AutoFlow(item, c, true)
		}
	}
}

func (trigger *souhaohuoTrigger) AutoReg(u *models.User, merchant *models.Merchant) {
	client, ok := models.GetMerchant(models.PlatformSouHaoHuo)
	if !ok {
		return
	}
	if err := trigger.ValidateCompany(u.Company); err != nil {
		return
	}
	if res, err := client.Reg(u.Company); err != nil {
		merchant.RegFailedReason = err.Error()
		services.Merchant.Save(merchant)
	} else {
		var user = res.(souhaohuo.SimpleUser)
		merchant.CompanySite = user.ShopUrl
		merchant.RegFailedReason = ""
		merchant.Account = client.Account()
		merchant.TargetCompanyID = merchant.Account["id"].(int)
		merchant.CertStatus = dbtypes.CertStatusPassed
		services.Merchant.Save(merchant)
	}
}

func (trigger *souhaohuoTrigger) OnUserLogin(u *models.User, config publisher.PlatformConfig, mClient models.Merchanter) {
	trigger.OnFirstLogin(u, config, mClient)
	client, ok := models.GetMerchant(models.PlatformSouHaoHuo)
	if !ok {
		return
	}
	client.RenewWhenNeeded(u.CompanyID)
}

func (trigger *souhaohuoTrigger) OnFirstLogin(u *models.User, config publisher.PlatformConfig, mClient models.Merchanter) {
	client, ok := models.GetMerchant(models.PlatformSouHaoHuo)
	if !ok {
		return
	}
	services.NewUserPlatformService().UpdateFunction(models.UserPlatform{CompanyID: u.CompanyID,
		Platform: models.PlatformSouHaoHuo, Funcname: models.FuncnamePost, Status: models.UserPlatformStatusOpened,
		ExpireTime: u.PostExpireTime})
	if item, err := services.Merchant.GetByCompanyId(u.CompanyID, models.PlatformSouHaoHuo); err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		var merchantItem models.Merchant
		merchantItem.CreatedAt = dbtypes.SHNow()
		merchantItem.UpdatedAt = dbtypes.SHNow()
		merchantItem.CompanyID = u.CompanyID
		merchantItem.Account = client.Account()
		merchantItem.Pause = false
		merchantItem.Total = config.MaxItem
		merchantItem.BaseCnt = config.BaseItem
		merchantItem.RenewAddCnt = config.BaseItem
		merchantItem.CertStatus = dbtypes.CertStatusPassed
		merchantItem.DailyPubProducts = 2
		merchantItem.CompanySite = "https://www.912688.com/"
		merchantItem.PubCount = 20
		merchantItem.PubPerCount = 3
		merchantItem.AutoPub = true
		now, _ := time.ParseInLocation("15:04:05", fmt.Sprintf("%02d:%02d:00", rand.Intn(14)+1, rand.Intn(59)), dbtypes.SHLocation)
		autoPubTime := dbtypes.MysqlTime{now}
		merchantItem.AutoPubTime = &autoPubTime
		name := client.Name()
		merchantItem.Name = &name
		merchantItem.Contact = u.Company.ContactName
		merchantItem.ContactPhone = u.Phone
		merchantItem.PlatForm = models.PlatformSouHaoHuo
		if err := services.Merchant.Create(&merchantItem); err == nil {
			trigger.AutoReg(u, &merchantItem)
		}

	} else {
		item.Total = config.MaxItem
		item.BaseCnt = config.BaseItem
		item.RenewAddCnt = config.BaseItem
		services.Merchant.Updates(item, "total", "base_cnt", "renew_add_cnt")
		var account merchants.SouhaohuoAccount
		account.Init(item.Account)
		if account.Id == 0 {
			//需要注册
			trigger.AutoReg(u, item)
		}
		c, _ := services.NewCompanyService().Find(item.CompanyID)
		trigger.AutoFlow(item, c)
	}
}

func (trigger *souhaohuoTrigger) AutoFlow(merchant *models.Merchant, c *models.Company, userTrigger ...bool) bool {
	return true
}
func (trigger *souhaohuoTrigger) ValidateCompany(c models.Company) error {
	cates := cat.NewCatService().GetIdsForSouhaohuo(c.Cate)
	areas, err := services.Area.GetCitiesForSouhaohuo(c.AreaIds)
	if err != nil {
		return err
	}
	mainproducts := strings.Split(c.MainProduct, ",")
	for _, v := range mainproducts {
		size := utf8.RuneCountInString(v)
		if size < 2 || size > 10 {
			return errors.New("搜好货: 主营产品以逗号分隔，每个产品长度为2-10个汉字")
		}
	}
	param := souhaohuo.UserReq{
		Mobile:             c.Phone[0],
		Name:               c.Name,
		MajorIndustryIds:   strings.Join(cates, ","),
		MajorProd:          c.MainProduct,
		ProvinceId:         areas[0],
		CityId:             areas[1],
		AreaId:             areas[2],
		AddrDetail:         c.Address,
		ShortCut:           c.Introduce,
		EstablishDate:      c.RegDate,
		CreditNo:           c.RegNo,
		BusinessLicenseUrl: c.License,
	}
	if err := models.Valiate(param); err != nil {
		return errors.New("搜好货:" + err.Error())
	}
	return nil
}
func (trigger *souhaohuoTrigger) CalSteps(m *models.Merchant, c models.Company) (s schemas.MerchantStep) {
	s.Name = models.PlatFormName(m.PlatForm)
	s.Steps = append(s.Steps, schemas.Step{
		Text:   "已开通",
		Action: dbtypes.ActionNone,
		Status: schemas.StepStatusDone,
	})
	return
}

func (trigger *souhaohuoTrigger) FormatAccount(m *models.Merchant) (ret map[string]interface{}) {
	ret = map[string]interface{}{
		"username": m.Company.Phone[0],
		"password": m.Company.Phone[0],
	}
	if name, ok := m.Account["username"]; ok {
		ret["username"] = name
		ret["password"] = name
	}
	return ret
}
func (trigger *souhaohuoTrigger) BindUser(m *models.Merchant, mobile int, password string) error {
	return nil
}
