package eventtrigger

import (
	"errors"
	"fmt"
	"git.paihang8.com/lib/goutils/sites/b2b168"
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	_ "gitlab.com/all_publish/api/internal/business/merchanter"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/convert"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/gorm"
	"math/rand"
	"strconv"
	"strings"
	"time"
)

type bafangTrigger struct {
}

func init() {
	bafang := new(bafangTrigger)
	models.RegisterTrigger(models.PlatformBafang, bafang)
}
func (trigger *bafangTrigger) ValidateCompany(c models.Company) error {
	if c.Get<PERSON>reaID() == 0 {
		return errors.New("八方资源:公司地址没编辑，不能注册")
	}
	if c.GetCatID() == 0 {
		return errors.New("八方资源:公司分类没编辑，不能注册")
	}
	p := b2b168.CompanyReq{
		CommonParams: b2b168.CommonParams{
			AgentPassword: "13r32x",
			CompanyId:     0,
		},
		UserName:    "test",
		Password:    "123456",
		Name:        c.Name,
		LinkMan:     c.ContactName,
		Mobile:      c.Phone[0],
		Class:       82002004,
		Region:      1027900160,
		Addr:        c.Address,
		MainProduct: c.MainProduct,
	}
	if err := models.Valiate(p); err != nil {
		return errors.New("八方资源:" + err.Error())
	}
	return nil
}

func (trigger *bafangTrigger) OnCompanyEdit(c *models.Company) {
	if item, err := services.Merchant.GetByCompanyId(c.ID, models.PlatformBafang); err == nil {
		if c.GetAreaID() > 0 && c.GetCatID() > 0 {
			if item.TargetCompanyID == 0 {
				if u, e := services.NewUserService().GetUserByCompanyId(c.ID); e == nil {
					u.Company = *c
					trigger.AutoReg(u, item)
				}
			}
			trigger.AutoFlow(item, c, true)
		}
	}
}

func (trigger *bafangTrigger) AutoReg(u *models.User, merchant *models.Merchant) {
	bafangClient, ok := models.GetMerchant(models.PlatformBafang)
	if !ok {
		return
	}
	if err := trigger.ValidateCompany(u.Company); err != nil {
		return
	}
	if data, err := bafangClient.Reg(u.Company); err != nil {
		merchant.RegFailedReason = err.Error()
		services.Merchant.Save(merchant)
	} else {
		c := data.(b2b168.CompanyData)
		merchant.TargetCompanyID, _ = strconv.Atoi(c.ID)
		merchant.CompanySite = c.URL
		merchant.RegFailedReason = ""
		merchant.Account = bafangClient.Account()
		if u.Company.License == "" {
			merchant.CertStatus = dbtypes.CertStatusNeedRegNo
		} else {
			merchant.CertStatus = dbtypes.CertStatusNeedSubmit
		}
		services.Merchant.Save(merchant)
	}
}

func (trigger *bafangTrigger) OnUserLogin(u *models.User, config publisher.PlatformConfig, client models.Merchanter) {
	trigger.OnFirstLogin(u, config, client)
	bafangClient, ok := models.GetMerchant(models.PlatformBafang)
	if !ok {
		return
	}
	bafangClient.RenewWhenNeeded(u.CompanyID)
}

func (trigger *bafangTrigger) OnFirstLogin(u *models.User, config publisher.PlatformConfig, client models.Merchanter) {
	bafangClient, ok := models.GetMerchant(models.PlatformBafang)
	if !ok {
		return
	}
	services.NewUserPlatformService().UpdateFunction(models.UserPlatform{CompanyID: u.CompanyID,
		Platform: models.PlatformBafang, Funcname: models.FuncnamePost, Status: models.UserPlatformStatusOpened,
		ExpireTime: u.PostExpireTime})
	if item, err := services.Merchant.GetByCompanyId(u.CompanyID, models.PlatformBafang); err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		var merchantItem models.Merchant
		merchantItem.CreatedAt = dbtypes.SHNow()
		merchantItem.UpdatedAt = dbtypes.SHNow()
		merchantItem.CompanyID = u.CompanyID
		merchantItem.Total = config.MaxItem
		merchantItem.BaseCnt = config.BaseItem
		merchantItem.RenewAddCnt = config.BaseItem

		if u.Company.Site != "" {
			merchantItem.CompanySite = u.Company.Site
		}
		merchantItem.Account = bafangClient.Account()
		merchantItem.Pause = false
		merchantItem.PubCount = 20
		merchantItem.PubPerCount = 3
		merchantItem.DailyPubProducts = 2
		merchantItem.AutoPub = true
		now, _ := time.ParseInLocation("15:04:05", fmt.Sprintf("%02d:%02d:00", rand.Intn(14)+1, rand.Intn(59)), dbtypes.SHLocation)
		autoPubTime := dbtypes.MysqlTime{now}
		merchantItem.AutoPubTime = &autoPubTime
		name := bafangClient.Name()
		merchantItem.Name = &name
		merchantItem.Contact = u.Company.ContactName
		merchantItem.ContactPhone = u.Phone
		merchantItem.PlatForm = models.PlatformBafang
		if err := services.Merchant.Create(&merchantItem); err == nil {
			trigger.AutoReg(u, &merchantItem)
		}

	} else {
		item.Total = config.MaxItem
		item.BaseCnt = config.BaseItem
		item.RenewAddCnt = config.BaseItem
		services.Merchant.Updates(item, "total", "base_cnt", "renew_add_cnt")
		if item.TargetCompanyID == 0 {
			//需要注册
			trigger.AutoReg(u, item)
		} else {
			c, _ := services.NewCompanyService().Find(item.CompanyID)
			trigger.AutoFlow(item, c)
		}
	}
}

func (trigger *bafangTrigger) AutoFlow(merchant *models.Merchant, c *models.Company, userTrigger ...bool) bool {
	isUserTrigger := false
	if len(userTrigger) > 0 && userTrigger[0] {
		isUserTrigger = true
	}
	if merchant.TargetCompanyID == 0 {
		return false
	}
	var ext merchants.BafangExt
	err := ext.Init(merchant.Ext)
	if err != nil {
		return false
	}
	if merchant.CertStatus == dbtypes.CertStatusPassed && ext.Annonce.Status == dbtypes.CertStatusPassed {
		return true
	}
	client, _ := models.GetMerchant(models.PlatformBafang)
	client.LoginBy(merchant.Account)
	var certData merchants.CertData
	err = certData.Init(merchant.CertData)

	if merchant.CertStatus != dbtypes.CertStatusPassed {
		if merchant.CertStatus == dbtypes.CertStatusSubmitted || merchant.CertStatus == dbtypes.CertStatusSubmitFailed {
			var certID = certData.Id
			if certID > 0 {
				if data, err := client.GetCert(merchant.TargetCompanyID, certID); err != nil {
					certData.Reason = err.Error()
				} else if status, ok := data.(b2b168.GetData); ok {
					parts := strings.SplitN(status.Check, " ", 2)
					if len(parts) == 2 {
						status.Check = parts[0]
						status.CheckReason = parts[1]
					}
					certData.Reason = status.CheckReason
					certData.Check = status.Check
					var cstatus dbtypes.CertStatus
					switch status.Check {
					case "审核通过":
						cstatus = dbtypes.CertStatusPassed
					case "修改审核中":
						fallthrough
					case "新增审核中":
						cstatus = dbtypes.CertStatusSubmitted
					case "审核拒绝":
						cstatus = dbtypes.CertStatusRejected
					}
					merchant.CertStatus = cstatus
				}
				merchant.SetCertData(certData)
				services.Merchant.Save(merchant)
				if merchant.CertStatus == dbtypes.CertStatusPassed && ext.Annonce.Status == dbtypes.CertStatusPassed {
					return true
				}

			}
		} else {
			if isUserTrigger && merchant.CertStatus != dbtypes.CertStatusPassed {
				if data, err := client.SubmitCert(*c, "营业执照", b2b168.CertTypeCom); err != nil {
					if !strings.Contains(err.Error(), "已审核") {
						certData.Reason = err.Error()
						merchant.CertStatus = dbtypes.CertStatusSubmitFailed
					} else {
						certData.Reason = ""
						merchant.CertStatus = dbtypes.CertStatusPassed
					}
					certData.License = c.License
				} else if status, ok := data.(b2b168.CertData); ok {
					certData.Id = convert.Str(status.ID).MustInt()
					certData.License = c.License
					merchant.CertStatus = dbtypes.CertStatusSubmitted
				}
				merchant.SetCertData(certData)
				services.Merchant.Save(merchant)
			}
		}
	}

	if ext.Annonce.Status != dbtypes.CertStatusPassed {
		if ext.Annonce.Status == dbtypes.CertStatusSubmitted || merchant.CertStatus == dbtypes.CertStatusSubmitFailed {
			if id, err := strconv.Atoi(ext.Annonce.Id); err == nil {
				if data, err := client.GetCert(merchant.TargetCompanyID, id); err != nil {
					ext.Annonce.Reason = err.Error()
				} else if status, ok := data.(b2b168.GetData); ok {
					parts := strings.SplitN(status.Check, " ", 2)
					if len(parts) == 2 {
						status.Check = parts[0]
						status.CheckReason = parts[1]
					}
					ext.Annonce.Reason = status.CheckReason
					ext.Annonce.Check = status.Check
					var cstatus dbtypes.CertStatus
					switch status.Check {
					case "审核通过":
						cstatus = dbtypes.CertStatusPassed
					case "新增审核中":
					case "修改审核中":
						cstatus = dbtypes.CertStatusSubmitted
					case "审核拒绝":
						cstatus = dbtypes.CertStatusRejected
					}
					ext.Annonce.Status = cstatus
				}
				merchant.SetExt(ext)
				services.Merchant.Save(merchant)
				if merchant.CertStatus == dbtypes.CertStatusPassed && ext.Annonce.Status == dbtypes.CertStatusPassed {
					return true
				}

			}
		} else {
			if ext.Annonce.Pic != "" && isUserTrigger {
				c.License = ext.Annonce.Pic
				if data, err := client.SubmitCert(*c, "企业声明", b2b168.CertTypeComAnnounce); err != nil {
					ext.Annonce.Reason = err.Error()
					ext.Annonce.Status = dbtypes.CertStatusSubmitFailed
					trigger.AutoFlow(merchant, c, userTrigger...)
				} else if status, ok := data.(b2b168.CertData); ok {
					ext.Annonce.Id = status.ID
					ext.Annonce.Status = dbtypes.CertStatusSubmitted
				}
				merchant.SetExt(ext)
				services.Merchant.Save(merchant)
			}
		}
	}
	return false
}

func (trigger *bafangTrigger) CalSteps(m *models.Merchant, c models.Company) (s schemas.MerchantStep) {
	s.Name = models.PlatFormName(m.PlatForm)
	var certData merchants.CertData
	certData.Init(m.CertData)
	// 检查公司资料
	s.Steps = []schemas.Step{
		{
			Text:   "提交公司资料",
			Action: dbtypes.ActionUploadLincense,
			Status: schemas.StepStatusTodo,
		},
		{
			Text:   "待审核",
			Action: dbtypes.ActionNone,
			Status: schemas.StepStatusUnReach,
		},
		{
			Text:   "审核结果",
			Action: dbtypes.ActionNone,
			Status: schemas.StepStatusUnReach,
		},
	}
	if err := trigger.ValidateCompany(c); err == nil {
		s.Steps[0].Status = schemas.StepStatusDone
	} else {
		s.Steps[0].Status = schemas.StepStatusDone
		s.Steps[1].Status = schemas.StepStatusDone
		s.Steps[2].Status = schemas.StepStatusTodo
		s.Steps[2].Reason = err.Error()
		s.Steps[2].Action = dbtypes.ActionUploadLincense
		return
	}
	if m.TargetCompanyID > 0 {
		s.Steps[1].Status = schemas.StepStatusTodo
		switch m.CertStatus {
		case dbtypes.CertStatusRejected:
			s.Steps[2].Text = "审核未通过"
			s.Steps[2].Reason = certData.Reason
			s.Steps[2].Status = schemas.StepStatusTodo
		case dbtypes.CertStatusPassed:
			s.Steps[2].Text = "已开通"
			s.Steps[2].Action = dbtypes.ActionNone
			s.Steps[1].Status = schemas.StepStatusDone
			s.Steps[2].Status = schemas.StepStatusDone
		}

	} else if m.RegFailedReason != "" {
		s.Steps[1].Status = schemas.StepStatusDone
		s.Steps[2].Status = schemas.StepStatusTodo
		s.Steps[2].Text = "审核未通过"
		s.Steps[2].Action = dbtypes.ActionUploadLincense
		s.Steps[2].Reason = m.RegFailedReason
	}
	return
}

func (trigger *bafangTrigger) FormatAccount(m *models.Merchant) (ret map[string]interface{}) {
	ret = map[string]interface{}{
		"username": "",
		"password": "",
	}
	if _, ok := m.Account["username"]; ok {
		ret = m.Account
	}
	return ret
}

func (trigger *bafangTrigger) BindUser(m *models.Merchant, mobile int, password string) error {
	return nil
}
