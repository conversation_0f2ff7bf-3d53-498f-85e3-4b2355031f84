package eventtrigger

import (
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/test"
	"testing"
)

func TestKuyisoTrigger_BindUserByPhone(t *testing.T) {
	//t.SkipNow()
	test.InitTest(t)
	trigger, _ := models.GetTrigger(models.PlatformKuyiso)
	if err := trigger.(*kuyisoTrigger).BindUserByPhone(18717735710, 18717735710, "welcome123"); err != nil {
		t.Fatal(err)
	} else {
		t.Log("ok")
	}
}
