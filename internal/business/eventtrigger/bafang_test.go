package eventtrigger

import (
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/test"
	"testing"
)

func TestBafangTrigger_AutoReg(t *testing.T) {
	test.InitTest(t)
	if client, ok := models.GetTrigger(models.PlatformBafang); ok {
		u, _ := services.NewUserService().GetUserByOem(228540)
		c, _ := services.NewCompanyService().Find(u.CompanyID)
		u.Company = *c
		merchant, _ := services.Merchant.GetByCompanyId(u.CompanyID, models.PlatformBafang)
		client.AutoReg(u, merchant)
	}
}

func TestBafangTrigger_AutoFlow(t *testing.T) {
	test.InitTest(t)
	if client, ok := models.GetTrigger(models.PlatformBafang); ok {
		u, _ := services.NewUserService().GetUserByPhone(15106813883)
		c, _ := services.NewCompanyService().Find(u.CompanyID)
		u.Company = *c
		merchant, _ := services.Merchant.GetByCompanyId(u.CompanyID, models.PlatformBafang)
		client.AutoFlow(merchant, c, true)
	}
}
