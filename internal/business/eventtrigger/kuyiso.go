package eventtrigger

import (
	"errors"
	"fmt"
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"git.paihang8.com/lib/goutils/sites/kuyiso"
	"gitlab.com/all_publish/api/configs"
	_ "gitlab.com/all_publish/api/internal/business/merchanter"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/internal/services/cat"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/gorm"
	"math/rand"
	"strconv"
	"time"
)

type kuyisoTrigger struct {
}

func init() {
	kuyiso := new(kuyisoTrigger)
	models.RegisterTrigger(models.PlatformKuyiso, kuyiso)
}

func (trigger *kuyisoTrigger) ValidateCompany(c models.Company) error {
	mobile, _ := strconv.Atoi(c.Phone[0])
	p := kuyiso.RegCompanyReq{
		UID:         1,
		Coname:      c.Name,
		Realname:    c.ContactName,
		Con_mobile:  mobile,
		Categoryid:  4,
		Areaid:      4,
		Mainproduct: c.MainProduct,
		Description: c.Introduce,
	}
	if err := models.Valiate(p); err != nil {
		return errors.New("酷易搜:" + err.Error())
	}
	return nil
}

func (trigger *kuyisoTrigger) OnCompanyEdit(c *models.Company) {
	if item, err := services.Merchant.GetByCompanyId(c.ID, models.PlatformKuyiso); err == nil {
		if c.GetAreaID() > 0 && c.GetCatID() > 0 {
			if item.TargetCompanyID == 0 {
				if u, e := services.NewUserService().GetUserByCompanyId(c.ID); e == nil {
					u.Company = *c
					trigger.AutoReg(u, item)
				}
			} else {
				var account merchants.KuyisoAccount
				account.Init(item.Account)
				client, _ := models.GetMerchant(models.PlatformKuyiso)
				//同步公司资料
				shouldCallModifyCompany := false
				for _, v := range c.AuditingFields {
					modifyCompanyKeys := []string{"cate", "phone", "name", "main_brand", "introduce", "contact_name", "areaids"}
					for _, vv := range modifyCompanyKeys {
						if v == vv {
							shouldCallModifyCompany = true
							break
						}
					}
				}
				if shouldCallModifyCompany {
					params := kuyiso.ModifyCompanyReq{
						UID: account.Uid,
					}.WithOption("coname", c.Name).WithOption("mainproduct", c.MainProduct).WithOption("description", c.Introduce)
					classmapping, err := cat.NewCatService().GetMapping(uint64(c.GetCatID()), models.PlatformKuyiso)
					if err == nil {
						params = params.WithOption("categoryid", classmapping.TargetCatID)
					}
					areamapping, err := services.Area.GetMapping(uint64(c.GetAreaID()), models.PlatformKuyiso)
					if err == nil {
						params = params.WithOption("areaid", areamapping.TargetAreaID)
					}
					if c.ContactName != "" {
						params = params.WithOption("realname", c.ContactName)
					}
					if len(c.Phone) > 0 {
						params = params.WithOption("con_mobile", c.Phone[0])
					}
					if _, err := client.LoginBy(item.Account); err == nil {
						client.GetApi().(kuyiso.PublishApi).ModifyCompany(params)
					}
				}
			}
			trigger.AutoFlow(item, c, true)
		}
	}
}

func (trigger *kuyisoTrigger) AutoReg(u *models.User, merchant *models.Merchant) {
	kuyisoClient, ok := models.GetMerchant(models.PlatformKuyiso)
	if !ok {
		return
	}
	if err := trigger.ValidateCompany(u.Company); err != nil {
		return
	}
	kuyisoClient.LoginBy(merchant.Account)
	if _, err := kuyisoClient.Reg(u.Company); err != nil {
		merchant.RegFailedReason = err.Error()
		var account merchants.KuyisoAccount
		account.Init(kuyisoClient.Account())
		merchant.SetAccount(account)
		services.Merchant.Save(merchant)
	} else {
		var account merchants.KuyisoAccount
		account.Init(merchant.Account)
		merchant.CompanySite = "http://www.kuyiso.com"
		merchant.RegFailedReason = ""
		merchant.Account = kuyisoClient.Account()
		merchant.Account["is_personal"] = account.IsPersonal
		merchant.TargetCompanyID = merchant.Account["id"].(int)
		if u.Company.License == "" {
			merchant.CertStatus = dbtypes.CertStatusNeedRegNo
		} else {
			merchant.CertStatus = dbtypes.CertStatusNeedSubmit
		}
		services.Merchant.Save(merchant)
	}
}

func (trigger *kuyisoTrigger) OnUserLogin(u *models.User, config publisher.PlatformConfig, client models.Merchanter) {
	trigger.OnFirstLogin(u, config, client)
	client, ok := models.GetMerchant(models.PlatformKuyiso)
	if !ok {
		return
	}
	client.RenewWhenNeeded(u.CompanyID)
}

func (trigger *kuyisoTrigger) OnFirstLogin(u *models.User, config publisher.PlatformConfig, client models.Merchanter) {
	kuyisoClient, ok := models.GetMerchant(models.PlatformKuyiso)
	if !ok {
		return
	}
	services.NewUserPlatformService().UpdateFunction(models.UserPlatform{CompanyID: u.CompanyID,
		Platform: models.PlatformKuyiso, Funcname: models.FuncnamePost, Status: models.UserPlatformStatusOpened,
		ExpireTime: u.PostExpireTime})
	if item, err := services.Merchant.GetByCompanyId(u.CompanyID, models.PlatformKuyiso); err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		var merchantItem models.Merchant
		merchantItem.CreatedAt = dbtypes.SHNow()
		merchantItem.UpdatedAt = dbtypes.SHNow()
		merchantItem.CompanyID = u.CompanyID
		if u.Company.Site != "" {
			merchantItem.CompanySite = u.Company.Site
		}
		merchantItem.Account = kuyisoClient.Account()
		merchantItem.Pause = false
		merchantItem.PubCount = 20
		merchantItem.PubPerCount = 3
		merchantItem.DailyPubProducts = 2
		merchantItem.AutoPub = true
		merchantItem.Total = config.MaxItem
		merchantItem.BaseCnt = config.BaseItem
		merchantItem.RenewAddCnt = config.BaseItem
		now, _ := time.ParseInLocation("15:04:05", fmt.Sprintf("%02d:%02d:00", rand.Intn(14)+1, rand.Intn(59)), dbtypes.SHLocation)
		autoPubTime := dbtypes.MysqlTime{now}
		merchantItem.AutoPubTime = &autoPubTime
		name := kuyisoClient.Name()
		merchantItem.Name = &name
		merchantItem.Contact = u.Company.ContactName
		merchantItem.ContactPhone = u.Phone
		merchantItem.PlatForm = models.PlatformKuyiso
		trigger.updateAccount(&merchantItem, config)
		if err := services.Merchant.Create(&merchantItem); err == nil {
			trigger.AutoReg(u, &merchantItem)
		}

	} else if item.TargetCompanyID == 0 {
		//需要注册
		item.Total = config.MaxItem
		item.BaseCnt = config.BaseItem
		item.RenewAddCnt = config.BaseItem
		trigger.updateAccount(item, config)
		services.Merchant.Save(item)
		trigger.AutoReg(u, item)
	} else {
		c, _ := services.NewCompanyService().Find(item.CompanyID)
		item.Total = config.MaxItem
		item.BaseCnt = config.BaseItem
		item.RenewAddCnt = config.BaseItem
		trigger.updateAccount(item, config)
		services.Merchant.Save(item)
		trigger.AutoFlow(item, c)
	}
}

func (trigger *kuyisoTrigger) updateAccount(merchant *models.Merchant, config publisher.PlatformConfig) {
	var account merchants.KuyisoAccount
	account.Init(merchant.Account)
	if config.Utype == "personal" {
		account.IsPersonal = true
		merchant.SetAccount(account)
	}
}

func (trigger *kuyisoTrigger) AutoFlow(merchant *models.Merchant, c *models.Company, userTrigger ...bool) bool {
	isUserTrigger := false
	if len(userTrigger) > 0 && userTrigger[0] {
		isUserTrigger = true
	}
	var account merchants.KuyisoAccount
	var certData merchants.CertData
	account.Init(merchant.Account)
	certData.Init(merchant.CertData)
	if merchant.TargetCompanyID == 0 {
		return false
	}
	if merchant.CertStatus == dbtypes.CertStatusPassed {
		return true
	}
	client, _ := models.GetMerchant(models.PlatformKuyiso)
	client.LoginBy(merchant.Account)
	if merchant.CertData == nil {
		merchant.CertData = map[string]interface{}{}
	}

	if merchant.CertStatus == dbtypes.CertStatusSubmitted || merchant.CertStatus == dbtypes.CertStatusSubmitFailed {
		if data, err := client.GetCert(merchant.TargetCompanyID, 0); err != nil {
			certData.Reason = err.Error()
			merchant.CertStatus = dbtypes.CertStatusNeedSubmit
			trigger.AutoFlow(merchant, c, userTrigger...)
		} else if status, ok := data.(*kuyiso.GetCertData); ok {
			var cstatus dbtypes.CertStatus
			switch status.Status {
			case 0:
				cstatus = dbtypes.CertStatusSubmitted
			case 1:
				cstatus = dbtypes.CertStatusPassed
			case 2:
				if status.Rejectreason != "" {
					certData.Reason = status.Rejectreason
				}
				cstatus = dbtypes.CertStatusRejected
			}
			merchant.CertStatus = cstatus
		}
		merchant.SetCertData(certData)
		services.Merchant.Save(merchant)
		if merchant.CertStatus == dbtypes.CertStatusPassed {
			return true
		}

	} else {
		//preLicense := certData.License
		if isUserTrigger && merchant.CertStatus != dbtypes.CertStatusPassed {
			if data, err := client.SubmitCert(*c, "营业执照"); err != nil {
				merchant.CertStatus = dbtypes.CertStatusSubmitFailed
				certData.Reason = err.Error()
				certData.License = c.License
			} else if status, ok := data.(*kuyiso.SubCertData); ok {
				certData.Id = dbtypes.MustInt(status.Chengxinid)
				certData.License = c.License
				merchant.CertStatus = dbtypes.CertStatusSubmitted
			}
			merchant.SetCertData(certData)
			services.Merchant.Save(merchant)
		}
	}
	return false
}

func (trigger *kuyisoTrigger) CalSteps(m *models.Merchant, c models.Company) (s schemas.MerchantStep) {
	s.Name = models.PlatFormName(m.PlatForm)
	// 检查公司资料
	s.Steps = []schemas.Step{
		{
			Text:   "提交公司资料",
			Action: dbtypes.ActionUploadLincense,
			Status: schemas.StepStatusTodo,
		},
		{
			Text:   "待审核",
			Action: dbtypes.ActionNone,
			Status: schemas.StepStatusUnReach,
		},
		{
			Text:   "审核结果",
			Action: dbtypes.ActionNone,
			Status: schemas.StepStatusUnReach,
		},
	}
	if err := trigger.ValidateCompany(c); err == nil {
		s.Steps[0].Status = schemas.StepStatusDone
	} else {
		s.Steps[0].Status = schemas.StepStatusDone
		s.Steps[1].Status = schemas.StepStatusDone
		s.Steps[2].Status = schemas.StepStatusTodo
		s.Steps[2].Reason = err.Error()
		s.Steps[2].Action = dbtypes.ActionUploadLincense
		return
	}
	if m.TargetCompanyID > 0 {
		s.Steps[1].Status = schemas.StepStatusTodo
		switch m.CertStatus {
		case dbtypes.CertStatusRejected:
			s.Steps[1].Status = schemas.StepStatusDone
			s.Steps[2].Text = "审核未通过"
			s.Steps[2].Reason = m.CertData["reason"].(string)
			s.Steps[2].Status = schemas.StepStatusTodo
			s.Steps[2].Action = dbtypes.ActionUploadLincense
		case dbtypes.CertStatusPassed:
			s.Steps[1].Status = schemas.StepStatusDone
			s.Steps[2].Text = "已开通"
			s.Steps[2].Action = dbtypes.ActionNone
			s.Steps[2].Status = schemas.StepStatusDone
		}

	} else if m.RegFailedReason != "" {
		s.Steps[1].Status = schemas.StepStatusDone
		s.Steps[2].Status = schemas.StepStatusTodo
		s.Steps[2].Text = "审核未通过"
		s.Steps[2].Action = dbtypes.ActionUploadLincense
		s.Steps[2].Reason = m.RegFailedReason
	}
	return
}

func (trigger *kuyisoTrigger) FormatAccount(m *models.Merchant) (ret map[string]interface{}) {
	ret = map[string]interface{}{
		"username": "",
		"password": "",
	}
	if _, ok := m.Account["mobile"]; ok {
		ret = m.Account
		ret["username"] = ret["mobile"]
	}
	return ret
}

func (trigger *kuyisoTrigger) BindUser(m *models.Merchant, mobile int, password string) error {
	client, ok := models.GetMerchant(models.PlatformKuyiso)
	if !ok {
		return errors.New("酷易搜平台未开通")
	}
	//if m.TargetCompanyID > 0 {
	//	return errors.New("已绑定，请勿重复绑定")
	//}
	if item, err := services.Merchant.GetByThirdAccount("mobile", mobile, models.PlatformKuyiso); err == nil && item != nil {
		return errors.New("该账号已绑定，请换一个账号")
	}
	if userData, err := client.GetApi().(kuyiso.PublishApi).Related(mobile, password); err != nil {
		return configs.ErrorWithCode(configs.ErrCodeLoginFailed, err)
	} else {
		var account merchants.KuyisoAccount
		account.Uid = userData.UID
		account.Mobile = strconv.Itoa(mobile)
		account.Password = password
		client.LoginBy(account.ToMap())
		certed := false
		// 个人认证
		if cert, err := client.GetApi().(kuyiso.PublishApi).GetPersonCert(kuyiso.GetCertReq{
			UID:  userData.UID,
			Type: kuyiso.CertTypePerson,
		}); err == nil {
			if cert.Status == 1 {
				certed = true
				account.IsPersonal = true
			}
		}
		// 公司认证
		if cert, err := client.GetApi().(kuyiso.PublishApi).GetCompanyCert(kuyiso.GetCertReq{
			UID:  userData.UID,
			Type: kuyiso.CertTypeCompany,
		}); err == nil {
			if cert.Status == 1 {
				certed = true
				account.IsPersonal = false
			}
		}
		if !certed {
			return configs.ErrorWithCode(configs.ErrCodeNotCerted, errors.New("未认证"))
		}

		if comData, err := client.GetApi().(kuyiso.PublishApi).GetCompanyInfo(account.Uid); err != nil {
			//没有公司信息，就用个人信息
			if com, err := services.NewCompanyService().Find(m.CompanyID); err == nil {
				changed := false
				if com.ContactName == "" && userData.Nickname != "" {
					com.ContactName = userData.Nickname
					changed = true
				}
				if len(com.Phone) == 0 {
					com.Phone = []string{strconv.Itoa(mobile)}
					changed = true
				}
				if com.Name == "" && userData.Nickname != "" {
					com.Name = userData.Nickname
					changed = true
				}
				if changed {
					services.NewCompanyService().Save(com)
				}
			}
		} else {
			account.Id = comData.Cid
			if com, err := services.NewCompanyService().Find(m.CompanyID); err == nil {
				changed := false
				if com.ContactName == "" && comData.Realname != "" {
					com.ContactName = comData.Realname
					changed = true
				}
				if len(com.Phone) == 0 && comData.ConMobile != "" {
					com.Phone = []string{comData.ConMobile}
					changed = true
				}
				if com.Name == "" && comData.Coname != "" {
					com.Name = comData.Coname
					changed = true
				}
				if changed {
					services.NewCompanyService().Save(com)
				}
			}
		}
		m.SetAccount(account)
		m.CertStatus = dbtypes.CertStatusPassed
		m.TargetCompanyID = account.Id
		m.RegFailedReason = ""
		return services.Merchant.Save(m)
	}
}

func (trigger *kuyisoTrigger) BindUserByPhone(phone int, mobile int, password string) error {
	if user, err := services.NewUserService().GetUserByPhone(phone); err != nil {
		return err
	} else {
		if m, err := services.Merchant.GetByCompanyId(user.CompanyID, models.PlatformKuyiso); err != nil {
			return err
		} else {
			return trigger.BindUser(m, mobile, password)

		}
	}
}
