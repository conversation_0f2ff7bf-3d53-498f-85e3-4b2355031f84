package eventtrigger

import (
	"errors"
	"fmt"
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/gorm"
	"math/rand"
	"time"
)

type baixingTrigger struct {
}

func init() {
	baixing := new(baixingTrigger)
	models.RegisterTrigger(models.PlatformBaixing, baixing)
}

func (trigger *baixingTrigger) OnCompanyEdit(c *models.Company) {

}

func (trigger *baixingTrigger) AutoReg(u *models.User, merchant *models.Merchant) {

}

func (trigger *baixingTrigger) OnUserLogin(u *models.User, config publisher.PlatformConfig, mClient models.Merchanter) {
	trigger.OnFirstLogin(u, config, mClient)
	client, ok := models.GetMerchant(models.PlatformBaixing)
	if !ok {
		return
	}
	client.RenewWhenNeeded(u.CompanyID)
}

func (trigger *baixingTrigger) OnFirstLogin(u *models.User, config publisher.PlatformConfig, mClient models.Merchanter) {
	client, ok := models.GetMerchant(models.PlatformBaixing)
	if !ok {
		return
	}
	services.NewUserPlatformService().UpdateFunction(models.UserPlatform{CompanyID: u.CompanyID,
		Platform: models.PlatformBaixing, Funcname: models.FuncnamePost, Status: models.UserPlatformStatusOpened,
		ExpireTime: u.PostExpireTime})
	if item, err := services.Merchant.GetByCompanyId(u.CompanyID, models.PlatformBaixing); err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		var merchantItem models.Merchant
		merchantItem.CreatedAt = dbtypes.SHNow()
		merchantItem.UpdatedAt = dbtypes.SHNow()
		merchantItem.CompanyID = u.CompanyID
		merchantItem.Account = client.Account()
		merchantItem.Pause = false
		merchantItem.Total = config.MaxItem
		merchantItem.BaseCnt = config.BaseItem
		merchantItem.RenewAddCnt = config.BaseItem
		merchantItem.CertStatus = dbtypes.CertStatusPassed
		merchantItem.DailyPubProducts = 2
		merchantItem.CompanySite = "https://www.baixing.com/"
		merchantItem.PubCount = 20
		merchantItem.PubPerCount = 3
		merchantItem.AutoPub = true
		now, _ := time.ParseInLocation("15:04:05", fmt.Sprintf("%02d:%02d:00", rand.Intn(14)+1, rand.Intn(59)), dbtypes.SHLocation)
		autoPubTime := dbtypes.MysqlTime{now}
		merchantItem.AutoPubTime = &autoPubTime
		name := client.Name()
		merchantItem.Name = &name
		merchantItem.Contact = u.Company.ContactName
		merchantItem.ContactPhone = u.Phone
		merchantItem.PlatForm = models.PlatformBaixing
		if err := services.Merchant.Create(&merchantItem); err == nil {
			trigger.AutoReg(u, &merchantItem)
		}

	} else {
		item.Total = config.MaxItem
		item.BaseCnt = config.BaseItem
		item.RenewAddCnt = config.BaseItem
		services.Merchant.Updates(item, "total", "base_cnt", "renew_add_cnt")
		var account merchants.LiebiaoAccount
		account.Init(item.Account)
		if account.Id == 0 {
			//需要注册
			trigger.AutoReg(u, item)
		}
		c, _ := services.NewCompanyService().Find(item.CompanyID)
		trigger.AutoFlow(item, c)
	}
}

func (trigger *baixingTrigger) AutoFlow(merchant *models.Merchant, c *models.Company, userTrigger ...bool) bool {
	return true
}
func (trigger *baixingTrigger) ValidateCompany(c models.Company) error {
	return nil
}
func (trigger *baixingTrigger) CalSteps(m *models.Merchant, c models.Company) (s schemas.MerchantStep) {
	s.Name = models.PlatFormName(m.PlatForm)
	s.Steps = append(s.Steps, schemas.Step{
		Text:   "已开通",
		Action: dbtypes.ActionNone,
		Status: schemas.StepStatusDone,
	})
	return
}

func (trigger *baixingTrigger) FormatAccount(m *models.Merchant) (ret map[string]interface{}) {
	ret = map[string]interface{}{
		"username": m.Company.Phone[0],
		"password": "",
	}
	return ret
}

func (trigger *baixingTrigger) BindUser(m *models.Merchant, mobile int, password string) error {
	return nil
}
