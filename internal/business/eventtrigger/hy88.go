package eventtrigger

import (
	"errors"
	"fmt"
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/gorm"
	"math/rand"
	"strings"
	"time"
)

type hy88Trigger struct {
}

func init() {
	hy88 := new(hy88Trigger)
	models.RegisterTrigger(models.PlatformHy88, hy88)
}

func (trigger *hy88Trigger) OnCompanyEdit(c *models.Company) {

}

func (trigger *hy88Trigger) AutoReg(u *models.User, merchant *models.Merchant) {

}

func (trigger *hy88Trigger) OnUserLogin(u *models.User, config publisher.PlatformConfig, mClient models.Merchanter) {
	if merchantItem, err := services.Merchant.GetByCompanyId(u.CompanyID, models.PlatformHy88); !errors.Is(err, gorm.ErrRecordNotFound) {
		merchantItem.Account = mClient.Account()
		merchantItem.TargetCompanyID = int(u.Company.ID)
		//merchantItem.Total = config.MaxItem
		merchantItem.Total = 0
		if dbtypes.MerChantTotalIndicateClosed == merchantItem.Total {
			merchantItem.AutoPub = false
			merchantItem.Pause = true
			merchantItem.PauseReason = "未开通"
			merchantItem.EnablePostProduct = false
			merchantItem.ProductPauseReason = "未开通"
		}
		merchantItem.BaseCnt = config.BaseItem
		merchantItem.RenewAddCnt = config.BaseItem
		if u.Company.Site != "" {
			merchantItem.CompanySite = u.Company.Site
		}
		if merchantItem.Pause && strings.Contains(merchantItem.PauseReason, "密码不正确") {
			merchantItem.Pause = false
			merchantItem.AutoPub = true
			merchantItem.PauseReason = ""
		}
		services.Merchant.Updates(merchantItem, "pause", "auto_pub", "pause_reason", "company_site", "account", "base_cnt", "renew_add_cnt")
	} else {
		if merchantItem.CompanySite != u.Company.Site {
			merchantItem.CompanySite = u.Company.Site
			services.Merchant.Save(merchantItem)
		}

	}
	services.NewUserPlatformService().UpdateFunction(models.UserPlatform{CompanyID: u.CompanyID,
		Platform: models.PlatformHy88, Funcname: models.FuncnamePost, Status: models.UserPlatformStatusOpened,
		ExpireTime: u.PostExpireTime})
	services.NewUserPlatformService().UpdateFunction(models.UserPlatform{CompanyID: u.CompanyID,
		Platform: models.PlatformHy88, Funcname: models.FuncnameRank, Status: models.UserPlatformStatusOpened,
		ExpireTime: u.RankExpireTime})
	services.NewUserPlatformService().UpdateFunction(models.UserPlatform{CompanyID: u.CompanyID,
		Platform: models.PlatformHy88, Funcname: models.FuncnameSeek, Status: models.UserPlatformStatusOpened,
		ExpireTime: u.SeekExpireTime})
	if sts, err := services.NewUserStatService().GetStatByCompanyId(u.CompanyID); err == nil {
		sts.DigKeywordLimit = u.DigKeywordLimit
		sts.DigMaterialsLimit = u.DigMaterialsLimit
		services.NewUserStatService().Update(sts)
	}
}

func (trigger *hy88Trigger) OnFirstLogin(u *models.User, config publisher.PlatformConfig, mClient models.Merchanter) {
	// 首次登陆需要插入商户表， 需要插入user_platform表
	var merchantItem models.Merchant
	merchantItem.CreatedAt = dbtypes.SHNow()
	merchantItem.UpdatedAt = dbtypes.SHNow()
	merchantItem.CompanyID = u.CompanyID
	merchantItem.CertStatus = dbtypes.CertStatusPassed
	//merchantItem.Total = config.MaxItem
	merchantItem.Total = 0
	if dbtypes.MerChantTotalIndicateClosed == merchantItem.Total {
		merchantItem.AutoPub = false
		merchantItem.Pause = true
		merchantItem.PauseReason = "未开通"
		merchantItem.EnablePostProduct = false
		merchantItem.ProductPauseReason = "未开通"
	}
	merchantItem.BaseCnt = config.BaseItem
	merchantItem.RenewAddCnt = config.BaseItem
	if u.Company.Site != "" {
		merchantItem.CompanySite = u.Company.Site
	}
	merchantItem.Account = mClient.Account()
	merchantItem.Pause = false
	merchantItem.PubCount = 450
	merchantItem.PubPerCount = 15
	merchantItem.DailyPubProducts = 2
	merchantItem.AutoPub = true
	now, _ := time.ParseInLocation("15:04:05", fmt.Sprintf("%02d:%02d:00", rand.Intn(14)+1, rand.Intn(59)), dbtypes.SHLocation)
	autoPubTime := dbtypes.MysqlTime{now}
	merchantItem.AutoPubTime = &autoPubTime

	name := mClient.Name()
	merchantItem.Name = &name
	merchantItem.Contact = u.Company.ContactName
	merchantItem.ContactPhone = u.Phone
	merchantItem.PlatForm = models.PlatformHy88
	if item, err := services.Merchant.GetByCompanyId(u.CompanyID, models.PlatformHy88); errors.Is(err, gorm.ErrRecordNotFound) {
		if err := services.Merchant.Create(&merchantItem); err != nil {
			return
		}
	} else {
		item.CompanySite = merchantItem.CompanySite
		if err := services.Merchant.Save(item); err != nil {
			return
		}
	}

	services.NewUserPlatformService().UpdateFunction(models.UserPlatform{CompanyID: u.CompanyID,
		Platform: models.PlatformHy88, Funcname: models.FuncnamePost, Status: models.UserPlatformStatusOpened,
		ExpireTime: u.PostExpireTime})
	services.NewUserPlatformService().UpdateFunction(models.UserPlatform{CompanyID: u.CompanyID,
		Platform: models.PlatformHy88, Funcname: models.FuncnameRank, Status: models.UserPlatformStatusOpened,
		ExpireTime: u.RankExpireTime})
	services.NewUserPlatformService().UpdateFunction(models.UserPlatform{CompanyID: u.CompanyID,
		Platform: models.PlatformHy88, Funcname: models.FuncnameSeek, Status: models.UserPlatformStatusOpened,
		ExpireTime: u.SeekExpireTime})

}

func (trigger *hy88Trigger) AutoFlow(merchant *models.Merchant, c *models.Company, userTrigger ...bool) bool {
	return true
}
func (trigger *hy88Trigger) ValidateCompany(c models.Company) error {
	return nil
}
func (trigger *hy88Trigger) CalSteps(m *models.Merchant, c models.Company) (s schemas.MerchantStep) {
	s.Name = models.PlatFormName(m.PlatForm)
	s.Steps = append(s.Steps, schemas.Step{
		Text:   "已开通",
		Action: dbtypes.ActionNone,
		Status: schemas.StepStatusDone,
	})
	return
}

func (trigger *hy88Trigger) FormatAccount(m *models.Merchant) (ret map[string]interface{}) {
	ret = map[string]interface{}{
		"username": "",
		"password": "",
	}
	return ret
}
func (trigger *hy88Trigger) BindUser(m *models.Merchant, mobile int, password string) error {
	return nil
}
