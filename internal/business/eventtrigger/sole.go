package eventtrigger

import (
	"errors"
	"fmt"
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"git.paihang8.com/lib/goutils/sites/sole"
	"github.com/gomodule/redigo/redis"
	_ "gitlab.com/all_publish/api/internal/business/merchanter"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/gorm"
	"math/rand"
	"strings"
	"time"
)

/*
*
注册。
提交认证，
获取认证状态
*/
type soleTrigger struct {
}

func init() {
	sole := new(soleTrigger)
	models.RegisterTrigger(models.PlatformSole, sole)
}

func (trigger *soleTrigger) ValidateCompany(c models.Company) error {
	p := sole.RegReq{
		UserName:        "abccccc23",
		Pwd:             "123Abc!0",
		NiName:          c.ContactName,
		CompanyName:     c.Name,
		MobilePhone:     c.Phone[0],
		QQ:              *c.Qq,
		CompanyType:     c.CompanyType,
		WorkingModel:    c.WorkingModel,
		CompanyDesc:     c.Introduce,
		CompanyEmail:    c.Email,
		Area_t:          2,
		Area_p:          3,
		BigIndustryId:   1,
		MidIndustryId:   4,
		SmallIndustryId: 1,
		MainProduct:     c.MainProduct,
		MainBrand:       c.MainBrand,
		CompanyAddress:  c.Address,
		CompanyIntro:    c.ShortName,
	}
	if err := models.Valiate(p); err != nil {
		return errors.New("搜了网:" + err.Error())
	}
	validPeriod := strings.Split(c.ValidPeriod, " - ")
	if len(validPeriod) != 2 {
		return errors.New("搜了网: 营业期限格式无效，示例: 2014-12-19 - 永续经营")
	}
	if validPeriod[1] == "永续经营" {
		validPeriod[1] = "9999-12-31"
	}
	return nil
}

func (trigger *soleTrigger) OnCompanyEdit(c *models.Company) {
	if item, err := services.Merchant.GetByCompanyId(c.ID, models.PlatformSole); err == nil {
		if c.GetAreaID() > 0 && c.GetCatID() > 0 {
			if item.TargetCompanyID == 0 {
				if u, e := services.NewUserService().GetUserByCompanyId(c.ID); e == nil {
					u.Company = *c
					trigger.AutoReg(u, item)
				}
			}
			trigger.AutoFlow(item, c, true)
		}
	}
}
func (trigger *soleTrigger) lockReg(merchant *models.Merchant) bool {
	rds := db.GetRedisConn()
	defer rds.Close()
	key := fmt.Sprintf("soleReg:%d", merchant.ID)
	if ok, err := redis.String(rds.Do("set", key, merchant.ID, "nx", "px", 2000)); err != nil {
		return false
	} else {
		return ok == "OK"
	}
}

func (trigger *soleTrigger) ReleaseReg(merchant *models.Merchant) bool {
	rds := db.GetRedisConn()
	defer rds.Close()
	key := fmt.Sprintf("soleReg:%d", merchant.ID)
	if ok, err := redis.Bool(rds.Do("del", key)); err != nil {
		return false
	} else {
		return ok
	}
}
func (trigger *soleTrigger) AutoReg(u *models.User, merchant *models.Merchant) {
	soleClient, ok := models.GetMerchant(models.PlatformSole)
	if !ok {
		return
	}
	if err := trigger.ValidateCompany(u.Company); err != nil {
		return
	}
	if merchant.TargetCompanyID != 0 {
		return
	}

	if !trigger.lockReg(merchant) {
		return
	}
	defer trigger.ReleaseReg(merchant)
	merchant.TargetCompanyID = -1 // 这个-1 表示正在注册
	services.Merchant.Save(merchant)
	if _, err := soleClient.Reg(u.Company); err != nil {
		merchant.RegFailedReason = err.Error()
		merchant.TargetCompanyID = 0
		services.Merchant.Save(merchant)
	} else {
		merchant.RegFailedReason = ""
		merchant.Account = soleClient.Account()
		merchant.TargetCompanyID = merchant.Account["id"].(int)
		merchant.CompanySite = merchant.Account["site"].(string)
		if u.Company.License == "" {
			merchant.CertStatus = dbtypes.CertStatusNeedRegNo
		} else {
			merchant.CertStatus = dbtypes.CertStatusNeedSubmit
		}
		services.Merchant.Save(merchant)
	}
}

func (trigger *soleTrigger) OnUserLogin(u *models.User, config publisher.PlatformConfig, client models.Merchanter) {
	trigger.OnFirstLogin(u, config, client)
	client, ok := models.GetMerchant(models.PlatformSole)
	if !ok {
		return
	}
	client.RenewWhenNeeded(u.CompanyID)
}

func (trigger *soleTrigger) OnFirstLogin(u *models.User, config publisher.PlatformConfig, client models.Merchanter) {
	soleClient, ok := models.GetMerchant(models.PlatformSole)
	if !ok {
		return
	}
	services.NewUserPlatformService().UpdateFunction(models.UserPlatform{CompanyID: u.CompanyID,
		Platform: models.PlatformSole, Funcname: models.FuncnamePost, Status: models.UserPlatformStatusOpened,
		ExpireTime: u.PostExpireTime})
	if item, err := services.Merchant.GetByCompanyId(u.CompanyID, models.PlatformSole); err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		var merchantItem models.Merchant
		merchantItem.CreatedAt = dbtypes.SHNow()
		merchantItem.UpdatedAt = dbtypes.SHNow()
		merchantItem.CompanyID = u.CompanyID
		if u.Company.Site != "" {
			merchantItem.CompanySite = u.Company.Site
		}
		merchantItem.Account = soleClient.Account()
		merchantItem.Pause = false
		merchantItem.Total = config.MaxItem
		merchantItem.BaseCnt = config.BaseItem
		merchantItem.RenewAddCnt = config.BaseItem
		merchantItem.DailyPubProducts = 2
		merchantItem.PubCount = 20
		merchantItem.PubPerCount = 3
		merchantItem.AutoPub = true
		now, _ := time.ParseInLocation("15:04:05", fmt.Sprintf("%02d:%02d:00", rand.Intn(14)+1, rand.Intn(59)), dbtypes.SHLocation)
		autoPubTime := dbtypes.MysqlTime{now}
		merchantItem.AutoPubTime = &autoPubTime
		name := soleClient.Name()
		merchantItem.Name = &name
		merchantItem.Contact = u.Company.ContactName
		merchantItem.ContactPhone = u.Phone
		merchantItem.PlatForm = models.PlatformSole
		if err := services.Merchant.Create(&merchantItem); err == nil {
			trigger.AutoReg(u, &merchantItem)
		}

	} else {
		item.Total = config.MaxItem
		item.BaseCnt = config.BaseItem
		item.RenewAddCnt = config.BaseItem
		services.Merchant.Updates(item, "total", "base_cnt", "renew_add_cnt")
		if item.TargetCompanyID == 0 {
			//需要注册
			trigger.AutoReg(u, item)
		} else {
			c, _ := services.NewCompanyService().Find(item.CompanyID)
			trigger.AutoFlow(item, c)
		}
	}
}

func (trigger *soleTrigger) AutoFlow(merchant *models.Merchant, c *models.Company, userTrigger ...bool) bool {
	isUserTrigger := false
	if len(userTrigger) > 0 && userTrigger[0] {
		isUserTrigger = true
	}
	if merchant.TargetCompanyID <= 0 {
		return false
	}
	if merchant.CertStatus == dbtypes.CertStatusPassed {
		return true
	}
	client, _ := models.GetMerchant(models.PlatformSole)
	client.LoginBy(merchant.Account)
	if merchant.CertData == nil {
		merchant.CertData = map[string]interface{}{}
	}

	if merchant.CertStatus == dbtypes.CertStatusSubmitted || merchant.CertStatus == dbtypes.CertStatusSubmitFailed {
		if data, err := client.GetCert(merchant.TargetCompanyID, 0); err != nil {
			merchant.CertData["reason"] = err.Error()
		} else if status, ok := data.(*sole.CertStatus); ok {
			var cstatus dbtypes.CertStatus
			switch status.AuditStatus {
			case sole.CertAuditStatusAuditing:
				cstatus = dbtypes.CertStatusSubmitted
			case sole.CertAuditStatusPassed:
				cstatus = dbtypes.CertStatusPassed
			case sole.CertAuditStatusRejected:
				merchant.CertData["reason"] = status.Remark
				cstatus = dbtypes.CertStatusRejected
			}
			merchant.CertStatus = cstatus
		}
		services.Merchant.Save(merchant)
		if merchant.CertStatus == dbtypes.CertStatusPassed {
			return true
		}

	} else {
		//preLicense := ""
		//if pic, ok := merchant.CertData["license"]; ok {
		//	preLicense = pic.(string)
		//}
		if isUserTrigger && merchant.CertStatus != dbtypes.CertStatusPassed {
			if _, err := client.SubmitCert(*c, "营业执照"); err != nil {
				merchant.CertStatus = dbtypes.CertStatusSubmitFailed
				merchant.CertData["reason"] = err.Error()
				merchant.CertData["license"] = ""
			} else {
				merchant.CertData["id"] = 10000
				merchant.CertData["license"] = c.License
				merchant.CertStatus = dbtypes.CertStatusSubmitted
			}
			services.Merchant.Save(merchant)
		}
	}
	return false
}

func (trigger *soleTrigger) CalSteps(m *models.Merchant, c models.Company) (s schemas.MerchantStep) {
	s.Name = models.PlatFormName(m.PlatForm)
	// 检查公司资料
	s.Steps = []schemas.Step{
		{
			Text:   "提交公司资料",
			Action: dbtypes.ActionUploadLincense,
			Status: schemas.StepStatusTodo,
		},
		{
			Text:   "待审核",
			Action: dbtypes.ActionNone,
			Status: schemas.StepStatusUnReach,
		},
		{
			Text:   "审核结果",
			Action: dbtypes.ActionNone,
			Status: schemas.StepStatusUnReach,
		},
	}
	if err := trigger.ValidateCompany(c); err == nil {
		s.Steps[0].Status = schemas.StepStatusDone
	} else {
		s.Steps[0].Status = schemas.StepStatusDone
		s.Steps[1].Status = schemas.StepStatusDone
		s.Steps[2].Status = schemas.StepStatusTodo
		s.Steps[2].Reason = err.Error()
		s.Steps[2].Action = dbtypes.ActionUploadLincense
		return
	}
	if m.TargetCompanyID > 0 {
		s.Steps[1].Status = schemas.StepStatusTodo
		switch m.CertStatus {
		case dbtypes.CertStatusRejected:
			s.Steps[2].Text = "审核未通过"
			s.Steps[2].Reason = m.CertData["reason"].(string)
			s.Steps[2].Status = schemas.StepStatusTodo
		case dbtypes.CertStatusPassed:
			s.Steps[1].Status = schemas.StepStatusDone
			s.Steps[2].Text = "已开通"
			s.Steps[2].Action = dbtypes.ActionNone
			s.Steps[2].Status = schemas.StepStatusDone
		}

	} else if m.RegFailedReason != "" {
		s.Steps[1].Status = schemas.StepStatusDone
		s.Steps[2].Status = schemas.StepStatusTodo
		s.Steps[2].Text = "审核未通过"
		s.Steps[2].Action = dbtypes.ActionUploadLincense
		s.Steps[2].Reason = m.RegFailedReason
	}
	return
}

func (trigger *soleTrigger) FormatAccount(m *models.Merchant) (ret map[string]interface{}) {
	ret = map[string]interface{}{
		"username": "",
		"password": "",
	}
	if _, ok := m.Account["username"]; ok {
		ret = m.Account
	}
	return ret
}
func (trigger *soleTrigger) BindUser(m *models.Merchant, mobile int, password string) error {
	return nil
}
