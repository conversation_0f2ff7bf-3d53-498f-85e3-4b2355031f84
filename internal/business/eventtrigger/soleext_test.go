package eventtrigger

import (
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/test"
	"testing"
)

func TestSoleExtTrigger_AutoFlow(t *testing.T) {
	test.InitTest(t)
	if client, ok := models.GetTrigger(models.PlatformSoleExt); ok {
		u, _ := services.NewUserService().GetUserByPhone(18771007358)
		c, _ := services.NewCompanyService().Find(u.CompanyID)
		u.Company = *c
		merchant, _ := services.Merchant.GetByCompanyId(u.CompanyID, models.PlatformSoleExt)
		client.AutoFlow(merchant, c, true)
	}
}
