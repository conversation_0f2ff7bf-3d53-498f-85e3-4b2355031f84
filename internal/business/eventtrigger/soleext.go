package eventtrigger

import (
	"errors"
	"fmt"
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"git.paihang8.com/lib/goutils/sites/sole"
	"github.com/mitchellh/mapstructure"
	"gitlab.com/all_publish/api/internal/business/merchanter"
	_ "gitlab.com/all_publish/api/internal/business/merchanter"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/convert"
	"gitlab.com/all_publish/api/pkg/db"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gitlab.com/all_publish/api/pkg/log"
	"gorm.io/gorm"
	"math/rand"
	"strings"
	"time"
)

/*
*
1. 需要先注册51sole
2. 注册爱采购
3. 审核通过(查询审核状态), 未通过需要修改爱采购信息
4. 提交订单(审核通过的话)
5. 发布信息
*/
type soleExtTrigger struct {
	soleTrigger
}

func init() {
	soleext := new(soleExtTrigger)
	models.RegisterTrigger(models.PlatformSoleExt, soleext)
}

func (trigger *soleExtTrigger) ValidateCompany(c models.Company) error {
	return trigger.soleTrigger.ValidateCompany(c)
}

func (trigger *soleExtTrigger) OnCompanyEdit(c *models.Company) {
	if item, err := services.Merchant.GetByCompanyId(c.ID, models.PlatformSoleExt); err == nil {
		if c.GetAreaID() > 0 && c.GetCatID() > 0 {
			if item.TargetCompanyID == 0 {
				if u, e := services.NewUserService().GetUserByCompanyId(c.ID); e == nil {
					u.Company = *c
					trigger.AutoReg(u, item)
				}
			}
			trigger.AutoFlow(item, c, true)
		}
	}
}

/*
*
这里只能做到注册搜了网。 注册爱采购得主动调用
*/
func (trigger *soleExtTrigger) AutoReg(u *models.User, merchant *models.Merchant) {
	soleClient, ok := models.GetMerchant(models.PlatformSoleExt)
	if !ok {
		return
	}
	if err := trigger.ValidateCompany(u.Company); err != nil {
		return
	}
	if !trigger.soleTrigger.lockReg(merchant) {
		return
	}
	defer trigger.soleTrigger.ReleaseReg(merchant)
	if _, err := soleClient.Reg(u.Company); err != nil {
		merchant.RegFailedReason = err.Error()
		services.Merchant.Save(merchant)
	} else {
		merchant.RegFailedReason = ""
		merchant.Account = soleClient.Account()
		if merchant.TargetCompanyID == 0 {
			merchant.TargetCompanyID = merchant.Account["id"].(int)
			merchant.CompanySite = merchant.Account["site"].(string)
		}
		merchant.CertStatus = dbtypes.CertStatusNeedSubmit
		services.Merchant.Save(merchant)
	}
}

func (trigger *soleExtTrigger) OnUserLogin(u *models.User, config publisher.PlatformConfig, client models.Merchanter) {
	trigger.OnFirstLogin(u, config, client)
	client, ok := models.GetMerchant(models.PlatformSoleExt)
	if !ok {
		return
	}
	client.RenewWhenNeeded(u.CompanyID)
}

func (trigger *soleExtTrigger) OnFirstLogin(u *models.User, config publisher.PlatformConfig, client models.Merchanter) {
	soleClient, ok := models.GetMerchant(models.PlatformSoleExt)
	if !ok {
		return
	}
	services.NewUserPlatformService().UpdateFunction(models.UserPlatform{CompanyID: u.CompanyID,
		Platform: models.PlatformSoleExt, Funcname: models.FuncnamePost, Status: models.UserPlatformStatusOpened,
		ExpireTime: u.PostExpireTime})
	if item, err := services.Merchant.GetByCompanyId(u.CompanyID, models.PlatformSoleExt); err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		var merchantItem models.Merchant
		merchantItem.CreatedAt = dbtypes.SHNow()
		merchantItem.UpdatedAt = dbtypes.SHNow()
		merchantItem.CompanyID = u.CompanyID
		if u.Company.Site != "" {
			merchantItem.CompanySite = u.Company.Site
		}
		merchantItem.Account = soleClient.Account()
		merchantItem.Pause = false
		merchantItem.Total = config.MaxItem
		merchantItem.BaseCnt = config.BaseItem
		merchantItem.RenewAddCnt = config.BaseItem
		merchantItem.DailyPubProducts = 2
		merchantItem.PubCount = 150
		merchantItem.PubPerCount = 5
		merchantItem.AutoPub = true
		merchantItem.CertStatus = dbtypes.CertStatusNeedSubmit
		now, _ := time.ParseInLocation("15:04:05", fmt.Sprintf("%02d:%02d:00", rand.Intn(14)+1, rand.Intn(59)), dbtypes.SHLocation)
		autoPubTime := dbtypes.MysqlTime{now}
		merchantItem.AutoPubTime = &autoPubTime
		name := soleClient.Name()
		merchantItem.Name = &name
		merchantItem.Contact = u.Company.ContactName
		merchantItem.ContactPhone = u.Phone
		merchantItem.PlatForm = models.PlatformSoleExt
		if err := services.Merchant.Create(&merchantItem); err == nil {
			trigger.AutoReg(u, &merchantItem)
		}

	} else if item.TargetCompanyID == 0 {
		item.Total = config.MaxItem
		item.BaseCnt = config.BaseItem
		item.RenewAddCnt = config.BaseItem
		services.Merchant.Save(item)
		//需要注册
		trigger.AutoReg(u, item)
	} else {
		item.Total = config.MaxItem
		item.BaseCnt = config.BaseItem
		item.RenewAddCnt = config.BaseItem
		services.Merchant.Save(item)
		c, _ := services.NewCompanyService().Find(item.CompanyID)
		trigger.AutoFlow(item, c)
	}
}

/*
*
1. 先判断是否注册搜了网，没有则注册
2. 判断搜了网状态。如果是审核拒绝且营业执照已修改，则提交营业执照。
3. 判断是否注册了爱采购。没有则注册
4. 判断爱采购状态，如果未确定，则更新状态。
5. 如果状态已经是通过，继续获取合同状态，如果合同也是通过 则下订单
6. 判断爱采购信息是否修改，如果修改，则调用修改接口。更新状态为待审核
7. 判断合同状态，如果是拒绝且修改，则重新提交。更新状态为待审核
*/
func (trigger *soleExtTrigger) AutoFlow(merchant *models.Merchant, c *models.Company, userTrigger ...bool) bool {
	isUserTrigger := false
	if len(userTrigger) > 0 && userTrigger[0] {
		isUserTrigger = true
	}
	var account merchants.AicaigouAccount
	var certData merchants.CertData
	account.Init(merchant.Account)
	certData.Init(merchant.CertData)

	var aicaigou *models.AicaigouUsers
	var err error
	client, ok := models.GetMerchant(models.PlatformSoleExt)
	if !ok {
		return false
	}
	client.LoginBy(merchant.Account)
	//先判断是否注册sole网
	if merchant.TargetCompanyID == 0 {
		u := models.User{}
		u.Company = *c
		trigger.AutoReg(&u, merchant)
		if merchant.TargetCompanyID == 0 {
			return false
		}
		mapstructure.Decode(merchant.Account, &account)
	}

	//注册成功了。处理证书状态
	if merchant.TargetCompanyID > 0 {
		if merchant.CertStatus == dbtypes.CertStatusSubmitted || merchant.CertStatus == dbtypes.CertStatusSubmitFailed {
			if data, err := client.(*merchanter.SoleExtMerchant).SoleMerchant.GetCert(merchant.TargetCompanyID, 0); err != nil {
				certData.Reason = err.Error()
			} else if status, ok := data.(*sole.CertStatus); ok {
				var cstatus dbtypes.CertStatus
				if status.Remark != "" {
					status.AuditStatus = sole.CertAuditStatusRejected
				}
				switch status.AuditStatus {
				case sole.CertAuditStatusAuditing:
					cstatus = dbtypes.CertStatusSubmitted
				case sole.CertAuditStatusPassed:
					cstatus = dbtypes.CertStatusPassed
				case sole.CertAuditStatusRejected:
					certData.Reason = status.Remark
					cstatus = dbtypes.CertStatusRejected
				}
				merchant.CertStatus = cstatus
			}
			merchant.SetCertData(certData)
			services.Merchant.Save(merchant)
		} else {
			//preLicense := certData.License
			if isUserTrigger && merchant.CertStatus != dbtypes.CertStatusPassed {
				if _, err := client.(*merchanter.SoleExtMerchant).SoleMerchant.SubmitCert(*c, "营业执照"); err != nil {
					certData.Reason = err.Error()
					certData.License = ""
					merchant.CertStatus = dbtypes.CertStatusSubmitFailed
				} else {
					certData.License = c.License
					merchant.CertStatus = dbtypes.CertStatusSubmitted
				}
				merchant.SetCertData(certData)
				services.Merchant.Save(merchant)
			}
		}
	}

	if aicaigou, err = services.NewAicaigouUser(db.Instance().Get()).Find(c.ID); err != nil || aicaigou.CompanyID == 0 {
		return false
	}
	// 注册爱采购
	if !account.Joined {
		var companyProvince, companyCity, bankProvince, bankCity models.AreaMappings
		companyProvince, _ = services.Area.GetMapping(convert.Str(aicaigou.CompanyAreaIds[0]).MustUInt64(), models.PlatformSoleExt)
		if services.Area.IsDirectCity(convert.Str(aicaigou.CompanyAreaIds[0]).MustInt()) {
			companyCity = companyProvince
		} else {
			companyCity, _ = services.Area.GetMapping(convert.Str(aicaigou.CompanyAreaIds[1]).MustUInt64(), models.PlatformSoleExt)
		}
		bankProvince, _ = services.Area.GetMapping(convert.Str(aicaigou.BankAreaIds[0]).MustUInt64(), models.PlatformSoleExt)
		bankCity, _ = services.Area.GetMapping(convert.Str(aicaigou.BankAreaIds[1]).MustUInt64(), models.PlatformSoleExt)
		param := sole.RegAicaigouReq{
			ActionReq: sole.ActionReq{
				UserName: account.UserName,
				Password: account.Password,
			},
			TpCompanyName:     aicaigou.CompanyName,
			TpCompanyType:     aicaigou.CompanyType,
			SocialCreditCode:  aicaigou.SocialCreditCode,
			LinkPerson:        aicaigou.LinkPerson,
			LinkPhone:         aicaigou.LinkPhone,
			ProviceId:         convert.Str(companyProvince.TargetAreaID).MustInt(),
			CityId:            convert.Str(companyCity.TargetAreaID).MustInt(),
			BusinessImg:       aicaigou.BusinessImg,
			LinkEmail:         aicaigou.LinkEmail,
			CompanyLogo:       aicaigou.CompanyLogo,
			CompanyWeb:        aicaigou.CompanyWeb,
			ContractBeginDate: aicaigou.ContractBeginDate,
			ContractEndDate:   aicaigou.ContractEndDate,
			ContractFile:      aicaigou.ContractFile,
			ProductType:       1,
			BrankName:         aicaigou.BrankName,
			BkProviceName:     bankProvince.TargetAreaName,
			BkCityName:        bankCity.TargetAreaName,
			OpenBranch:        aicaigou.OpenBranch,
			InterBankNum:      aicaigou.InterBankNum,
			CardNumber:        aicaigou.CardNumber,
		}
		if err := models.Valiate(param); err != nil {
			return false
		}
		//资料修改了才重新注册
		if account.Hash != aicaigou.Hash() {
			if _, err := client.GetApi().(sole.PublishApi).RegAicaigou(param); err != nil {
				account.Joined = false
				account.Hash = aicaigou.Hash()
				account.Status = sole.UserAuditStatusRejected
				account.Reason = err.Error()
				merchant.SetAccount(account)
				services.Merchant.Save(merchant)
				return false
			} else {
				account.Joined = true
				account.Hash = aicaigou.Hash()
				account.Status = sole.UserAuditStatusAuditing
				account.Reason = ""
				merchant.SetAccount(account)
				merchant.CertStatus = dbtypes.CertStatusSubmitted
				services.Merchant.Save(merchant)
			}
		}

	} else {
		// 检测状态
		if account.Status == sole.UserAuditStatusAuditing {
			trigger.refreshUserStatus(client, &account, merchant)
		}
	}
	if account.Status == sole.UserAuditStatusPassed {
		if contract, err := client.GetCert(int(c.ID), 0); err != nil {
			return false
		} else {
			data := contract.([]sole.ContractItem)
			if len(data) > 0 {
				sts := data[len(data)-1]
				switch sts.AuditStatus {
				case sole.ContractAuditStatusAuditing:
					account.SoleCertStatus = dbtypes.CertStatusSubmitted
				case sole.ContractAuditStatusPassed:
					account.SoleCertStatus = dbtypes.CertStatusPassed
				case sole.ContractAuditStatusRejected:
					account.SoleCertStatus = dbtypes.CertStatusRejected
					account.SoleCertReason = sts.RefuseReason
				}
				merchant.SetAccount(account)
				services.Merchant.Save(merchant)
			}
		}
	}

	// 下订单
	if account.Status == sole.UserAuditStatusPassed && !account.Ordered {
		if err := client.GetApi().(sole.PublishApi).SubmitOrder(sole.ActionReq{
			UserName: account.UserName,
			Password: account.Password,
		}); err == nil || strings.Contains(err.Error(), "已开通服务") {
			account.Ordered = true
			merchant.SetAccount(account)
			services.Merchant.Save(merchant)
		}
	}
	// 账户修改过了。 修改爱采购(审核通过是无法修改的)
	if isUserTrigger && account.Status != sole.UserAuditStatusPassed {
		var companyProvince, companyCity, bankProvince, bankCity models.AreaMappings
		companyProvince, _ = services.Area.GetMapping(convert.Str(aicaigou.CompanyAreaIds[0]).MustUInt64(), models.PlatformSoleExt)
		if services.Area.IsDirectCity(convert.Str(aicaigou.CompanyAreaIds[0]).MustInt()) {
			companyCity = companyProvince
		} else {
			companyCity, _ = services.Area.GetMapping(convert.Str(aicaigou.CompanyAreaIds[1]).MustUInt64(), models.PlatformSoleExt)
		}
		bankProvince, _ = services.Area.GetMapping(convert.Str(aicaigou.BankAreaIds[0]).MustUInt64(), models.PlatformSoleExt)
		bankCity, _ = services.Area.GetMapping(convert.Str(aicaigou.BankAreaIds[1]).MustUInt64(), models.PlatformSoleExt)
		param := sole.ModifyAicaigouReq{
			ActionReq: sole.ActionReq{
				UserName: account.UserName,
				Password: account.Password,
			},
			TpCompanyName:     aicaigou.CompanyName,
			TpCompanyType:     aicaigou.CompanyType,
			SocialCreditCode:  aicaigou.SocialCreditCode,
			LinkPerson:        aicaigou.LinkPerson,
			LinkPhone:         aicaigou.LinkPhone,
			ProviceId:         convert.Str(companyProvince.TargetAreaID).MustInt(),
			CityId:            convert.Str(companyCity.TargetAreaID).MustInt(),
			BusinessImg:       aicaigou.BusinessImg,
			LinkEmail:         aicaigou.LinkEmail,
			CompanyLogo:       aicaigou.CompanyLogo,
			CompanyWeb:        aicaigou.CompanyWeb,
			ContractBeginDate: aicaigou.ContractBeginDate,
			ContractEndDate:   aicaigou.ContractEndDate,
			ContractFile:      aicaigou.ContractFile,
			ProductType:       1,
			BrankName:         aicaigou.BrankName,
			BkProviceName:     bankProvince.TargetAreaName,
			BkCityName:        bankCity.TargetAreaName,
			OpenBranch:        aicaigou.OpenBranch,
			InterBankNum:      aicaigou.InterBankNum,
			CardNumber:        aicaigou.CardNumber,
		}
		if err := client.GetApi().(sole.PublishApi).ModifyAicaigou(param); err != nil {
			trigger.refreshUserStatus(client, &account, merchant)
			return false
		} else {
			account.Status = sole.UserAuditStatusAuditing
			account.Reason = ""
			account.Hash = aicaigou.Hash()
			merchant.SetAccount(account)
			services.Merchant.Save(merchant)
			return true
		}
	}
	//preHash := account.ContractHash
	currentHash := account.GetContractHash(aicaigou.ContractFile, aicaigou.ContractBeginDate, aicaigou.ContractEndDate)
	if aicaigou.ContractFile != "" && isUserTrigger {
		if _, err := client.SubmitCert(*c); err != nil {
			account.SoleCertStatus = dbtypes.CertStatusSubmitFailed
			account.SoleCertReason = err.Error()
			account.SoleCertPic = ""
			account.ContractHash = ""
		} else {
			account.SoleCertPic = aicaigou.ContractFile
			account.SoleCertStatus = dbtypes.CertStatusSubmitted
			account.ContractHash = currentHash
		}
		merchant.SetAccount(account)
		services.Merchant.Save(merchant)
	}
	return true
}

func (trigger *soleExtTrigger) refreshUserStatus(client models.Merchanter, account *merchants.AicaigouAccount, merchant *models.Merchant) bool {
	if data, err := client.GetApi().(sole.PublishApi).GetUserStatus(sole.ActionReq{
		UserName: account.UserName,
		Password: account.Password,
	}); err != nil {

		return false
	} else {
		if data.AuditRemark != "" {
			data.AuditStatus = sole.UserAuditStatusRejected
		}
		account.Status = data.AuditStatus
		account.Reason = data.AuditRemark
		merchant.SetAccount(*account)
		services.Merchant.Save(merchant)
	}
	return true
}
func (trigger *soleExtTrigger) CalSteps(m *models.Merchant, c models.Company) (s schemas.MerchantStep) {
	var account merchants.AicaigouAccount
	var certData merchants.CertData
	account.Init(m.Account)
	certData.Init(m.CertData)
	s.Name = models.PlatFormName(m.PlatForm)
	// 检查公司资料
	s.Steps = []schemas.Step{
		{
			Text:   "提交公司资料",
			Action: dbtypes.ActionUploadLincense,
			Status: schemas.StepStatusTodo,
		},
		{
			Text:   "待审核",
			Action: dbtypes.ActionNone,
			Status: schemas.StepStatusUnReach,
		},
		{
			Text:   "审核结果",
			Action: dbtypes.ActionNone,
			Status: schemas.StepStatusUnReach,
		},
		{
			Text:   "提交爱采购资料",
			Action: dbtypes.ActionEditAicaigou,
			Status: schemas.StepStatusUnReach,
		},
		{
			Text:   "待审核",
			Action: dbtypes.ActionNone,
			Status: schemas.StepStatusUnReach,
		},
		{
			Text:   "审核结果",
			Action: dbtypes.ActionNone,
			Status: schemas.StepStatusUnReach,
		},
	}
	if err := trigger.ValidateCompany(c); err == nil {
		s.Steps[0].Status = schemas.StepStatusDone
		s.Steps[1].Status = schemas.StepStatusTodo
	} else {
		s.Steps[0].Status = schemas.StepStatusDone
		s.Steps[1].Status = schemas.StepStatusDone
		s.Steps[2].Status = schemas.StepStatusTodo
		s.Steps[2].Reason = err.Error()
		s.Steps[2].Action = dbtypes.ActionUploadLincense
		return
	}

	if m.TargetCompanyID > 0 {
		s.Steps[1].Status = schemas.StepStatusDone
		s.Steps[2].Status = schemas.StepStatusDone
		s.Steps[2].Text = "审核通过"
		switch m.CertStatus {
		case dbtypes.CertStatusRejected:
			s.Steps[1].Status = schemas.StepStatusDone
			s.Steps[2].Text = "审核未通过"
			s.Steps[2].Reason = certData.Reason
			s.Steps[2].Action = dbtypes.ActionUploadLincense
			s.Steps[2].Status = schemas.StepStatusTodo
		case dbtypes.CertStatusPassed:
			s.Steps[1].Status = schemas.StepStatusDone
			s.Steps[2].Text = "审核通过"
			s.Steps[2].Status = schemas.StepStatusDone
			s.Steps[2].Action = dbtypes.ActionNone
		default:
			s.Steps[1].Status = schemas.StepStatusTodo
			s.Steps[2].Text = "审核结果"
			s.Steps[2].Status = schemas.StepStatusUnReach
			s.Steps[2].Action = dbtypes.ActionNone
			return
		}
		if !account.Joined {
			if account.Reason == "" {
				s.Steps[3] = schemas.Step{
					Text:   "提交爱采购资料",
					Action: dbtypes.ActionEditAicaigou,
					Status: schemas.StepStatusTodo,
				}
			} else {
				s.Steps[3].Status = schemas.StepStatusDone
				s.Steps[4].Status = schemas.StepStatusDone
				s.Steps[5].Status = schemas.StepStatusTodo
				s.Steps[5].Reason = account.Reason
				s.Steps[5].Action = dbtypes.ActionEditAicaigou
			}

		} else {
			s.Steps[3].Status = schemas.StepStatusDone
			switch account.Status {
			case sole.UserAuditStatusAuditing:
				s.Steps[4].Status = schemas.StepStatusTodo
			case sole.UserAuditStatusRejected:
				s.Steps[4].Status = schemas.StepStatusDone
				s.Steps[5].Text = "审核未通过"
				s.Steps[5].Reason = account.Reason
				s.Steps[5].Action = dbtypes.ActionEditAicaigou
				s.Steps[5].Status = schemas.StepStatusTodo
			case sole.UserAuditStatusPassed:
				//判断sole网合同状态
				if account.SoleCertStatus != dbtypes.CertStatusPassed {
					if account.SoleCertStatus == dbtypes.CertStatusRejected {
						s.Steps[5].Status = schemas.StepStatusTodo
						s.Steps[5].Text = "审核未通过"
						s.Steps[5].Reason = account.SoleCertReason
						s.Steps[5].Action = dbtypes.ActionEditAicaigou
					} else {
						s.Steps[4].Status = schemas.StepStatusTodo
						s.Steps[5].Status = schemas.StepStatusUnReach
						s.Steps[5].Text = "审核结果"
					}
					return
				} else {
					s.Steps[4].Status = schemas.StepStatusDone
					s.Steps[5].Text = "已开通"
					s.Steps[5].Action = dbtypes.ActionNone
					s.Steps[5].Status = schemas.StepStatusDone
				}

			}

		}
	} else if m.RegFailedReason != "" {
		s.Steps[1].Status = schemas.StepStatusDone
		s.Steps[2].Text = "审核未通过"
		s.Steps[2].Status = schemas.StepStatusTodo
		s.Steps[2].Action = dbtypes.ActionUploadLincense
		s.Steps[2].Reason = m.RegFailedReason
	}

	return
}

func (trigger *soleExtTrigger) FormatAccount(m *models.Merchant) (ret map[string]interface{}) {
	ret = map[string]interface{}{
		"username": "",
		"password": "",
	}
	if _, ok := m.Account["username"]; ok {
		ret = m.Account
	}
	return ret
}

func (trigger *soleExtTrigger) RenewWhenNeeded(m *models.Merchant) bool {
	client, _ := models.GetMerchant(models.PlatformSoleExt)
	client.LoginBy(m.Account)
	var account merchants.AicaigouAccount
	account.Init(m.Account)
	if m.Total > m.BaseCnt+m.RenewCount*m.RenewAddCnt {
		if _, err := client.GetApi().(sole.PublishApi).Renew(sole.ActionReq{UserName: account.UserName,
			Password: account.Password,
		}); err != nil {
			log.Error("sole:", err)
		} else {
			m.RenewCount += 1
			services.Merchant.Save(m)
			return true
		}
	}
	return false
}
