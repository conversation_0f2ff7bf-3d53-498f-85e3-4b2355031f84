package eventtrigger

import (
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	"git.paihang8.com/lib/goutils/sites/liebiao"
	"gitlab.com/all_publish/api/internal/business/merchanter"
	_ "gitlab.com/all_publish/api/internal/business/merchanter"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/gorm"
)

/*
*
注册。
发布
提交认证（个人认证，营业执照认证)，
获取认证状态
注册商铺(需要审核)
*/
type liebiaoTrigger struct {
}

func init() {
	liebiao := new(liebiaoTrigger)
	models.RegisterTrigger(models.PlatformLiebiao, liebiao)
}

func (trigger *liebiaoTrigger) ValidateCompany(c models.Company) error {

	if c.Gender == nil {
		return errors.New("列表网要求填写性别")
	}
	p := liebiao.UserInfo{
		SourceId: fmt.Sprintf("%d", c.ID),
		Phone:    c.Phone[0],
	}
	if err := models.Valiate(p); err != nil {
		return errors.New("列表网:" + err.Error())
	}
	validPeriod := strings.Split(c.ValidPeriod, " - ")
	if len(validPeriod) != 2 {
		return errors.New("列表网: 营业期限格式无效，示例: 2014-12-19 - 永续经营")
	}
	if validPeriod[1] == "永续经营" {
		validPeriod[1] = "9999-12-31"
	}
	// 300~1500 个中文
	if utf8.RuneCountInString(c.Introduce) < 300 || utf8.RuneCountInString(c.Introduce) > 1500 {
		return errors.New("列表网: 公司简介需要在 300～1500个字")
	}
	p1 := liebiao.LicenseInfo{
		UserId:       int(c.ID),
		Company:      c.Name,
		License:      c.RegNo,
		DeadLine:     c.ValidPeriod,
		LicenseImage: c.License,
	}
	if err := models.Valiate(p1); err != nil {
		return errors.New("列表网:" + err.Error())
	}
	fareaId, _ := strconv.Atoi(c.AreaIds[0])
	var areaId = 0
	if fareaId == 0 {
		return errors.New("列表网:城市需要选到县/区级")
	} else {
		fareamapping, err := services.Area.GetMapping(uint64(fareaId), models.PlatformLiebiao)
		if err != nil {
			return err
		}
		if !models.IsDirectCity(fareamapping.TargetAreaName) {
			// 第一级不是直辖市就是省，需要下探一级
			if len(c.AreaIds) < 3 {
				return errors.New("列表网: 城市需要选到县/区级")
			}
			fareaId, _ = strconv.Atoi(c.AreaIds[1])
			areaId, _ = strconv.Atoi(c.AreaIds[2])

		} else {
			areaId, _ = strconv.Atoi(c.AreaIds[1])
		}
		if fareaId == 0 || areaId == 0 {
			return errors.New("城市需要选到县/区级")
		}
		fareamapping, err = services.Area.GetMapping(uint64(fareaId), models.PlatformLiebiao)
		if err != nil {
			return err
		}
		_, err = services.Area.GetMapping(uint64(areaId), models.PlatformLiebiao)
		if err != nil {
			return err
		}
	}
	return nil
}

func (trigger *liebiaoTrigger) OnCompanyEdit(c *models.Company) {
	if item, err := services.Merchant.GetByCompanyId(c.ID, models.PlatformLiebiao); err == nil {
		if c.GetAreaID() > 0 && c.GetCatID() > 0 {
			var account merchants.LiebiaoAccount
			account.Init(item.Account)
			if account.Id == 0 {
				if u, e := services.NewUserService().GetUserByCompanyId(c.ID); e == nil {
					u.Company = *c
					trigger.AutoReg(u, item)
				}
			}
			trigger.AutoFlow(item, c, true)
		}
	}
}

func (trigger *liebiaoTrigger) AutoReg(u *models.User, merchant *models.Merchant) {
	liebiaoClient, ok := models.GetMerchant(models.PlatformLiebiao)
	if !ok {
		return
	}
	if err := trigger.ValidateCompany(u.Company); err != nil {
		return
	}
	merchant.TargetCompanyID = -1 // 这个-1 表示正在注册
	services.Merchant.Save(merchant)
	if _, err := liebiaoClient.Reg(u.Company); err != nil {
		merchant.RegFailedReason = err.Error()
		merchant.TargetCompanyID = 0
		services.Merchant.Save(merchant)
	} else {
		merchant.RegFailedReason = ""
		merchant.TargetCompanyID = 0
		merchant.Account = liebiaoClient.Account()
		if u.Company.License == "" {
			merchant.CertStatus = dbtypes.CertStatusNeedRegNo
		} else {
			merchant.CertStatus = dbtypes.CertStatusNeedSubmit
		}
		services.Merchant.Save(merchant)
	}
}

func (trigger *liebiaoTrigger) OnUserLogin(u *models.User, config publisher.PlatformConfig, client models.Merchanter) {
	trigger.OnFirstLogin(u, config, client)
	client, ok := models.GetMerchant(models.PlatformLiebiao)
	if !ok {
		return
	}
	client.RenewWhenNeeded(u.CompanyID)
}

func (trigger *liebiaoTrigger) OnFirstLogin(u *models.User, config publisher.PlatformConfig, client models.Merchanter) {
	liebiaoClient, ok := models.GetMerchant(models.PlatformLiebiao)
	if !ok {
		return
	}
	services.NewUserPlatformService().UpdateFunction(models.UserPlatform{CompanyID: u.CompanyID,
		Platform: models.PlatformLiebiao, Funcname: models.FuncnamePost, Status: models.UserPlatformStatusOpened,
		ExpireTime: u.PostExpireTime})
	if item, err := services.Merchant.GetByCompanyId(u.CompanyID, models.PlatformLiebiao); err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		var merchantItem models.Merchant
		merchantItem.CreatedAt = dbtypes.SHNow()
		merchantItem.UpdatedAt = dbtypes.SHNow()
		merchantItem.CompanyID = u.CompanyID
		merchantItem.Account = liebiaoClient.Account()
		merchantItem.Pause = false
		merchantItem.Total = config.MaxItem
		merchantItem.BaseCnt = config.BaseItem
		merchantItem.RenewAddCnt = config.BaseItem
		merchantItem.DailyPubProducts = 2
		merchantItem.PubCount = 20
		merchantItem.PubPerCount = 3
		merchantItem.AutoPub = true
		now, _ := time.ParseInLocation("15:04:05", fmt.Sprintf("%02d:%02d:00", rand.Intn(14)+1, rand.Intn(59)), dbtypes.SHLocation)
		autoPubTime := dbtypes.MysqlTime{now}
		merchantItem.AutoPubTime = &autoPubTime
		name := liebiaoClient.Name()
		merchantItem.Name = &name
		merchantItem.Contact = u.Company.ContactName
		merchantItem.ContactPhone = u.Phone
		merchantItem.PlatForm = models.PlatformLiebiao
		if err := services.Merchant.Create(&merchantItem); err == nil {
			trigger.AutoReg(u, &merchantItem)
		}

	} else {
		item.Total = config.MaxItem
		item.BaseCnt = config.BaseItem
		item.RenewAddCnt = config.BaseItem
		services.Merchant.Updates(item, "total", "base_cnt", "renew_add_cnt")
		var account merchants.LiebiaoAccount
		account.Init(item.Account)
		if account.Id == 0 {
			//需要注册
			trigger.AutoReg(u, item)
		}
		c, _ := services.NewCompanyService().Find(item.CompanyID)
		trigger.AutoFlow(item, c)
	}
}

func (trigger *liebiaoTrigger) AutoFlow(merchant *models.Merchant, c *models.Company, userTrigger ...bool) bool {
	isUserTrigger := false
	if len(userTrigger) > 0 && userTrigger[0] {
		isUserTrigger = true
	}
	// 商铺已注册成功
	if merchant.TargetCompanyID > 0 {
		return true
	}

	client, _ := models.GetMerchant(models.PlatformLiebiao)
	client.LoginBy(merchant.Account)
	if merchant.CertData == nil {
		merchant.CertData = map[string]interface{}{}
	}
	var account merchants.LiebiaoAccount
	account.Init(merchant.Account)
	// 个人账号还未注册
	if account.Id == 0 {
		return false
	}
	//if account.CertStatus != dbtypes.CertStatusSubmitted && account.CertStatus != dbtypes.CertStatusPassed {
	//	if isUserTrigger {
	//		if _, err := client.SubmitCert(*c, merchanter.AuthTypePersonal); err == nil {
	//			account.CertStatus = dbtypes.CertStatusSubmitted
	//			account.Reason = ""
	//			merchant.SetAccount(account)
	//		} else {
	//			if strings.Contains(err.Error(), "已存在") {
	//				// 重复提交
	//				account.CertStatus = dbtypes.CertStatusSubmitted
	//				account.Reason = ""
	//				merchant.SetAccount(account)
	//			} else {
	//				account.CertStatus = dbtypes.CertStatusRejected
	//				account.Reason = err.Error()
	//				merchant.SetAccount(account)
	//			}
	//
	//		}
	//		services.Merchant.Save(merchant)
	//	}
	//
	//}
	//if account.CertStatus == dbtypes.CertStatusSubmitted {
	//	if data, err := client.GetCert(0, 0); err != nil {
	//		return false
	//	} else {
	//		result := data.(*liebiao.AuthorizedResultData)
	//		switch result.IdCard.Checked {
	//		case "待审核":
	//		case "审核通过":
	//			account.CertStatus = dbtypes.CertStatusPassed
	//		default:
	//			account.CertStatus = dbtypes.CertStatusRejected
	//			account.Reason = result.IdCard.Checked
	//		}
	//
	//		merchant.SetAccount(account)
	//		services.Merchant.Save(merchant)
	//	}
	//}
	//// 个人认证未通过
	//if account.CertStatus != dbtypes.CertStatusPassed {
	//	return false
	//}

	if merchant.CertStatus != dbtypes.CertStatusPassed && merchant.CertStatus != dbtypes.CertStatusSubmitted {
		if (isUserTrigger || merchant.CertStatus == dbtypes.CertStatusNeedSubmit) && merchant.CertStatus != dbtypes.CertStatusPassed {
			if _, err := client.SubmitCert(*c, merchanter.AuthTypeLicense); err != nil {
				if strings.Contains(err.Error(), "已存在") {
					// 重复提交
					merchant.CertStatus = dbtypes.CertStatusSubmitted
					merchant.CertData["reason"] = ""
				} else {
					merchant.CertStatus = dbtypes.CertStatusRejected
					merchant.CertData["reason"] = err.Error()
				}
			} else {
				merchant.CertData["id"] = 10000
				merchant.CertData["license"] = c.License
				merchant.CertStatus = dbtypes.CertStatusSubmitted
			}
			services.Merchant.Save(merchant)
		}
	} else if merchant.CertStatus == dbtypes.CertStatusSubmitted {
		if data, err := client.GetCert(merchant.TargetCompanyID, 0); err != nil {
			merchant.CertData["reason"] = err.Error()
		} else if data, ok := data.(*liebiao.AuthorizedResultData); ok {
			var cstatus = merchant.CertStatus
			switch data.License.Checked {
			case "待审核":
				cstatus = dbtypes.CertStatusSubmitted
			case "审核通过":
				cstatus = dbtypes.CertStatusPassed
			default:
				merchant.CertData["reason"] = data.License.Checked
				cstatus = dbtypes.CertStatusRejected
			}
			merchant.CertStatus = cstatus
		}
		services.Merchant.Save(merchant)
	}

	// 营业执照认证通过 就可以申请商铺了
	if merchant.CertStatus == dbtypes.CertStatusPassed {
		var err error
		// var areamapping models.AreaMappings
		var fareamapping models.AreaMappings
		areaId := c.GetAreaID()
		fareaId, _ := strconv.Atoi(c.AreaIds[0])

		if fareaId == 0 {
			return false
		} else {
			fareamapping, err = services.Area.GetMapping(uint64(fareaId), models.PlatformLiebiao)
			if err != nil {
				return false
			}
			if !models.IsDirectCity(fareamapping.TargetAreaName) {
				// 第一级不是直辖市就是省，需要下探一级
				if len(c.AreaIds) < 3 {
					return false
				}
				fareaId, _ = strconv.Atoi(c.AreaIds[1])
				areaId, _ = strconv.Atoi(c.AreaIds[2])
				if fareaId == 0 {
					return false
				}
				fareamapping, err = services.Area.GetMapping(uint64(fareaId), models.PlatformLiebiao)
				if err != nil {
					return false
				}
			} else {
				if len(c.AreaIds) < 2 {
					return false
				}
				areaId, _ = strconv.Atoi(c.AreaIds[1])
			}
			if areaId == 0 {
				return false
			}
			// areamapping, err = services.Area.GetMapping(uint64(areaId), models.PlatformLiebiao)
			// if err != nil {
			// 	return false
			// }
		}
		// HY-9012 去掉商铺申请
		// categoryId := c.GetCatID()
		// classmapping, err := cat.NewCatService().GetMapping(uint64(categoryId), models.PlatformLiebiao)
		// if err != nil {
		// 	return false
		// }
		// cats := strings.Split(classmapping.TargetCatID, ",")
		// var info = liebiao.ShopReq{
		// 	SourceId: fmt.Sprintf("%d", c.ID),
		// 	UserId:   account.Id,
		// 	Name:     c.Name,
		// 	//Desc:      c.Introduce,
		// 	CateId:   convert.Str(cats[1]).MustInt(),
		// 	CityId:   convert.Str(fareamapping.TargetAreaID).MustInt(),
		// 	RegionId: convert.Str(areamapping.TargetAreaID).MustInt(),
		// 	Address:  c.Address,
		// 	Logo:     c.Logo,
		// 	Phone:    c.Phone[0],
		// 	KefuQQ:   "",
		// 	Intro:    c.Introduce,
		// }
		// 注册商铺
		// if account.ApplyId == 0 {
		// 	if err := client.GetApi().(liebiao.PublishApi).ApplyShop(liebiao.ShopRequest{ShopInfo: info}); err != nil {
		// 		if strings.Contains(err.Error(), "成功") {
		// 			account.ApplyId = 1
		// 			merchant.RegFailedReason = ""
		// 		} else {
		// 			merchant.RegFailedReason = err.Error()
		// 		}
		// 	} else {
		// 		account.ApplyId = 1
		// 		merchant.RegFailedReason = ""
		// 		merchant.SetAccount(account)
		// 	}
		// }
		// if account.ApplyId > 0 {
		// 	// 获取状态
		// 	if data, err := client.GetCert(0, 0); err != nil {
		// 		return false
		// 	} else {
		// 		result := data.(*liebiao.AuthorizedResultData)
		// 		if result.Shop.Id > 0 { // 审核通过
		// 			merchant.CompanySite = result.Shop.Url
		// 			merchant.TargetCompanyID = result.Shop.Id
		// 		} else {
		// 			if result.Shop.StatusName == "待审核" {

		// 			} else if result.Shop.StatusName == "申请店铺" {
		// 				merchant.RegFailedReason = ""
		// 				account.ApplyId = 0 // 重置id, 重新提交
		// 				merchant.SetAccount(account)
		// 			} else {
		// 				merchant.RegFailedReason = result.Shop.Reason
		// 				account.ApplyId = 0 // 重置id, 重新提交
		// 				merchant.SetAccount(account)
		// 			}

		// 		}
		// 		services.Merchant.Save(merchant)
		// 	}
		// }

		services.Merchant.Save(merchant)
	}
	return true
}

func (trigger *liebiaoTrigger) CalSteps(m *models.Merchant, c models.Company) (s schemas.MerchantStep) {
	s.Name = models.PlatFormName(m.PlatForm)
	// 检查公司资料
	s.Steps = []schemas.Step{
		{
			Text:   "提交公司资料",
			Action: dbtypes.ActionUploadLincense,
			Status: schemas.StepStatusTodo,
		},
		{
			Text:   "待审核",
			Action: dbtypes.ActionNone,
			Status: schemas.StepStatusUnReach,
		},
		{
			Text:   "审核结果",
			Action: dbtypes.ActionNone,
			Status: schemas.StepStatusUnReach,
		},
	}
	if err := trigger.ValidateCompany(c); err == nil {
		s.Steps[0].Status = schemas.StepStatusDone
	} else {
		s.Steps[0].Status = schemas.StepStatusDone
		s.Steps[1].Status = schemas.StepStatusDone
		s.Steps[2].Status = schemas.StepStatusTodo
		s.Steps[2].Reason = err.Error()
		s.Steps[2].Action = dbtypes.ActionUploadLincense
		return
	}
	var account merchants.LiebiaoAccount
	account.Init(m.Account)
	if m.RegFailedReason != "" {
		s.Steps[1].Status = schemas.StepStatusDone
		s.Steps[2].Status = schemas.StepStatusTodo
		s.Steps[2].Text = "审核未通过"
		s.Steps[2].Action = dbtypes.ActionUploadLincense
		s.Steps[2].Reason = m.RegFailedReason
	} else {
		if account.Id > 0 {
			if m.TargetCompanyID > 0 {
				s.Steps[1].Status = schemas.StepStatusDone
				s.Steps[2].Text = "已开通"
				s.Steps[2].Status = schemas.StepStatusDone
			} else {
				if account.Reason != "" {
					s.Steps[2].Text = "审核未通过"
					s.Steps[2].Reason = account.Reason
					s.Steps[2].Status = schemas.StepStatusTodo
				} else {
					s.Steps[1].Status = schemas.StepStatusDone
					s.Steps[1].Text = "审核通过 "
					s.Steps[1].Action = dbtypes.ActionNone
					//switch account.CertStatus {
					//case dbtypes.CertStatusRejected:
					//	s.Steps[2].Text = "审核未通过"
					//	s.Steps[2].Reason = ""
					//	s.Steps[2].Status = schemas.StepStatusTodo
					//case dbtypes.CertStatusNeedSubmit:
					//	s.Steps[2].Text = "待提交"
					//	s.Steps[2].Reason = ""
					//	s.Steps[2].Status = schemas.StepStatusTodo
					//	s.Steps[2].Action = dbtypes.ActionUploadLincense
					//case dbtypes.CertStatusSubmitted:
					//	s.Steps[2].Text = "待审核"
					//	s.Steps[2].Reason = ""
					//	s.Steps[2].Status = schemas.StepStatusTodo
					//case dbtypes.CertStatusPassed:
					switch m.CertStatus {
					case dbtypes.CertStatusRejected:
						s.Steps[2].Text = "审核未通过"
						s.Steps[2].Reason = m.CertData["reason"].(string)
						s.Steps[2].Status = schemas.StepStatusTodo
					case dbtypes.CertStatusPassed:
						s.Steps[1].Status = schemas.StepStatusDone
						s.Steps[2].Text = "申请商铺"
						s.Steps[2].Action = dbtypes.ActionNone
						s.Steps[2].Status = schemas.StepStatusTodo
					}
					//default:
					//	s.Steps[1].Status = schemas.StepStatusTodo
					//}

				}
			}

		}
	}
	return
}

func (trigger *liebiaoTrigger) FormatAccount(m *models.Merchant) (ret map[string]interface{}) {
	ret = map[string]interface{}{
		"username": "",
		"password": "",
	}
	if _, ok := m.Account["phone"]; ok {
		ret = m.Account
		ret["username"] = ret["phone"]
	}
	return ret
}
func (trigger *liebiaoTrigger) BindUser(m *models.Merchant, mobile int, password string) error {
	return nil
}
