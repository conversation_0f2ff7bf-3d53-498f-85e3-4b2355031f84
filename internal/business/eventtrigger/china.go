package eventtrigger

import (
	"errors"
	"fmt"
	"git.paihang8.com/lib/goutils/request"
	"git.paihang8.com/lib/goutils/sites/china"
	"git.paihang8.com/lib/goutils/sites/hy88/publisher"
	_ "gitlab.com/all_publish/api/internal/business/merchanter"
	"gitlab.com/all_publish/api/internal/models"
	"gitlab.com/all_publish/api/internal/models/merchants"
	"gitlab.com/all_publish/api/internal/schemas"
	"gitlab.com/all_publish/api/internal/services"
	"gitlab.com/all_publish/api/pkg/dbtypes"
	"gorm.io/gorm"
	"math/rand"
	"strings"
	"time"
)

type chinaTrigger struct {
}

func init() {
	china := new(chinaTrigger)
	models.RegisterTrigger(models.PlatformChina, china)
}
func (trigger *chinaTrigger) ValidateCompany(c models.Company) error {
	if c.GetAreaID() == 0 {
		return errors.New("中国供应商:公司地址没编辑，不能注册")
	}
	if c.GetCatID() == 0 {
		return errors.New("中国供应商:公司分类没编辑，不能注册")
	}

	validPeriod := strings.Split(c.ValidPeriod, " - ")
	long := 0
	if len(validPeriod) != 2 {
		return errors.New("中国供应商: 营业期限格式无效，示例: 2014-12-19 - 永续经营")
	}
	if validPeriod[1] == "永续经营" {
		validPeriod[1] = "9999-12-31"
		long = 1
	}

	p := china.UserReq{
		UserName:      "bjmayor",
		Password:      "abfda234242",
		Name:          c.ContactName,
		Corpname:      c.Name,
		Corpintro:     c.Introduce,
		Mobile:        c.Phone[0],
		Region:        "180108",
		Corpsubdomain: "bjmayor001",
	}.WithCategorys([]int{8212, 8302, 8221})
	if err := models.Valiate(p); err != nil {
		return errors.New("中国供应商:" + err.Error())
	}
	pcom := china.SubmitQualificationReq{
		UserName:         "bjmayor",
		Password:         "abfda234242",
		RegCode:          c.RegNo,
		Organ:            c.RegAuthority,
		Corptype:         china.CorpTypeFromName(c.CompanyType),
		Artificialperson: c.Legal,
		Registerplace:    c.RegAddr,
		Setuptime:        c.RegDate,
		Managetimelong:   long,
		Managetimestart:  validPeriod[0],
		Managetimeend:    validPeriod[1],
		Managebound:      c.Business,
	}.WithPic([]byte(c.License))
	if err := models.Valiate(pcom); err != nil {
		return errors.New("中国供应商:" + err.Error())
	}
	return nil
}

func (trigger *chinaTrigger) OnCompanyEdit(c *models.Company) {
	if item, err := services.Merchant.GetByCompanyId(c.ID, models.PlatformChina); err == nil {
		if c.GetAreaID() > 0 && c.GetCatID() > 0 {
			if item.TargetCompanyID == 0 {
				if u, e := services.NewUserService().GetUserByCompanyId(c.ID); e == nil {
					u.Company = *c
					trigger.AutoReg(u, item)
				}
			} else {
				//同步公司资料
				chinaClient, _ := models.GetMerchant(models.PlatformChina)

				shouldCallModifyUser := false
				shouldCallModifyCompany := false
				for _, v := range c.AuditingFields {
					modifyUserKeys := []string{"contact_name", "phone", "address"}
					for _, vv := range modifyUserKeys {
						if v == vv {
							shouldCallModifyUser = true
							break
						}
					}
					modifyCompanyKeys := []string{"name", "main_brand", "introduce"}
					for _, vv := range modifyCompanyKeys {
						if v == vv {
							shouldCallModifyCompany = true
							break
						}
					}
				}
				if shouldCallModifyUser {
					params := china.ModifyUserReq{
						UserName: item.Account["username"].(string),
						Password: item.Account["password"].(string),
					}
					if c.ContactName != "" {
						params.Name = &c.ContactName
					}
					if len(c.Phone) > 0 {
						params.Mobile = &c.Phone[0]
					}
					if len(c.Phone) > 1 {
						params.Phone = &c.Phone[1]
					}
					if c.Address != "" {
						params.Address = &c.Address
					}
					chinaClient.GetApi().(china.PublishApi).ModifyUser(params)
				}
				if shouldCallModifyCompany {
					params := china.ModifyCompanyReq{
						UserName:  item.Account["username"].(string),
						Password:  item.Account["password"].(string),
						Corpname:  &c.Name,
						Corpintro: &c.Introduce,
						Brand:     &c.MainBrand,
					}
					if data, err := chinaClient.GetApi().(china.PublishApi).ModifyCompany(params); err == nil && data.Succ {
						var account merchants.ChinaAccount
						account.Init(item.Account)
						if account.Status == china.AuditingsStatusRejected {
							account.Status = china.AuditingsStatusAuditing
							item.SetAccount(account)
							services.Merchant.Save(item)
						}
					}
				}

			}
			trigger.AutoFlow(item, c, true)
		}
	}
}

func (trigger *chinaTrigger) AutoReg(u *models.User, merchant *models.Merchant) {
	chinaClient, ok := models.GetMerchant(models.PlatformChina)
	if !ok {
		return
	}
	if err := trigger.ValidateCompany(u.Company); err != nil {
		return
	}
	if data, err := chinaClient.Reg(u.Company); err != nil {
		merchant.RegFailedReason = err.Error()
		services.Merchant.Save(merchant)
	} else {
		c := data.(china.UserRes)
		merchant.TargetCompanyID = c.Corpid
		merchant.CompanySite = c.Corpurl
		merchant.RegFailedReason = ""
		merchant.Account = chinaClient.Account()
		merchant.Account["status"] = china.AuditingsStatusAuditing
		if u.Company.License == "" {
			merchant.CertStatus = dbtypes.CertStatusNeedRegNo
		} else {
			merchant.CertStatus = dbtypes.CertStatusNeedSubmit
		}
		services.Merchant.Save(merchant)
	}
}

func (trigger *chinaTrigger) OnUserLogin(u *models.User, config publisher.PlatformConfig, client models.Merchanter) {
	trigger.OnFirstLogin(u, config, client)
	client, ok := models.GetMerchant(models.PlatformChina)
	if !ok {
		return
	}
	client.RenewWhenNeeded(u.CompanyID)
}

func (trigger *chinaTrigger) OnFirstLogin(u *models.User, config publisher.PlatformConfig, client models.Merchanter) {
	chinaClient, ok := models.GetMerchant(models.PlatformChina)
	if !ok {
		return
	}
	services.NewUserPlatformService().UpdateFunction(models.UserPlatform{CompanyID: u.CompanyID,
		Platform: models.PlatformChina, Funcname: models.FuncnamePost, Status: models.UserPlatformStatusOpened,
		ExpireTime: u.PostExpireTime})
	if item, err := services.Merchant.GetByCompanyId(u.CompanyID, models.PlatformChina); err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		var merchantItem models.Merchant
		merchantItem.CreatedAt = dbtypes.SHNow()
		merchantItem.UpdatedAt = dbtypes.SHNow()
		merchantItem.CompanyID = u.CompanyID
		if u.Company.Site != "" {
			merchantItem.CompanySite = u.Company.Site
		}
		merchantItem.Account = chinaClient.Account()
		merchantItem.Pause = false
		merchantItem.Total = config.MaxItem
		merchantItem.BaseCnt = config.BaseItem
		merchantItem.RenewAddCnt = config.BaseItem
		merchantItem.DailyPubProducts = 2
		merchantItem.PubCount = 20
		merchantItem.PubPerCount = 3
		merchantItem.AutoPub = true
		now, _ := time.ParseInLocation("15:04:05", fmt.Sprintf("%02d:%02d:00", rand.Intn(14)+1, rand.Intn(59)), dbtypes.SHLocation)
		autoPubTime := dbtypes.MysqlTime{now}
		merchantItem.AutoPubTime = &autoPubTime
		name := chinaClient.Name()
		merchantItem.Name = &name
		merchantItem.Contact = u.Company.ContactName
		merchantItem.ContactPhone = u.Phone
		merchantItem.PlatForm = models.PlatformChina
		if err := services.Merchant.Create(&merchantItem); err == nil {
			trigger.AutoReg(u, &merchantItem)
		}

	} else {
		var account merchants.ChinaAccount
		account.Init(item.Account)
		item.Total = config.MaxItem
		item.BaseCnt = config.BaseItem
		item.RenewAddCnt = config.BaseItem
		services.Merchant.Updates(item, "total", "base_cnt", "renew_add_cnt")
		if item.TargetCompanyID == 0 {
			//需要注册
			trigger.AutoReg(u, item)
		} else {
			var shouldFetch bool
			if account.Status != china.AuditingsStatusPassed {
				shouldFetch = true
			}
			if shouldFetch {
				if data, err := chinaClient.GetApi().(china.PublishApi).GetUserStat(china.GetUserStatReq{
					account.UserName,
				}); err == nil {
					account.Status = data.Info.Auditingstatus
					account.Reason = data.Info.RefuseReason
					item.SetAccount(account)
					services.Merchant.Save(item)
				}
			}

			c, _ := services.NewCompanyService().Find(item.CompanyID)
			trigger.AutoFlow(item, c)
		}
	}
}

func (trigger *chinaTrigger) AutoFlow(merchant *models.Merchant, c *models.Company, userTrigger ...bool) bool {
	isUserTrigger := false
	if len(userTrigger) > 0 && userTrigger[0] {
		isUserTrigger = true
	}
	var account merchants.ChinaAccount
	var certData merchants.CertData
	account.Init(merchant.Account)
	certData.Init(merchant.CertData)
	if merchant.CertStatus == dbtypes.CertStatusPassed {
		return true
	}
	client, _ := models.GetMerchant(models.PlatformChina)
	if merchant.TargetCompanyID == 0 {
		return false
	}
	client.LoginBy(merchant.Account)
	if merchant.CertStatus == dbtypes.CertStatusSubmitted || merchant.CertStatus == dbtypes.CertStatusSubmitFailed {
		if data, err := client.GetCert(merchant.TargetCompanyID, 0); err != nil {
			certData.Reason = err.Error()
			if strings.Contains(certData.Reason, "尚未发布") {
				merchant.CertStatus = dbtypes.CertStatusNeedSubmit
				trigger.AutoFlow(merchant, c, userTrigger...)
			}
		} else if status, ok := data.(china.GetQualificationStatusRes); ok {
			certData.Check = status.Info.AuditingstatusName
			certData.Reason = ""
			certData.Id = 10000
			var cstatus dbtypes.CertStatus
			switch status.Info.Auditingstatus {
			case china.AuditingsStatusPassed:
				cstatus = dbtypes.CertStatusPassed
			case china.AuditingsStatusAuditing:
				cstatus = dbtypes.CertStatusSubmitted
			case china.AuditingsStatusRejected:
				cstatus = dbtypes.CertStatusRejected
				certData.Reason = status.Info.RefuseReason
			}
			merchant.CertStatus = cstatus
		}
		merchant.SetCertData(certData)
		services.Merchant.Save(merchant)
		if merchant.CertStatus == dbtypes.CertStatusPassed {
			return true
		}

	} else {
		//preLicense := certData.License
		if isUserTrigger && merchant.CertStatus != dbtypes.CertStatusPassed {
			if certData.Id > 0 { //修改
				if v, e := request.HTTPGet(c.License); e == nil {
					validPeriod := strings.Split(c.ValidPeriod, " - ")
					long := "0"
					if len(validPeriod) != 2 {
						return false
					}
					if validPeriod[1] == "永续经营" {
						validPeriod[1] = "9999-12-31"
						long = "1"
					}
					corpType := china.CorpTypeFromName(c.CompanyType)
					if data, err := client.GetApi().(china.PublishApi).ModifyQualification(china.ModifyQualificationReq{
						UserName:         account.UserName,
						Password:         account.Password,
						RegCode:          &c.RegNo,
						Organ:            &c.RegAuthority,
						Corptype:         &corpType,
						Artificialperson: &c.Legal,
						Registerplace:    &c.RegAddr,
						Setuptime:        &c.RegDate,
						Managetimelong:   &long,
						Managetimestart:  &validPeriod[0],
						Managetimeend:    &validPeriod[1],
						Managebound:      &c.Business,
					}.WithPic(v).WithDisplay(2)); err != nil {
						certData.Reason = err.Error()
						certData.License = c.License
						merchant.SetCertData(certData)
						merchant.CertStatus = dbtypes.CertStatusSubmitFailed
					} else if data.Succ {
						certData.Id = 10000 // 没返回。
						certData.License = c.License
						certData.Reason = ""
						merchant.SetCertData(certData)
						merchant.CertStatus = dbtypes.CertStatusSubmitted
					}
				}

			} else { // 提交
				if data, err := client.SubmitCert(*c); err != nil {
					certData.Reason = err.Error()
					certData.License = c.License
					certData.Id = 0
					merchant.SetCertData(certData)
					merchant.CertStatus = dbtypes.CertStatusSubmitFailed
					trigger.AutoFlow(merchant, c, userTrigger...)
				} else if _, ok := data.(china.SubmitQualificationRes); ok {
					certData.Id = 10000
					certData.License = c.License
					merchant.SetCertData(certData)
					merchant.CertStatus = dbtypes.CertStatusSubmitted
				}
			}

			services.Merchant.Save(merchant)
		}
	}
	return false
}
func (trigger *chinaTrigger) CalSteps(m *models.Merchant, c models.Company) (s schemas.MerchantStep) {
	s.Name = models.PlatFormName(m.PlatForm)
	var account merchants.ChinaAccount
	var certData merchants.CertData
	account.Init(m.Account)
	certData.Init(m.CertData)
	// 检查公司资料
	s.Steps = []schemas.Step{
		{
			Text:   "提交公司资料",
			Action: dbtypes.ActionUploadLincense,
			Status: schemas.StepStatusTodo,
		},
		{
			Text:   "待审核",
			Action: dbtypes.ActionNone,
			Status: schemas.StepStatusUnReach,
		},
		{
			Text:   "审核结果",
			Action: dbtypes.ActionNone,
			Status: schemas.StepStatusUnReach,
		},
	}
	if err := trigger.ValidateCompany(c); err == nil {
		s.Steps[0].Status = schemas.StepStatusDone
	} else {
		s.Steps[0].Status = schemas.StepStatusDone
		s.Steps[1].Status = schemas.StepStatusDone
		s.Steps[2].Status = schemas.StepStatusTodo
		s.Steps[2].Reason = err.Error()
		s.Steps[2].Action = dbtypes.ActionUploadLincense
		return
	}
	if m.TargetCompanyID > 0 {
		s.Steps[0].Status = schemas.StepStatusDone
		s.Steps[1].Status = schemas.StepStatusTodo
		switch m.CertStatus {
		case dbtypes.CertStatusSubmitFailed:
			s.Steps[1].Status = schemas.StepStatusDone
			s.Steps[2].Text = "提交失败，请重试"
			s.Steps[2].Reason = certData.Reason
			s.Steps[2].Action = dbtypes.ActionUploadLincense
			s.Steps[2].Status = schemas.StepStatusTodo
		case dbtypes.CertStatusRejected:
			s.Steps[1].Status = schemas.StepStatusDone
			s.Steps[2].Text = "审核未通过"
			s.Steps[2].Reason = certData.Reason
			s.Steps[2].Action = dbtypes.ActionUploadLincense
			s.Steps[2].Status = schemas.StepStatusTodo
		}

		if account.Status == china.AuditingsStatusRejected {
			s.Steps[1].Status = schemas.StepStatusDone
			s.Steps[2].Text = "审核未通过"
			s.Steps[2].Action = dbtypes.ActionUploadLincense
			s.Steps[2].Reason = account.Reason
			s.Steps[2].Status = schemas.StepStatusTodo
		} else if account.Status == china.AuditingsStatusPassed && m.CertStatus == dbtypes.CertStatusPassed {
			s.Steps[1].Status = schemas.StepStatusDone
			s.Steps[2].Text = "已开通"
			s.Steps[2].Action = dbtypes.ActionNone
			s.Steps[2].Status = schemas.StepStatusDone
		}

	} else if m.RegFailedReason != "" {
		s.Steps[1].Status = schemas.StepStatusDone
		s.Steps[2].Status = schemas.StepStatusTodo
		s.Steps[2].Text = "审核未通过"
		s.Steps[2].Action = dbtypes.ActionUploadLincense
		s.Steps[2].Reason = m.RegFailedReason
	}

	return
}

func (trigger *chinaTrigger) FormatAccount(m *models.Merchant) (ret map[string]interface{}) {
	ret = map[string]interface{}{
		"username": "",
		"password": "",
	}
	if _, ok := m.Account["username"]; ok {
		ret = m.Account
	}
	return ret
}
func (trigger *chinaTrigger) BindUser(m *models.Merchant, mobile int, password string) error {
	return nil
}
