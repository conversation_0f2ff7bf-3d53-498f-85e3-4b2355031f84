package configs

import (
	"errors"
	"github.com/kataras/iris/v12/context"
	"net/http"
)

type ApiError struct {
	Code string      `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

type CodeError struct {
	Code int   `json:"code"`
	Err  error `json:"error"`
}

func (ce CodeError) Error() string {
	return ce.Err.Error()
}

func ErrorWithCode(code int, err error) error {
	return CodeError{
		Code: code,
		Err:  err,
	}
}

func (api ApiError) Response(ctx context.Context, exit ...bool) interface{} {
	defer func() {
		if len(exit) == 0 {
			ctx.Next()
		}
	}()
	switch api.Code {
	case ErrScopeMissing.Error():
		ctx.StatusCode(http.StatusForbidden)
	case "ok":
		ctx.StatusCode(http.StatusOK)
	case ErrAuthFailed.Error():
		ctx.StatusCode(http.StatusUnauthorized)
	default:
		ctx.StatusCode(http.StatusBadRequest)
	}
	ctx.JSON(api)
	return nil
}

type ApiOkWrapper struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

func NewApiOkWrapper(data interface{}) ApiOkWrapper {
	return ApiOkWrapper{
		Code: 0,
		Msg:  "success",
		Data: data,
	}
}

var (
	ErrUnkown                   = errors.New("unkown")                      //未知错误
	ErrCallApiError             = errors.New("call_api_error")              //调用api错误
	ErrUpdateModelError         = errors.New("update_model_error")          //更新model错误
	ErrInputParamError          = errors.New("input_param_error")           //参数错误
	ErrNoDataError              = errors.New("no_data")                     //参数错误
	ErrOPDataBaseError          = errors.New("op_database_failed")          //数据库错误
	ErrAuthFailed               = errors.New("auth_failed")                 //认证失败
	ErrScopeMissing             = errors.New("scope_missing")               //权限不足
	ErrModifyPasswordFailed     = errors.New("modify_password_failed")      //修改密码失败
	ErrModifyCompanyFailed      = errors.New("modify_company_failed")       //修改公司信息失败
	ErrAddAicaigouFailed        = errors.New("add_aicaigou_failed")         //添加爱采购
	ErrEditAicaigouFailed       = errors.New("edit_aicaigou_failed")        //编辑爱采购
	ErrAddProductFailed         = errors.New("add_product_failed")          //添加商品错误
	ErrModifyProductFailed      = errors.New("modify_product_failed")       //修改商品错误
	ErrOptProductFailed         = errors.New("opt_product_failed")          //操作商品错误
	ErrPubInfoFailed            = errors.New("pub_info_failed")             //发布信息错误
	ErrImgUploadFailed          = errors.New("img_upload_failed")           //图片上传失败
	ErrAddSysUserFailed         = errors.New("add_sys_user_failed")         //增加系统用户失败
	ErrModifySysUserFailed      = errors.New("modify_sys_user_failed")      //修改系统用户失败
	ErrAddUserFailed            = errors.New("add_user_failed")             //增加用户失败
	ErrModifyUserFailed         = errors.New("modify_user_failed")          //修改用户失败
	ErrCheckModifyCompanyFailed = errors.New("check_modify_company_failed") //审核、修改企业信息失败
	ErrCheckModifyProductFailed = errors.New("check_modify_product_failed") //审核、修改产品信息失败
	ErrAddMerchantFailed        = errors.New("add_merchant_failed")         //添加商户失败
	ErrModifyMerchantFailed     = errors.New("modify_merchant_failed")      //修改商户失败
	ErrModifyInfoFailed         = errors.New("modify_info_failed")          //修改信息失败
	ErrQueryTaskFailed          = errors.New("query_task_failed")           //获取任务失败
	ErrModifyAndPubinfoFailed   = errors.New("modify_and_pubinfo_failed")   //修改和发布信息失败
	ErrTitleFinished            = errors.New("title_finished")              //标题已经用完
	ErrContentFinished          = errors.New("content_finished")            //内容已经用完
	ErrSubjectFinished          = errors.New("subject_finished")            //产品标题已经用完
	ErrProductCate              = errors.New("product_cate_error")          //产品分类设置错误
	ErrVersionTooLow            = errors.New("version_too_low")             //产品分类设置错误
	ErrDuplicatedImage          = errors.New("upload_duplicated_image")     // 重复上传
	ErrDupPub                   = errors.New("重复发布")                        // 重复发布
	ErrNoMerchant               = errors.New("信息没有指定商户")
	ErrDupImage                 = errors.New("该图片已存在")
	ErrMerchantStopped          = errors.New("商铺已经暂停")
	ErrMerchantAutoPubClosed    = errors.New("商铺没开启自动发布")
	ErrMerchantEditFailed       = errors.New("编辑商铺出错")
	ErrQuotaOut                 = errors.New("额度已用完")
)

const (
	ErrCodeLoginFailed = 10001 // 登录失败
	ErrCodeNotCerted   = 10002 // 未认证
)
