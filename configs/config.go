package configs

import (
	"log"

	"gopkg.in/ini.v1"
)

type Config struct {
	DB      DBConfig      `ini:"db"`
	Mysql   DBConfig      `ini:"mysql"`
	Web     WebConfig     `ini:"web"`
	Redis   DBConfig      `ini:"redis"`
	Hy88    Hy88Config    `ini:"hy88"`
	Swagger SwaggerConfig `ini:"swagger,omitempty"`
	Upload  UploadConfig  `ini:"upload"`
}

type Hy88Config struct {
	CHNUser     string `ini:"user"`
	CHNPassword string `ini:"password"`
	CHNID       int    `ini:"id"`
}
type DBConfig struct {
	Host     string `ini:"host"`
	Port     string `ini:"port"`
	User     string `ini:"user"`
	Password string `ini:"password"`
	Name     string `ini:"name"`
}

type WebConfig struct {
	IsProduction  bool   `ini:"is_production"`
	EnableTask    bool   `ini:"enable_task"`
	EnablePub     bool   `ini:"enable_pub"`
	AugoMigrateDb bool   `ini:"automigratedb"`
	Host          string `ini:"host"`
	Port          string `ini:"port"`
	ShowSql       bool   `ini:"showsql"`
	OemClass      string `ini:"oem"`
	JWTSecret     string `ini:"jwt_secret"`
	JWTExpDelta   int64  `ini:"jwt_exp_delta"`
	// StaticPath    string `ini:"static_path"`
	KEY_5188 string `ini:"key_5188"`
	Debug    bool   `ini:"debug"`
	MaxId    uint64 `ini:"max_id"`
}

type SwaggerConfig struct {
	Host     string `ini:"host"`
	Port     string `ini:"port"`
	User     string `ini:"user"`
	Password string `ini:"password"`
}

type UploadConfig struct {
	Url   string `ini:"url"`
	Token string `ini:"token"`
}

var ApiConfig *Config

func NewConfig(path string) (*Config, error) {
	var config Config
	log.Println("load config from:", path)
	conf, err := ini.Load(path)
	if err != nil {
		log.Println("load config file fail!")
		return nil, err
	}
	conf.BlockMode = false
	err = conf.MapTo(&config)
	if err != nil {
		log.Println("mapto config file fail!")
		return nil, err
	}

	ApiConfig = &config
	return &config, nil
}
