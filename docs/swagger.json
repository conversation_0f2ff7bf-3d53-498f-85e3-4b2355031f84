{"swagger": "2.0", "info": {"description": "This is a sample server for all_publish", "title": "all_publish API DOC", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "shunping.liu(微信:may<PERSON><PERSON><PERSON>)", "url": "https://www.go2live.cn", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0"}, "paths": {"/aicaigou": {"put": {"security": [{"ApiKeyAuth": []}], "description": "修改那个字段，提交那个字段, 直接修改", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "提交/修改爱采购用户信息", "parameters": [{"description": "Aicaigou", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.AicaigouUsers"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.AicaigouUsers"}}}}, "post": {"security": [{"ApiKeyAuth": []}], "description": "添加爱采购资料\n需要先完善公司资料，注册好搜了网", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "添加爱采购资料", "parameters": [{"description": "AicaigouUsers", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.AicaigouUsers"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.AicaigouUsers"}}}}}, "/aicaigou/me/": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取爱采购用户详情, 只能获取当前用户的", "produces": ["application/json"], "tags": ["client"], "summary": "获取爱采购用户详情", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.AicaigouUsers"}}}}}, "/aicaigou/save": {"post": {"security": [{"ApiKeyAuth": []}], "description": "已有就是编辑，没有就是保存", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "保存爱采购用户信息", "parameters": [{"description": "Aicaigou", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.AicaigouUsers"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.AicaigouUsers"}}}}}, "/areas/{pid}": {"get": {"security": [{"ApiKeyAuth": []}], "description": "传0获取省份\n不用接口，用js的方式:  http://api.huangye88.com/js/apiarea.js", "produces": ["application/json"], "tags": ["deprecated"], "summary": "根据父级id获取下一级地区", "parameters": [{"type": "integer", "description": "int", "name": "pid", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/publisher.Area"}}}}}}, "/auth": {"post": {"description": "登陆认证, 黄页88登录使用mobile(grant_type=user), 系统用户使用username(grant_type=system)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client", "sys"], "summary": "登陆认证接口", "parameters": [{"description": "login", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schemas.Login"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/schemas.LoginResult"}}}}}, "/category/{id}": {"get": {"security": [{"ApiKeyAuth": []}], "produces": ["application/json"], "tags": ["deprecated"], "summary": "获取分类详情", "parameters": [{"type": "integer", "description": "int", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/publisher.CategoryDetail"}}}}}, "/category/{id}/properties": {"get": {"security": [{"ApiKeyAuth": []}], "description": "input->(string),textarea->长文本,radio->单选按钮,checkbox->多选按钮,select->下拉框", "produces": ["application/json"], "tags": ["deprecated"], "summary": "获取属性列表", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/publisher.Property"}}}}}}, "/category/{id}/subs": {"get": {"security": [{"ApiKeyAuth": []}], "produces": ["application/json"], "tags": ["deprecated"], "summary": "获取子分类", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/publisher.Category"}}}}}}, "/company": {"put": {"security": [{"ApiKeyAuth": []}], "description": "修改那个字段，提交那个字段, 直接修改", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "提交/修改公司信息", "parameters": [{"description": "Company", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.Company"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Company"}}}}}, "/company/me/": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取公司详情, 只能获取当前用户的", "produces": ["application/json"], "tags": ["client"], "summary": "获取公司详情", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Company"}}}}}, "/companyproduct/items": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取发布产品列表 带分页\nstatus 0:等待推送 1:推送成功 3:推送失败", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "获取发布产品列表 带分页", "parameters": [{"enum": ["created_at", "updated_at"], "type": "string", "description": "string enums", "name": "sortby", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}, {"type": "string", "description": "搜索关键词，搜索标题", "name": "keyword", "in": "query"}, {"enum": ["0", "1", "3"], "type": "string", "description": "发布状态，int enums", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/services.CompanyProductPaginator"}}}}}, "/companyproduct/query": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取发布产品列表\nstatus -1:所有状态(默认值) 0:等待推送 1:推送成功 3:推送失败", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "获取发布产品列表", "parameters": [{"enum": ["created_at", "updated_at"], "type": "string", "description": "string enums", "name": "sortby", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}, {"type": "string", "description": "搜索关键词，搜索标题", "name": "keyword", "in": "query"}, {"enum": ["-1", "0", "1", "3"], "type": "string", "description": "发布状态，int enums", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.CompanyProduct"}}}}}}, "/info": {"post": {"security": [{"ApiKeyAuth": []}], "description": "手动发布信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "手动发布信息", "parameters": [{"description": "Info", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.Info"}}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Info"}}}}}}, "/info/items": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取发布信息列表 带分页\nstatus 0:全部(默认) 1:等待推送 2:推送成功 4:推送失败", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "获取发布信息列表 带分页", "parameters": [{"enum": ["created_at", "updated_at"], "type": "string", "description": "string enums", "name": "sortby", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}, {"type": "string", "description": "搜索关键词，搜索标题", "name": "keyword", "in": "query"}, {"enum": ["1", "2", "4"], "type": "string", "description": "发布状态，int enums", "name": "status", "in": "query"}, {"enum": ["auto", "manual"], "type": "string", "description": "发布类型，string enums", "name": "pub_type", "in": "query"}, {"enum": ["-1", "0", "1"], "type": "string", "description": "目标平台，int enums, 默认-1所有，0--黄页88，1--八方资源", "name": "platform", "in": "query"}, {"enum": [0, 1], "type": "integer", "description": "是否已同步到爱采购，int enums, 默认: 0--不查 1--已同步到爱采购", "name": "synced", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/services.InfoPaginator"}}}}}, "/info/query": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取发布信息列表\nstatus 0:所有状态 1:等待推送 2:推送成功 4:推送失败", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "获取发布信息列表", "parameters": [{"enum": ["created_at", "updated_at"], "type": "string", "description": "string enums", "name": "sortby", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}, {"type": "string", "description": "搜索关键词，搜索标题", "name": "keyword", "in": "query"}, {"enum": ["1", "2", "4"], "type": "string", "description": "发布状态，int enums", "name": "status", "in": "query"}, {"enum": ["auto", "manual"], "type": "string", "description": "发布类型，string enums", "name": "pub_type", "in": "query"}, {"enum": ["-1", "0", "1"], "type": "string", "description": "目标平台，int enums, 默认-1所有，0--黄页88，1--八方资源", "name": "platform", "in": "query"}, {"enum": [0, 1], "type": "integer", "description": "是否已同步到爱采购，int enums, 默认: 0--不查 1--已同步到爱采购", "name": "synced", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Info"}}}}}}, "/info/stat": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取前一天信息统计信息", "produces": ["application/json"], "tags": ["client"], "summary": "获取前一天信息统计信息", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/schemas.InfoStat"}}}}}, "/info/{id}": {"get": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "获取发布信息详情", "parameters": [{"type": "integer", "description": "信息id", "name": "id", "in": "path"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Info"}}}}}, "/merchant/enables": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取当前用户已开通的商铺列表", "produces": ["application/json"], "tags": ["client"], "summary": "获取当前用户已开通的商铺列表", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Merchant"}}}}}}, "/merchant/query": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取当前用户开通的商户列表", "produces": ["application/json"], "tags": ["client"], "summary": "获取当前用户开通的商户列表", "parameters": [{"enum": ["created_at", "updated_at"], "type": "string", "description": "string enums", "name": "sortby", "in": "query", "required": true}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Merchant"}}}}}}, "/merchant/status": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取当前用户开通的商户列表状态", "produces": ["application/json"], "tags": ["client"], "summary": "获取当前用户开通的商户列表状态", "parameters": [{"enum": ["created_at", "updated_at"], "type": "string", "description": "string enums", "name": "sortby", "in": "query", "required": true}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/schemas.MerchantStep"}}}}}}, "/merchant/{id}": {"put": {"security": [{"ApiKeyAuth": []}], "description": "提交企业声明, pic", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "提交企业声明", "parameters": [{"type": "integer", "description": "商铺id", "name": "id", "in": "path", "required": true}, {"description": "EditMerchant", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schemas.EditMerchant"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Company"}}}}}, "/openapi/rank": {"post": {"description": "添加排名，添加前，需要小调用status, 避免重复插入", "produces": ["application/json"], "tags": ["client"], "summary": "添加排名", "parameters": [{"type": "string", "description": "string", "name": "hash", "in": "query", "required": true}, {"type": "string", "description": "string", "name": "annouce", "in": "query", "required": true}, {"type": "string", "description": "url", "name": "url", "in": "query", "required": true}, {"type": "string", "description": "关键词", "name": "keyword", "in": "query", "required": true}, {"type": "string", "description": "排名", "name": "rank", "in": "query", "required": true}, {"type": "string", "description": "source:like BD", "name": "eg", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Infos"}}}}}, "/openapi/rank/status": {"get": {"description": "获取关键词列表。。 关键词组成 'word source'\nsource: baidu:百度电脑端; baidu_m:百度移动", "produces": ["application/json"], "tags": ["client"], "summary": "获取关键词列表", "parameters": [{"type": "string", "description": "string", "name": "hash", "in": "query", "required": true}, {"type": "string", "description": "string", "name": "annouce", "in": "query", "required": true}, {"type": "string", "description": "url", "name": "url", "in": "query", "required": true}, {"type": "string", "description": "关键词", "name": "keyword", "in": "query", "required": true}, {"type": "string", "description": "排名", "name": "rank", "in": "query", "required": true}, {"type": "string", "description": "source:like BD", "name": "eg", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Infos"}}}}}, "/openapi/tasks": {"get": {"description": "获取关键词列表。。 关键词组成 'word source'\nsource: baidu:百度电脑端; baidu_m:百度移动", "produces": ["application/json"], "tags": ["client"], "summary": "获取关键词列表", "parameters": [{"type": "string", "description": "string", "name": "hash", "in": "query", "required": true}, {"type": "string", "description": "string", "name": "annouce", "in": "query", "required": true}, {"type": "integer", "description": "int", "name": "limit", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string"}}}}}}, "/product": {"post": {"security": [{"ApiKeyAuth": []}], "description": "添加产品", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "添加产品", "parameters": [{"description": "Product", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.Product"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Product"}}}}}, "/product/opt/{productId}": {"get": {"security": [{"ApiKeyAuth": []}], "description": "提交审核、推广、暂停推广等操作\nquery string parmas:\nopt: 1 提交审核 2 开始推广 3 取消推广 4 删除", "produces": ["application/json"], "tags": ["client"], "summary": "操作产品", "parameters": [{"type": "integer", "description": "int enum Enum(1,2,3,4)", "name": "opt", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/product/query": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取产品列表\n0.2.57 开始字段精简", "produces": ["application/json"], "tags": ["client"], "summary": "获取产品列表", "parameters": [{"enum": ["created_at", "updated_at"], "type": "string", "description": "string enums", "name": "sortby", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/schemas.Product"}}}}}}, "/product/stat": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取产品统计信息", "produces": ["application/json"], "tags": ["client"], "summary": "获取产品统计信息", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/schemas.StatusCount"}}}}}, "/product/{productId}": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取产品详情, 只能获取当前用户的", "produces": ["application/json"], "tags": ["client"], "summary": "获取产品详情", "parameters": [{"type": "integer", "description": "productId", "name": "productId", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}, "put": {"security": [{"ApiKeyAuth": []}], "description": "修改产品描述， 只上传需要修改的部分", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "修改产品内容", "parameters": [{"type": "integer", "description": "productId", "name": "productId", "in": "path", "required": true}, {"description": "Product", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.Product"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Product"}}}}}, "/ranks": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取搜索排名列表\nquery string parmas:\neg: 0 全部 1 百度 2 360， 3 搜狗 4 神马 5头条", "produces": ["application/json"], "tags": ["client"], "summary": "获取搜索排名列表", "parameters": [{"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "description": "int enums", "name": "eg", "in": "query", "required": true}, {"type": "integer", "description": "int", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "int", "name": "debug", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/schemas.RanksData"}}}}}, "/seeks": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取收录排名列表\nquery string parmas:\neg: pc_baidu:百度电脑端; m_baidu:百度移动; pc_sogou:搜狗电脑端;m_sogou:搜狗移动;m_haosou:好搜移动;m_toutiao:头条移动", "produces": ["application/json"], "tags": ["client"], "summary": "获取收录列表", "parameters": [{"enum": ["pc_baidu", "m_baidu", "pc_sogou", "m_sogou", "m_haosou", "m_to<PERSON><PERSON>"], "type": "string", "description": "string enums", "name": "eg", "in": "query", "required": true}, {"type": "integer", "description": "int", "name": "page", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/publisher.RanksData"}}}}}, "/seeks/count": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取当天用户的收录统计", "produces": ["application/json"], "tags": ["client"], "summary": "获取收录统计", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/publisher.SeekCountData"}}}}}, "/sys/album/upload/{cid}": {"post": {"security": [{"ApiKeyAuth": []}], "description": "支持JPG/JPEG/BMP/GIF/PNG两种格式", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["sys"], "summary": "上传文件", "parameters": [{"type": "integer", "description": "cid 公司id", "name": "cid", "in": "path", "required": true}, {"type": "file", "description": "jpg,jpeg,bmp, gif, png file", "name": "file", "in": "formData", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/schemas.UploadResponse"}}}}}}, "/sys/areas/{pid}": {"get": {"security": [{"ApiKeyAuth": []}], "description": "传0获取省份\n不用接口，用js的方式:  http://api.huangye88.com/js/apiarea.js", "produces": ["application/json"], "tags": ["deprecated"], "summary": "根据父级id获取下一级地区", "parameters": [{"type": "integer", "description": "int", "name": "pid", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/publisher.Area"}}}}}}, "/sys/company/check_not_pass/{companyid}": {"put": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：company:modify", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "审核拒绝公司修改", "parameters": [{"type": "integer", "description": "companyid", "name": "companyid", "in": "path", "required": true}, {"description": "AuditNotPass", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schemas.AuditNotPass"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/sys/company/check_pass": {"post": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：company:modify\n仅需传输auditing_fields 和 id两个字段，系统将修改id对应的company信息依据auditing_fields", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "审核、修改企业信息", "parameters": [{"description": "CheckModifyCompany", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schemas.CheckModifyCompany"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Company"}}}}}, "/sys/company/query": {"get": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：company:get 或者 company:list", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "[搜索]获取[待审核]公司列表", "parameters": [{"enum": ["created_at", "updated_at"], "type": "string", "description": "string enums", "name": "sortby", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}, {"type": "string", "description": "搜索关键词，搜索公司名", "name": "keyword", "in": "query"}, {"type": "boolean", "default": true, "description": "是否只过滤等待审核的公司， string default", "name": "wait_audit", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Company"}}}}}}, "/sys/company/{id}": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取公司详情, 只能获取当前用户的", "produces": ["application/json"], "tags": ["sys"], "summary": "获取公司详情", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Company"}}}}, "put": {"security": [{"ApiKeyAuth": []}], "description": "修改那个字段，提交那个字段, 直接修改", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "提交/修改公司信息", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}, {"description": "Company", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.Company"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Company"}}}}}, "/sys/companyproduct/items": {"get": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：companyproduct:list\n状态0-待发布 1-推送成功 3-推送失败", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "获取发布信息列表 带分页", "parameters": [{"enum": ["created_at", "updated_at"], "type": "string", "description": "string enums", "name": "sortby", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}, {"type": "string", "description": "搜索关键词，搜索标题", "name": "keyword", "in": "query"}, {"type": "integer", "description": "公司id", "name": "company_id", "in": "query"}, {"enum": [0, 1, 3], "type": "integer", "description": "过滤状态， 默认全部", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/services.CompanyProductPaginator"}}}}}, "/sys/companyproduct/query": {"get": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：companyproduct:list\n状态0-待发布 1-推送成功 3-推送失败", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "获取发布信息列表", "parameters": [{"enum": ["created_at", "updated_at"], "type": "string", "description": "string enums", "name": "sortby", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}, {"type": "string", "description": "搜索关键词，搜索标题", "name": "keyword", "in": "query"}, {"type": "integer", "description": "公司id", "name": "company_id", "in": "query"}, {"enum": [0, 1, 3], "type": "integer", "description": "过滤状态， 默认全部", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.CompanyProduct"}}}}}}, "/sys/info/check_not_pass/{infoId}": {"put": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：info:modify", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "审核拒绝信息", "parameters": [{"type": "integer", "description": "infoId", "name": "infoId", "in": "path", "required": true}, {"description": "AuditNotPass", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schemas.AuditNotPass"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/sys/info/items": {"get": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：info:list", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "获取发布信息列表 带分页", "parameters": [{"enum": ["created_at", "updated_at"], "type": "string", "description": "string enums", "name": "sortby", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}, {"type": "string", "description": "搜索关键词，搜索标题", "name": "keyword", "in": "query"}, {"enum": ["auto", "manual"], "type": "string", "description": "发布类型，string enums", "name": "pub_type", "in": "query"}, {"type": "integer", "description": "公司id", "name": "company_id", "in": "query"}, {"enum": [1, 2, 3, 4], "type": "integer", "description": "过滤状态， 默认全部", "name": "status", "in": "query"}, {"enum": ["-1", "0", "1"], "type": "string", "description": "目标平台，int enums, 默认-1所有，0--黄页88，1--八方资源", "name": "platform", "in": "query"}, {"enum": [0, 1], "type": "integer", "description": "是否已同步到爱采购，int enums, 默认: 0--不查 1--已同步到爱采购", "name": "synced", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/services.InfoPaginator"}}}}}, "/sys/info/publish/{infoId}": {"put": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：info:modify\n如果信息需要，仅需传输需要修改的部分\n信息推送成功后修改部分才会保存", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "审核并发布产品信息", "parameters": [{"description": "Info", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.Info"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Info"}}}}}, "/sys/info/query": {"get": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：info:list", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "获取发布信息列表", "parameters": [{"enum": ["created_at", "updated_at"], "type": "string", "description": "string enums", "name": "sortby", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}, {"type": "string", "description": "搜索关键词，搜索标题", "name": "keyword", "in": "query"}, {"enum": ["auto", "manual"], "type": "string", "description": "发布类型，string enums", "name": "pub_type", "in": "query"}, {"type": "integer", "description": "公司id", "name": "company_id", "in": "query"}, {"enum": [1, 2, 3, 4], "type": "integer", "description": "过滤状态， 默认全部", "name": "status", "in": "query"}, {"enum": ["-1", "0", "1"], "type": "string", "description": "目标平台，int enums, 默认-1所有，0--黄页88，1--八方资源", "name": "platform", "in": "query"}, {"enum": [0, 1], "type": "integer", "description": "是否已同步到爱采购，int enums, 默认: 0--不查 1--已同步到爱采购", "name": "synced", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Info"}}}}}}, "/sys/info/{id}": {"get": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：info:list", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "获取发布信息详情", "parameters": [{"type": "integer", "description": "信息id", "name": "id", "in": "path"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Info"}}}}}, "/sys/product/check_not_pass/{productId}": {"put": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：product:modify\\", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "审核拒绝产品", "parameters": [{"type": "integer", "description": "productId", "name": "productId", "in": "path", "required": true}, {"description": "AuditNotPass", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schemas.AuditNotPass"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/sys/product/check_pass/{productId}": {"put": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：product:modify\n仅需传输需要修改的部分", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "审核、修改产品信息", "parameters": [{"type": "integer", "description": "productId", "name": "productId", "in": "path", "required": true}, {"description": "Product", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.Product"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Product"}}}}}, "/sys/product/check_to_draft/{productId}": {"put": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：product:modify\\", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "重置为草稿", "parameters": [{"type": "integer", "description": "productId", "name": "productId", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/sys/product/query": {"get": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：product:get 或者 product:list", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "[搜索]获取[待审核]产品列表", "parameters": [{"enum": ["created_at", "updated_at"], "type": "string", "description": "string enums", "name": "sortby", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}, {"type": "string", "description": "搜索关键词，搜索产品名或描述", "name": "keyword", "in": "query"}, {"type": "integer", "description": "公司id", "name": "company_id", "in": "query"}, {"enum": [1, 2, 3, 4], "type": "integer", "description": "过滤状态， 默认全部", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/schemas.AdminProduct"}}}}}}, "/sys/product/stat": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取产品统计信息", "produces": ["application/json"], "tags": ["sys"], "summary": "获取产品统计信息", "parameters": [{"type": "integer", "description": "公司id", "name": "company_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/schemas.StatusCount"}}}}}, "/sys/product/{productId}": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取产品详情", "produces": ["application/json"], "tags": ["sys"], "summary": "获取产品详情", "parameters": [{"type": "integer", "description": "productId", "name": "productId", "in": "path", "required": true}, {"enum": [0, 1], "type": "integer", "description": "int enums", "name": "check", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}, "put": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：product:modify\n仅需传输需要修改的部分", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "直接修改产品信息", "parameters": [{"type": "integer", "description": "productId", "name": "productId", "in": "path", "required": true}, {"description": "Product", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.Product"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Product"}}}}}, "/sys/stats/query": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取公司统计信息列表", "produces": ["application/json"], "tags": ["sys"], "summary": "获取公司统计信息列表", "parameters": [{"type": "string", "description": "搜索词", "name": "kw", "in": "query"}, {"enum": ["product_cnt", "product_promotion_cnt", "info_succeed_cnt", "info_succeed_cnt_yesterday", "info_failed_cnt_yesterday", "op_end_time", "ranked_cnt"], "type": "string", "description": "string enums", "name": "sortby", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/services.UserPubStatsPaginator"}}}}}, "/sys/stats/{companyId}": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取公司统计详情", "produces": ["application/json"], "tags": ["sys"], "summary": "获取公司统计详情", "parameters": [{"type": "integer", "description": "companyId", "name": "companyId", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.UserPubStatDetails"}}}}}, "/sys/task/query": {"get": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：product/info:get 或者 product/info:list\n任务接口获取到的是当前系统用户所绑定的企业等待推送的产品信息列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "获取任务列表", "parameters": [{"enum": ["created_at", "updated_at"], "type": "string", "description": "string enums", "name": "sortby", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 50, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}, {"enum": ["-1", "0", "2"], "type": "string", "description": "目标平台，int enums, 默认-1所有，0--黄页88，2--八方资源", "name": "platform", "in": "query"}, {"enum": [0, 1], "type": "integer", "description": "是否已同步到爱采购，int enums, 默认: 0--不查 1--已同步到爱采购", "name": "synced", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Info"}}}}}, "/sys/user": {"get": {"security": [{"ApiKeyAuth": []}], "description": "系统用户获取当前用户信息", "produces": ["application/json"], "tags": ["sys"], "summary": "系统用户获取当前用户信息", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.SystemUser"}}}}}, "/sys/user/client_user": {"post": {"security": [{"ApiKeyAuth": []}], "description": "用于管理员添加后台用户,需要权限：user:add", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "添加客户账号", "parameters": [{"description": "User", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.User"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.User"}}}}}, "/sys/user/client_user/query": {"get": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：user:get 或者 user:list", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "获取客户列表", "parameters": [{"enum": ["created_at", "updated_at"], "type": "string", "description": "string enums", "name": "sortby", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}, {"type": "string", "description": "搜索关键词，搜索name", "name": "keyword", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.User"}}}}}}, "/sys/user/client_user/{userId}": {"put": {"security": [{"ApiKeyAuth": []}], "description": "\"用于管理员修改用户信息,需要权限：user:modify\n仅可修改以下字段 'phone', 'email', 'ban', 'company','max_products(超级管理员才可以)'\"", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "修改用户信息", "parameters": [{"type": "integer", "description": "userId", "name": "userId", "in": "path", "required": true}, {"description": "User", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.User"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.User"}}}}}, "/sys/user/func_list": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取指定用户信息", "produces": ["application/json"], "tags": ["sys"], "summary": "获取指定用户开通功能列表", "parameters": [{"type": "integer", "description": "公司id", "name": "company_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.UserPlatform"}}}}}}, "/sys/user/modify_password": {"post": {"security": [{"ApiKeyAuth": []}], "description": "系统用户用户修改密码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "系统用户用户修改密码", "parameters": [{"description": "ModifyPassword", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schemas.ModifyPassword"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/sys/user/scopes": {"get": {"security": [{"ApiKeyAuth": []}], "description": "请求权限：system_user:add or system_user:modify", "produces": ["application/json"], "tags": ["sys"], "summary": "获取系统权限列表", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/dbtypes.Scopes"}}}}}, "/sys/user/sys_user/query": {"get": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：system_user:get 或者 system_user:list", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "获取系统用户列表", "parameters": [{"enum": ["created_at", "updated_at"], "type": "string", "description": "string enums", "name": "sortby", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}, {"type": "string", "description": "搜索关键词，搜索name", "name": "keyword", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.SystemUser"}}}}}}, "/sys/user/system_user": {"post": {"security": [{"ApiKeyAuth": []}], "description": "用于管理员添加后台用户,需要权限：system_user:add", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "添加系统用户", "parameters": [{"description": "SystemUser", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.SystemUser"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.SystemUser"}}}}}, "/sys/user/system_user/{userId}": {"put": {"security": [{"ApiKeyAuth": []}], "description": "\"用于管理员修改后台用户,需要权限：system_user:modify\n仅可修改以下字段 'phone', 'email', 'ban', 'scopes', 'role','name'\"", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "修改系统用户", "parameters": [{"type": "integer", "description": "userId", "name": "userId", "in": "path", "required": true}, {"description": "SystemUser", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schemas.ModifySystemUser"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.SystemUser"}}}}}, "/sys/{companyId}/aicaigou": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取爱采购用户详情, 只能获取当前用户的", "produces": ["application/json"], "tags": ["sys"], "summary": "获取爱采购用户详情", "parameters": [{"type": "integer", "description": "companyId", "name": "companyId", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.AicaigouUsers"}}}}, "put": {"security": [{"ApiKeyAuth": []}], "description": "修改那个字段，提交那个字段, 直接修改", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "提交/修改爱采购用户信息", "parameters": [{"type": "integer", "description": "companyId", "name": "companyId", "in": "path", "required": true}, {"description": "Aicaigou", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.AicaigouUsers"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.AicaigouUsers"}}}}, "post": {"security": [{"ApiKeyAuth": []}], "description": "添加爱采购资料\n需要先完善公司资料，注册好搜了网", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "添加爱采购资料", "parameters": [{"type": "integer", "description": "companyId", "name": "companyId", "in": "path", "required": true}, {"description": "AicaigouUsers", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.AicaigouUsers"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.AicaigouUsers"}}}}}, "/sys/{companyId}/aicaigou/save": {"post": {"security": [{"ApiKeyAuth": []}], "description": "已有就是编辑，没有就是保存", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "保存爱采购用户信息", "parameters": [{"description": "Aicaigou", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.AicaigouUsers"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.AicaigouUsers"}}}}}, "/sys/{companyId}/merchant": {"post": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：company:modify or add\n仅需传输需要修改的部分", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "为某公司添加商户信息", "parameters": [{"type": "integer", "description": "companyId", "name": "companyId", "in": "path", "required": true}, {"description": "Merchant", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.Merchant"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Merchant"}}}}}, "/sys/{companyId}/merchant/meta": {"get": {"security": [{"ApiKeyAuth": []}], "description": "<company_id> 可传任意值 需要权限：company:get 或者 company:list 、company:add 、company:modify", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "获取商户Meta信息", "parameters": [{"type": "integer", "description": "companyId", "name": "companyId", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.MerchantMeta"}}}}}}, "/sys/{companyId}/merchant/query": {"get": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：company:get 或者 company:list", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "获取某个公司的商户列表", "parameters": [{"type": "integer", "description": "companyId", "name": "companyId", "in": "path", "required": true}, {"enum": ["created_at", "updated_at"], "type": "string", "description": "string enums", "name": "sortby", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "string enums", "name": "order", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Merchant"}}}}}}, "/sys/{companyId}/merchant/{merchantId}": {"put": {"security": [{"ApiKeyAuth": []}], "description": "需要权限：company:modify\n仅需传输需要修改的部分", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sys"], "summary": "修改某公司的某个商户信息", "parameters": [{"type": "integer", "description": "companyId", "name": "companyId", "in": "path", "required": true}, {"type": "integer", "description": "merchantId", "name": "merchantId", "in": "path", "required": true}, {"description": "Merchant", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.Merchant"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/schemas.Album"}}}}}, "/tools/dig/keywords": {"get": {"security": [{"ApiKeyAuth": []}], "description": "返回推荐的关键词", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["common"], "summary": "数据挖掘", "parameters": [{"type": "string", "description": "string", "name": "kw", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/configs.ApiOkWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/tools/dig/materials": {"get": {"security": [{"ApiKeyAuth": []}], "description": "返回推荐的素材", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["common"], "summary": "数据挖掘", "parameters": [{"type": "string", "description": "string", "name": "kw", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/configs.ApiOkWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/tools/similar": {"post": {"security": [{"ApiKeyAuth": []}], "description": "最后一条和前面的一一对比", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["common"], "summary": "检查重复度", "parameters": [{"description": "Similar", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schemas.Similar"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/configs.ApiOkWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/upload/img": {"post": {"security": [{"ApiKeyAuth": []}], "description": "支持JPG PNG两种格式", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["deprecated"], "summary": "上传文件", "parameters": [{"type": "file", "description": "jpg/png file", "name": "file", "in": "formData", "required": true}], "responses": {"200": {"description": "url_path", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/configs.ApiError"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/user": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取当前用户信息", "produces": ["application/json"], "tags": ["client"], "summary": "获取当前用户信息", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.User"}}}}}, "/user/func_list": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取当前用户信息", "produces": ["application/json"], "tags": ["client"], "summary": "获取当前用户开通功能列表", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.UserPlatform"}}}}}}, "/user/modify_password": {"post": {"security": [{"ApiKeyAuth": []}], "description": "用户修改密码, 未启用", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "用户修改密码", "parameters": [{"description": "ModifyPassword", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schemas.ModifyPassword"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/user/redirect": {"get": {"security": [{"ApiKeyAuth": []}], "description": "免登陆到黄页88页面", "tags": ["client"], "summary": "免登陆到黄页88页面", "parameters": [{"type": "string", "description": "要跳转的huangye88页面", "name": "url", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/configs.ApiOkWrapper"}}}}}, "/v2/album": {"post": {"security": [{"ApiKeyAuth": []}], "description": "创建相册", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "创建相册", "parameters": [{"description": "Album", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schemas.Album"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/schemas.AlbumResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/configs.ApiError"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/v2/album/": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取当前用户的相册列表", "produces": ["application/json"], "tags": ["client"], "summary": "获取相册列表", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Album"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/configs.ApiError"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/v2/album/images/": {"delete": {"security": [{"ApiKeyAuth": []}], "description": "删除照片，其实是改状态", "produces": ["application/json"], "tags": ["client"], "summary": "删除照片", "parameters": [{"description": "DelImages", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schemas.DelImages"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/configs.ApiError"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/configs.ApiError"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/v2/album/images/moveto/{id}": {"post": {"security": [{"ApiKeyAuth": []}], "description": "把图片从一个相册移动到另一个相册", "produces": ["application/json"], "tags": ["client"], "summary": "移动图片", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}, {"description": "MoveImages", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schemas.MoveImages"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/configs.ApiError"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/configs.ApiError"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/v2/album/images/{id}": {"put": {"security": [{"ApiKeyAuth": []}], "description": "修改图片信息，只能改名字", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "修改图片", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}, {"description": "album", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.Image"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Image"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/configs.ApiError"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/v2/album/stat/": {"get": {"security": [{"ApiKeyAuth": []}], "description": "相册总数， 图片总数， 购买的图片数， 赠送的图片数， 已上传图片数，可上传图片数。", "produces": ["application/json"], "tags": ["client"], "summary": "获取相册统计数据", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.UserStat"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/configs.ApiError"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/v2/album/upload/{id}": {"post": {"security": [{"ApiKeyAuth": []}], "description": "支持JPG/JPEG/BMP/GIF/PNG两种格式", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["client"], "summary": "上传文件", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}, {"type": "file", "description": "jpg,jpeg,bmp, gif, png file", "name": "file", "in": "formData", "required": true}, {"type": "integer", "description": "上传目的，默认0-上传到相册。1-公司营业执照。2-八方资源企业证明。3-爱采购营业执照。4-爱采购合同文件", "name": "purpose", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/schemas.UploadResponse"}}}}}}, "/v2/album/{id}": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取相册内图片列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "获取相册内图片列表", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}, {"type": "integer", "description": "默认20", "name": "limit", "in": "query"}, {"type": "integer", "description": "默认0", "name": "offset", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Image"}}}}}, "put": {"security": [{"ApiKeyAuth": []}], "description": "修改相册", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "修改相册", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}, {"description": "album", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schemas.Album"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Album"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/configs.ApiError"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}, "delete": {"security": [{"ApiKeyAuth": []}], "description": "删除相册，干4件事。1是把相册里的图片转移到默认相册。 2. 更新默认相册属性，增加图片数和文件总大小。3 更新相册统计表中的相册数. 4是删相册记录", "produces": ["application/json"], "tags": ["client"], "summary": "删除相册", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/configs.ApiError"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/configs.ApiError"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/configs.ApiError"}}}}}, "/v2/album/{id}/images/": {"get": {"security": [{"ApiKeyAuth": []}], "description": "根据图片名字做模糊索索", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["client"], "summary": "搜索图片", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "关键词", "name": "kw", "in": "query", "required": true}, {"type": "integer", "default": 20, "description": "int default", "name": "limit", "in": "query", "required": true}, {"type": "integer", "default": 0, "description": "int default", "name": "offset", "in": "query", "required": true}, {"type": "integer", "description": "默认20", "name": "limit", "in": "query"}, {"type": "integer", "description": "默认0", "name": "offset", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Image"}}}}}}, "/v2/areas/all": {"get": {"description": "http://api.huangye88.com/js/apiarea.js的發發助手版本", "produces": ["text/html", "application/json", "text/xml", "application/javascript"], "tags": ["common"], "summary": "js<PERSON>", "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/v2/areas/check": {"get": {"security": [{"ApiKeyAuth": []}], "description": "检查分类是否ok\n只检查已经开通的平台，如果没有对应的数据，需要提示用户。", "produces": ["application/json"], "tags": ["common"], "summary": "检查地区是否ok", "parameters": [{"type": "integer", "description": "地区id", "name": "id", "in": "query", "required": true}, {"type": "integer", "description": "公司id", "name": "cid", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string"}}}}}}, "/v2/areas/{pid}": {"get": {"description": "传0获取省份和直辖市\n不用接口，用js的方式:  http://api.huangye88.com/js/apiarea.js", "produces": ["application/json"], "tags": ["common"], "summary": "根据父级id获取下一级地区", "parameters": [{"type": "integer", "description": "int", "name": "pid", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/schemas.Area"}}}}}}, "/v2/category": {"get": {"produces": ["text/html", "application/json", "text/xml", "application/javascript"], "tags": ["common"], "summary": "获取所有分类,level版", "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/v2/category/check": {"get": {"security": [{"ApiKeyAuth": []}], "description": "只检查已经开通的平台，如果没有对应的数据，需要提示用户。", "produces": ["application/json"], "tags": ["common"], "summary": "检查分类是否ok", "parameters": [{"type": "integer", "description": "分类id", "name": "id", "in": "query", "required": true}, {"type": "integer", "description": "公司id", "name": "cid", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string"}}}}}}, "/v2/category/tree": {"get": {"produces": ["text/html", "application/json", "text/xml", "application/javascript"], "tags": ["common"], "summary": "获取所有分类, 树版", "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/v2/category/{id}": {"get": {"produces": ["application/json"], "tags": ["common"], "summary": "获取分类详情", "parameters": [{"type": "integer", "description": "int", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Cat"}}}}}, "/v2/category/{id}/properties": {"get": {"security": [{"ApiKeyAuth": []}], "description": "input->(string),textarea->长文本,radio->单选按钮,checkbox->多选按钮,select->下拉框", "produces": ["application/json"], "tags": ["common"], "summary": "获取属性列表", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/schemas.CatField"}}}}}}, "/v2/category/{id}/subs": {"get": {"security": [{"ApiKeyAuth": []}], "description": "id=0表示获取1级分类。。其他为具体分类的子分类", "produces": ["application/json"], "tags": ["common"], "summary": "获取子分类", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/schemas.Cat"}}}}}}, "/v2/ranks": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取搜索排名列表\nquery string parmas:\neg: 0 全部(默认值) 1 百度 2 360， 3 搜狗 4 神马 5头条\nplatform: -1 全部(默认值) 0 黄页88 1 八方资源 2 中国供应商 4 sole网  5 爱采购", "produces": ["application/json"], "tags": ["client"], "summary": "获取搜索排名列表", "parameters": [{"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "description": "int enums", "name": "eg", "in": "query", "required": true}, {"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "description": "int enums", "name": "pf", "in": "query", "required": true}, {"type": "integer", "description": "int", "name": "page", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Rank"}}}}}}, "definitions": {"configs.ApiError": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}}, "configs.ApiOkWrapper": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "object"}, "msg": {"type": "string"}}}, "dbtypes.JSONArray": {"type": "array", "items": {"type": "string"}}, "dbtypes.ScopeItem": {"type": "object", "properties": {"actions": {"type": "array", "items": {"type": "string"}}, "description": {"type": "string"}, "entity": {"type": "string"}, "scope": {"type": "string"}}}, "dbtypes.Scopes": {"type": "object", "properties": {"actions": {"type": "object", "additionalProperties": {"type": "string"}}, "scopes": {"type": "array", "items": {"$ref": "#/definitions/dbtypes.ScopeItem"}}}}, "models.AccountItem": {"type": "object", "properties": {"desc": {"type": "string"}, "type": {"type": "string"}}}, "models.AicaigouUsers": {"type": "object", "required": ["LinkPhone", "brank_name", "business_img", "company_logo", "company_name", "company_type", "company_web", "contract_begin_date", "contract_end_date", "contract_file", "link_email", "link_person", "open_branch", "social_credit_code"], "properties": {"LinkPhone": {"description": "注册手机号", "type": "string"}, "bank_areaids": {"description": "银行地址 必填字段 [\"省id\",\"市id\",\"县id\"], 至少选到第二级", "type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "brank_name": {"description": "银行名称", "type": "string"}, "business_img": {"description": "营业执照url路径", "type": "string"}, "card_number": {"description": "银行卡号", "type": "string"}, "company_areaids": {"description": "公司地址 必填字段 [\"省id\",\"市id\",\"县id\"], 至少选到第二级", "type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "company_id": {"description": "关联登录用户", "type": "integer"}, "company_logo": {"description": "公司logo 170x170 jpg", "type": "string"}, "company_name": {"description": "与执照上公司名一致", "type": "string"}, "company_type": {"description": "企业或个体工商户", "type": "string"}, "company_web": {"description": "公司网址", "type": "string"}, "contract_begin_date": {"description": "合同开始日期 yyyy-MM-dd格式", "type": "string"}, "contract_end_date": {"description": "合同结束日期 yyyy-MM-dd格式", "type": "string"}, "contract_file": {"description": "合同链接URL 8M以内，pdf格式", "type": "string"}, "inter_bank_num": {"description": "银联号", "type": "string"}, "link_email": {"description": "邮箱地址", "type": "string"}, "link_person": {"description": "联系人", "type": "string"}, "open_branch": {"description": "开户支行", "type": "string"}, "product_type": {"description": "产品套餐类型(Int类型) 默认1", "type": "integer"}, "social_credit_code": {"description": "社会统一信用代码", "type": "string"}}}, "models.Album": {"type": "object", "properties": {"company_id": {"description": "公司id, 因为其他表和接口都是用的companyId, 统一使用这个。", "type": "integer"}, "cover": {"description": "封面", "type": "string"}, "created_at": {"type": "string"}, "description": {"description": "相册描述", "type": "string"}, "display_sizes": {"description": "用户可见总大小", "type": "integer"}, "display_total": {"description": "用户可见文件数", "type": "integer"}, "id": {"description": "主键自增id", "type": "integer"}, "is_open": {"description": "是否公开，暂未启用。", "type": "integer"}, "main": {"description": "主相册，也是默认相册。名字不能修改", "type": "boolean"}, "name": {"description": "相册名字", "type": "string"}, "sizes": {"description": "文件大小综合。单位字节", "type": "integer"}, "status": {"description": "相册状态, 暂未启用", "type": "integer"}, "total": {"description": "文件数", "type": "integer"}, "updated_at": {"type": "string"}}}, "models.Cat": {"type": "object", "properties": {"active": {"type": "integer"}, "afsChannel": {"type": "string"}, "afsKey": {"type": "string"}, "baike": {"type": "integer"}, "bk": {"type": "string"}, "bkurl": {"type": "string"}, "buymessages": {"type": "integer"}, "catEn": {"type": "string"}, "catName": {"type": "string"}, "catTrace": {"type": "string"}, "catTraceEn": {"type": "string"}, "ddescription": {"type": "string"}, "description": {"type": "string"}, "descriptionQg": {"type": "string"}, "descriptionSpecial": {"type": "string"}, "dkeyword": {"type": "string"}, "dname": {"type": "string"}, "dtitle": {"type": "string"}, "friend": {"type": "string"}, "friendName": {"type": "string"}, "hotTitle": {"type": "string"}, "id": {"type": "integer"}, "isHot": {"type": "boolean"}, "itemTemplate": {"type": "string"}, "jpmark": {"type": "integer"}, "level": {"type": "integer"}, "messages": {"type": "integer"}, "mitemTemplate": {"type": "string"}, "module": {"type": "string"}, "orderID": {"type": "integer"}, "pageDescriptionB2blist": {"type": "string"}, "pageDescriptionQg": {"type": "string"}, "pageDescriptionSpecial": {"type": "string"}, "pageKeywordB2blist": {"type": "string"}, "pageKeywordQg": {"type": "string"}, "pageKeywordSpecial": {"type": "string"}, "pageTitle": {"type": "string"}, "pageTitleB2blist": {"type": "string"}, "pageTitleQg": {"type": "string"}, "pageTitleSpecial": {"type": "string"}, "parentID": {"type": "integer"}, "pr": {"type": "integer"}, "replacekeyword": {"type": "string"}, "replaceproduct": {"type": "string"}, "searchword": {"type": "string"}, "seoDescription": {"type": "string"}, "seoKeyword": {"type": "string"}, "seoTitle": {"type": "string"}, "shorttitle": {"type": "string"}, "subdomain": {"type": "integer"}, "templete": {"type": "integer"}, "type": {"type": "integer"}, "validate": {"type": "boolean"}, "video": {"type": "string"}}}, "models.Company": {"type": "object", "properties": {"address": {"description": "公司详细地址，展示用, 51sole 为必填字段", "type": "string"}, "advisor": {"description": "管理员信息", "type": "object", "$ref": "#/definitions/models.SystemUser"}, "advisor_id": {"description": "管理员id, 后台用", "type": "integer"}, "area_names": {"description": "和 areaids 对应的名字", "type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "areaids": {"description": "公司地址 必填字段 [\"省id\",\"市id\",\"县id\"], 至少选到第二级", "type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "audit_res": {"description": "拒绝原因, 未启用", "type": "string"}, "auditing_fields": {"description": "修改的字段, 未启用", "type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "business": {"description": "营业执照信息:经营范围,business,string,0,1000 必填,数据库管理及技术开发；计算机系统分析；计算机技术咨询；计算机软、硬件的技术开发、系统集成及销售；电子商务的技术开发；经营电子商务；计算机网络技术的研发。(法律、行政法规、国务院决定规定在经营前须经批准的项目除外）", "type": "string"}, "can_edit": {"description": "是否能编辑", "type": "boolean"}, "cate": {"description": "公司行业, 必填字段, 需要选到最后一级（第三级或第四级）", "type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "cate_names": {"description": "分类名字，和 cate 一一对应", "type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "company_type": {"description": "公司类型(51sole必填)， 枚举类型：私营企业，国有企业，集体所有制企业，合资企业，外资企业，股份企业，个体经营，事业单位，社会团体，个人，其他", "type": "string"}, "contact_name": {"description": "公司联系人, 必填字段", "type": "string"}, "corp_type": {"description": "营业执照信息:公司类型,corp_type,string,0,50 必填,有限责任公司", "type": "string"}, "created_at": {"type": "string"}, "email": {"description": "公司邮箱（51sole必填)", "type": "string"}, "gender": {"description": "性别", "type": "integer"}, "id": {"type": "integer"}, "introduce": {"description": "公司介绍, 必填字段", "type": "string"}, "legal": {"description": "营业执照信息:公司法人(法定代表人),legal,string,0,20 必填,舒开勇", "type": "string"}, "license": {"description": "营业执照, 必填字段", "type": "string"}, "logo": {"description": "公司Logo, 选填", "type": "string"}, "main_brand": {"description": "主要品牌(51sole必填)", "type": "string"}, "main_product": {"description": "主营产品, 必填项", "type": "string"}, "name": {"description": "公司名称, 必填字段, 不超过50字", "type": "string"}, "op_end_time": {"description": "运营到期时间，默认为空", "type": "string"}, "phone": {"description": "联系电话, 必填字段 [手机,公司座机，座机]", "type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "qq": {"description": "QQ, 51sole是必填字段", "type": "string"}, "qrcode": {"description": "公司二维码", "type": "string"}, "reason": {"description": "不能编辑的原因", "type": "string"}, "reg_addr": {"description": "营业执照信息:注册地址(住所),reg_addr,string,0,100 必填,广东深圳", "type": "string"}, "reg_authority": {"description": "营业执照信息:注册机构,reg_authority,string,0,100 必填,深圳市市场监督管理局宝安局", "type": "string"}, "reg_date": {"description": "营业执照信息:注册时间(成立时间),reg_date,string,0,15 必填,2014-12-19", "type": "string"}, "reg_money": {"description": "营业执照信息:注册资金,reg_money,string,0,50 必填,10万人民币", "type": "string"}, "reg_no": {"description": "营业执照信息:统一社会信用代码 ,reg_no,string,0,50 必填,440306111877015", "type": "string"}, "short_name": {"description": "公司简称，51搜了需要", "type": "string"}, "site": {"description": "黄页88网站,其他的平台在商户表里", "type": "string"}, "updated_at": {"type": "string"}, "valid_period": {"description": "营业执照信息:有效时间(营业期限),valid_period,string,0,50 必填,2014-12-19 - 永续经营", "type": "string"}, "working_model": {"description": "经营模式(51sole必填),枚举类型：生产型，贸易型，服务型，政府，其他机构", "type": "string"}}}, "models.CompanyProduct": {"type": "object", "properties": {"company": {"type": "object", "$ref": "#/definitions/models.Company"}, "company_id": {"type": "integer"}, "content": {"type": "string"}, "created_at": {"type": "string"}, "failed_reason": {"type": "string"}, "id": {"type": "integer"}, "pic": {"type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "price": {"type": "number"}, "product": {"type": "object", "$ref": "#/definitions/models.Product"}, "product_id": {"description": "为空时用来判断是否自动发布的。必须用指针", "type": "integer"}, "pub_res": {"type": "string"}, "status": {"type": "integer"}, "subject": {"type": "string"}, "unit": {"type": "string"}, "updated_at": {"type": "string"}, "will_pub_at": {"type": "string"}}}, "models.Image": {"type": "object", "properties": {"album_id": {"type": "integer"}, "company_id": {"description": "公司id", "type": "integer"}, "created_at": {"type": "string"}, "hash": {"description": "文件hash, 用于滤重的", "type": "string"}, "id": {"description": "主键自增id", "type": "integer"}, "name": {"description": "文件名字, 支持修改", "type": "string"}, "size": {"description": "文件大小。单位字节", "type": "integer"}, "status": {"description": "状态。 0 为可见状态。1 为不可见状态。删除图片就是标记为不可见状态", "type": "integer"}, "tag": {"description": "tag 自定义标签, 支持修改，暂时没有启用", "type": "string"}, "updated_at": {"type": "string"}, "url": {"description": "文件url", "type": "string"}, "using": {"description": "0 未使用 1 已使用。 保留用。状态为0的可以真实删除。", "type": "integer"}}}, "models.Info": {"type": "object", "properties": {"audit_res": {"type": "string"}, "company": {"type": "object", "$ref": "#/definitions/models.Company"}, "company_id": {"type": "integer"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "failed_reason": {"type": "string"}, "id": {"type": "integer"}, "pic": {"type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "plat_form_name": {"description": "平台名称", "type": "string"}, "platform": {"description": "平台", "type": "integer"}, "price": {"type": "number"}, "product": {"type": "object", "$ref": "#/definitions/models.Product"}, "product_id": {"description": "为空时用来判断是否自动发布的。必须用指针", "type": "integer"}, "pub_res": {"type": "string"}, "status": {"type": "integer"}, "title": {"type": "string"}, "titlepic": {"description": "新增标题图片HY-5170", "type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "unit": {"type": "string"}, "updated_at": {"type": "string"}, "will_pub_at": {"type": "string"}, "word": {"type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}}}, "models.Infos": {"type": "object", "properties": {"company_id": {"description": "公司id", "type": "integer"}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "keywords": {"description": "关键词", "type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "mid": {"description": "第三方平台信息id", "type": "integer"}, "plat_form_name": {"description": "平台名称", "type": "string"}, "platform": {"description": "哪个平台的信息", "type": "integer"}, "title": {"description": "标题", "type": "string"}, "updated_at": {"type": "string"}, "url": {"description": "第三方平台信息url", "type": "string"}}}, "models.Merchant": {"type": "object", "properties": {"account": {"type": "string"}, "action": {"description": "需要进一步处理 0-不需要处理 1-上传营业执照 2-上传企业声明 3-编辑爱采购", "type": "integer"}, "auto_pub": {"type": "boolean"}, "auto_pub_time": {"type": "string"}, "base_cnt": {"description": "基础信息条数", "type": "integer"}, "company": {"type": "object", "$ref": "#/definitions/models.Company"}, "company_id": {"description": "这里是指發發助手的公司ID", "type": "integer"}, "company_site": {"description": "第三方公司的商铺网址", "type": "string"}, "contact": {"type": "string"}, "contact_phone": {"type": "string"}, "created_at": {"type": "string"}, "daily_pub_products": {"type": "integer"}, "deletedAt": {"type": "string"}, "enable_post_product": {"description": "控制产品自动发布", "type": "boolean"}, "ext": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "pause": {"type": "boolean"}, "pause_reason": {"type": "string"}, "plat_form": {"type": "integer"}, "plat_form_name": {"description": "平台名称", "type": "string"}, "product_auto_pub_time": {"type": "string"}, "product_pause_reason": {"type": "string"}, "pub_count": {"description": "控制信息自动发布", "type": "integer"}, "pub_per_count": {"description": "单个产品每日发布量", "type": "integer"}, "published_cnt": {"description": "已发布数量", "type": "integer"}, "re_mark": {"description": "备注", "type": "string"}, "reason": {"description": "原因。", "type": "string"}, "renew_add_cnt": {"description": "续费一次增加的条数", "type": "integer"}, "renew_count": {"type": "integer"}, "status": {"description": "当前状态。文本描述。内容为空为正常", "type": "string"}, "target_company_id": {"description": "第三方公司ID", "type": "integer"}, "total": {"description": "发布总量。平台配置。特殊情况：-2 标示黄页88没开通。", "type": "integer"}, "updated_at": {"type": "string"}}}, "models.MerchantMeta": {"type": "object", "properties": {"description": {"type": "string"}, "home_url": {"type": "string"}, "logo_url": {"type": "string"}, "name": {"type": "string"}, "platFormName": {"description": "平台名称", "type": "string"}, "plat_form": {"type": "integer"}, "required_account": {"type": "object", "additionalProperties": {"$ref": "#/definitions/models.AccountItem"}}}}, "models.PostName": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}}, "models.Product": {"type": "object", "properties": {"active": {"description": "是否活跃版本。已有改成active=1为活跃版本。", "type": "integer"}, "alias": {"type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "area_names": {"description": "和 areaids 对应的名字", "type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "areaids": {"description": "[\"省id\",\"市id\",\"县id\"], 都填0，表示全国", "type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "audit_res": {"type": "string"}, "audit_res2": {"type": "string"}, "bad_words": {"description": "违禁词", "type": "string"}, "brand": {"description": "必填项", "type": "string"}, "cate": {"type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "cate_names": {"description": "分类名字，和 cate 一一对应", "type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "company": {"type": "object", "$ref": "#/definitions/models.Company"}, "company_id": {"type": "integer"}, "created_at": {"type": "string"}, "currentId": {"description": "CurrentId 当前使用的版本号", "type": "integer"}, "faq": {"type": "string"}, "id": {"type": "integer"}, "inventory": {"type": "integer"}, "material": {"type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "min_order": {"type": "integer"}, "modify_fields": {"type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "name": {"type": "string"}, "pic": {"type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "platforms": {"description": "{'1':{name:'黄页88',on:true}, '2':{name:'八方资源',on:false}}", "type": "string"}, "price": {"type": "number"}, "properties": {"type": "string"}, "status": {"type": "integer"}, "title": {"type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "titlepic": {"description": "新增标题图片HY-5170", "type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "unit": {"type": "string"}, "updated_at": {"type": "string"}, "use_alias": {"type": "boolean"}, "used_title": {"type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "word": {"type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}}}, "models.Rank": {"type": "object", "properties": {"company_id": {"description": "哪个用户的", "type": "integer"}, "created_at": {"type": "string"}, "eg": {"description": "搜索引擎", "type": "string"}, "id": {"type": "integer"}, "keyword": {"description": "关键词", "type": "string"}, "plat_form_name": {"description": "平台名称", "type": "string"}, "platform": {"description": "哪个平台的信息", "type": "integer"}, "rank": {"description": "排名 页码+排名", "type": "integer"}, "snap": {"description": "快照", "type": "string"}, "updated_at": {"type": "string"}, "url": {"description": "链接url", "type": "string"}}}, "models.SystemUser": {"type": "object", "properties": {"ban": {"type": "boolean"}, "created_at": {"type": "string"}, "email": {"type": "string"}, "id": {"type": "integer"}, "last_login_at": {"type": "string"}, "name": {"type": "string"}, "password": {"type": "string"}, "phone": {"type": "string"}, "role": {"type": "string"}, "scopes": {"type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "updated_at": {"type": "string"}, "username": {"type": "string"}}}, "models.User": {"type": "object", "properties": {"ban": {"type": "boolean"}, "company": {"type": "object", "$ref": "#/definitions/models.Company"}, "company_id": {"type": "integer"}, "created_at": {"type": "string"}, "dialy_items": {"type": "integer"}, "email": {"type": "string"}, "expire_time": {"type": "string"}, "id": {"type": "integer"}, "last_login_at": {"type": "string"}, "max_products": {"type": "integer"}, "oem_class": {"type": "string"}, "oem_id": {"type": "integer"}, "phone": {"type": "string"}, "post_buy_time": {"type": "string"}, "post_expire_time": {"type": "string"}, "post_names": {"type": "array", "items": {"$ref": "#/definitions/models.PostName"}}, "rank_buy_time": {"type": "string"}, "rank_expire_time": {"type": "string"}, "seek_buy_time": {"type": "string"}, "seek_expire_time": {"type": "string"}, "total_items": {"type": "integer"}, "updated_at": {"type": "string"}, "username": {"type": "string"}}}, "models.UserPlatform": {"type": "object", "properties": {"company_id": {"type": "integer"}, "created_at": {"type": "string"}, "expire_time": {"type": "string"}, "funcname": {"type": "string"}, "plat_form_name": {"description": "平台名称", "type": "string"}, "platform": {"type": "integer"}, "status": {"type": "integer"}, "updated_at": {"type": "string"}}}, "models.UserPubStat": {"type": "object", "properties": {"advisor_id": {"description": "顾问Id", "type": "integer"}, "advisor_name": {"description": "顾问名字", "type": "string"}, "company_id": {"description": "公司id", "type": "integer"}, "company_name": {"description": "公司名，方便搜索", "type": "string"}, "created_at": {"type": "string"}, "info_failed_cnt_yesterday": {"description": "昨日推送失败的信息数", "type": "integer"}, "info_succeed_cnt": {"description": "推送成功的信息数", "type": "integer"}, "info_succeed_cnt_yesterday": {"description": "昨日推送成功的信息数", "type": "integer"}, "op_end_time": {"description": "运营到期时间", "type": "string"}, "product_cnt": {"description": "添加的产品数", "type": "integer"}, "product_promotion_cnt": {"description": "推广中的产品数", "type": "integer"}, "ranked_cnt": {"description": "排名总数", "type": "integer"}, "updated_at": {"type": "string"}, "user_name": {"description": "用户名，方便搜索", "type": "string"}}}, "models.UserPubStatDetails": {"type": "object", "properties": {"company_id": {"description": "公司id", "type": "integer"}, "created_at": {"type": "string"}, "info_stats": {"description": "信息统计", "type": "string"}, "info_stats_yesterday": {"description": "昨日信息统计", "type": "string"}, "product_stats": {"description": "产品统计", "type": "string"}, "ranked_stats": {"description": "排名统计", "type": "string"}, "updated_at": {"type": "string"}}}, "models.UserStat": {"type": "object", "properties": {"album_cnt": {"description": "相册数", "type": "integer"}, "buy_photos": {"description": "购买的图片数", "type": "integer"}, "can_uploaded": {"description": "可以上传的图片数。。可计算得出， 2000+buy_photos-uploaded_cnt", "type": "integer"}, "company_id": {"description": "公司id", "type": "integer"}, "created_at": {"type": "string"}, "dig_keyword_limit": {"description": "挖掘关键词 可用次数", "type": "integer"}, "dig_keyword_used": {"description": "挖掘关键词 已用次数", "type": "integer"}, "dig_materials_limit": {"description": "挖掘物料 可用次数", "type": "integer"}, "dig_materials_used": {"description": "挖掘物料 已用次数", "type": "integer"}, "photo_cnt": {"description": "图片总数, 可查看的图片数，不包括删除的", "type": "integer"}, "updated_at": {"type": "string"}, "uploaded_cnt": {"description": "上传的图片数，包括被删除的 uploaded_cnt >= photo_cnt", "type": "integer"}}}, "publisher.Area": {"type": "object", "properties": {"area_en": {"type": "string"}, "area_name": {"type": "string"}, "area_trace": {"type": "string"}, "area_trace_en": {"type": "string"}, "id": {"type": "string"}}}, "publisher.Category": {"type": "object", "properties": {"cat_en": {"type": "string"}, "cat_name": {"type": "string"}, "cat_trace": {"type": "string"}, "cat_trace_en": {"type": "string"}, "id": {"type": "string"}}}, "publisher.CategoryDetail": {"type": "object", "properties": {"active": {"type": "string"}, "afs_channel": {"type": "string"}, "afs_key": {"type": "string"}, "baike": {"type": "string"}, "bk": {"type": "string"}, "bkurl": {"type": "string"}, "buymessages": {"type": "string"}, "cat_en": {"type": "string"}, "cat_name": {"type": "string"}, "cat_trace": {"type": "string"}, "cat_trace_en": {"type": "string"}, "ddescription": {"type": "string"}, "description": {"type": "string"}, "description_qg": {"type": "string"}, "description_special": {"type": "string"}, "dkeyword": {"type": "string"}, "dname": {"type": "string"}, "dtitle": {"type": "string"}, "friend": {"type": "string"}, "friend_name": {"type": "string"}, "hot_title": {"type": "string"}, "id": {"type": "string"}, "is_hot": {"type": "string"}, "item_template": {"type": "string"}, "jpmark": {"type": "string"}, "level": {"type": "string"}, "messages": {"type": "string"}, "mitem_template": {"type": "string"}, "module": {"type": "string"}, "order_id": {"type": "string"}, "page_description_b2blist": {"type": "string"}, "page_description_qg": {"type": "string"}, "page_description_special": {"type": "string"}, "page_keyword_b2blist": {"type": "string"}, "page_keyword_qg": {"type": "string"}, "page_keyword_special": {"type": "string"}, "page_title": {"type": "string"}, "page_title_b2blist": {"type": "string"}, "page_title_qg": {"type": "string"}, "page_title_special": {"type": "string"}, "parent_id": {"type": "string"}, "pr": {"type": "string"}, "replacekeyword": {"type": "object"}, "replaceproduct": {"type": "object"}, "searchword": {"type": "object"}, "seo_description": {"type": "string"}, "seo_keyword": {"type": "string"}, "seo_title": {"type": "string"}, "shorttitle": {"type": "string"}, "subdomain": {"type": "string"}, "templete": {"type": "string"}, "type": {"type": "string"}, "validate": {"type": "string"}, "video": {"type": "string"}}}, "publisher.Property": {"type": "object", "properties": {"displayname": {"type": "string"}, "fieldname": {"type": "string"}, "fieldoptions": {"type": "array", "items": {"type": "object"}}, "fieldtype": {"type": "string"}}}, "publisher.RanksData": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "RankItem"}}, "pageinfos": {"type": "Pageinfos"}}}, "publisher.SeekCountData": {"type": "object", "properties": {"fromse": {"type": "Fromse"}, "items": {"type": "integer"}}}, "schemas.AdminProduct": {"type": "object", "properties": {"audit_res": {"type": "string"}, "audit_res2": {"type": "object", "additionalProperties": true}, "company": {"type": "object", "$ref": "#/definitions/schemas.Company"}, "company_id": {"type": "integer"}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "modify_fields": {"type": "array", "items": {"type": "string"}}, "name": {"type": "string"}, "platforms": {"type": "string"}, "status": {"type": "integer"}, "updated_at": {"type": "string"}}}, "schemas.Album": {"type": "object", "properties": {"description": {"type": "string"}, "is_open": {"type": "string"}, "name": {"type": "string"}}}, "schemas.AlbumResponse": {"type": "object", "properties": {"albumid": {"type": "string"}}}, "schemas.Area": {"type": "object", "properties": {"area_en": {"type": "string"}, "area_name": {"type": "string"}, "id": {"type": "integer"}, "parent_id": {"type": "integer"}}}, "schemas.AuditNotPass": {"type": "object", "properties": {"audit_res": {"type": "string"}, "audit_res2": {"type": "object", "additionalProperties": true}}}, "schemas.Cat": {"type": "object", "properties": {"cat_en": {"type": "string"}, "cat_name": {"type": "string"}, "cat_trace": {"type": "string"}, "cat_trace_en": {"type": "string"}, "id": {"type": "integer"}, "parent_id": {"type": "integer"}}}, "schemas.CatField": {"type": "object", "properties": {"displayname": {"type": "string"}, "fieldname": {"type": "string"}, "fieldoptions": {"type": "array", "items": {"type": "object"}}, "fieldtype": {"type": "string"}}}, "schemas.CheckModifyCompany": {"type": "object", "required": ["auditing_fields", "id"], "properties": {"auditing_fields": {"type": "object", "additionalProperties": true}, "id": {"type": "integer"}}}, "schemas.Company": {"type": "object", "required": ["contact_name", "phone"], "properties": {"advisorID": {"description": "管理员id", "type": "integer"}, "audit_res": {"description": "拒绝原因", "type": "string"}, "auditing_fields": {"description": "拒绝字段", "type": "string"}, "cate": {"type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "contact_name": {"description": "公司联系人, 必填字段", "type": "string"}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "name": {"description": "企业名称", "type": "string"}, "phone": {"description": "联系电话, 必填字段 [手机,公司座机，座机]", "type": "object", "$ref": "#/definitions/dbtypes.JSONArray"}, "updated_at": {"type": "string"}}}, "schemas.DelImages": {"type": "object", "properties": {"from": {"description": "来自哪个相册", "type": "integer"}, "ids": {"type": "array", "items": {"type": "integer"}}}}, "schemas.EditMerchant": {"type": "object", "required": ["pic"], "properties": {"pic": {"type": "string"}}}, "schemas.InfoGroupForClient": {"type": "object", "properties": {"count": {"type": "string"}, "enabled": {"type": "boolean"}, "name": {"type": "string"}, "platform": {"type": "integer"}}}, "schemas.InfoStat": {"type": "object", "additionalProperties": {"$ref": "#/definitions/schemas.InfoGroupForClient"}}, "schemas.Login": {"type": "object", "required": ["password"], "properties": {"grant_type": {"type": "string", "default": "user", "enum": ["user", "system"]}, "mobile": {"description": "黄页88登录必须", "type": "string"}, "password": {"type": "string"}, "username": {"description": "系统登录使用", "type": "string"}}}, "schemas.LoginResult": {"type": "object", "properties": {"access_token": {"type": "string"}, "exp": {"type": "integer"}, "id": {"type": "integer"}, "oem_id": {"type": "integer"}, "role": {"type": "string"}, "scopes": {"type": "array", "items": {"type": "string"}}}}, "schemas.MerchantStep": {"type": "object", "properties": {"name": {"type": "string"}, "steps": {"type": "array", "items": {"$ref": "#/definitions/schemas.Step"}}}}, "schemas.ModifyPassword": {"type": "object", "required": ["new", "old"], "properties": {"new": {"type": "string"}, "old": {"type": "string"}}}, "schemas.ModifySystemUser": {"type": "object", "properties": {"ban": {"type": "boolean"}, "email": {"type": "string"}, "name": {"type": "string"}, "phone": {"type": "string"}, "role": {"type": "string"}, "scopes": {"type": "array", "items": {"type": "string"}}}}, "schemas.MoveImages": {"type": "object", "properties": {"from": {"description": "来自哪个相册", "type": "integer"}, "ids": {"type": "array", "items": {"type": "integer"}}}}, "schemas.Product": {"type": "object", "properties": {"audit_res2": {"type": "object", "additionalProperties": true}, "brand": {"description": "必填项", "type": "string"}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "platforms": {"type": "string"}, "status": {"type": "integer"}, "updated_at": {"type": "string"}}}, "schemas.RanksData": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "RankItem"}}, "info_totals": {"type": "integer"}, "pageinfos": {"type": "Pageinfos"}, "product_totals": {"type": "integer"}}}, "schemas.Similar": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "string"}}, "index": {"type": "integer"}}}, "schemas.StatusCount": {"type": "object", "additionalProperties": {"type": "integer"}}, "schemas.Step": {"type": "object", "properties": {"action": {"description": "编辑去哪里 需要进一步处理 0-不需要处理 1-上传营业执照 2-上传企业声明 3-编辑爱采购", "type": "integer"}, "reason": {"type": "string"}, "status": {"description": "状态 1 完成。0 待完成。-1 未到达", "type": "integer"}, "text": {"description": "描述", "type": "string"}}}, "schemas.UploadResponse": {"type": "object", "properties": {"etag": {"description": "md5值", "type": "string"}, "msg": {"type": "string"}, "size": {"description": "文件大小", "type": "integer"}, "url": {"type": "string"}}}, "service.Paginator": {"type": "object", "properties": {"has_next": {"description": "是否有下一页", "type": "boolean"}, "has_prev": {"description": "是否有上一页", "type": "boolean"}, "items_total": {"description": "总数", "type": "integer"}, "page": {"description": "当前第几页", "type": "integer"}, "page_size": {"description": "每页多少条", "type": "integer"}, "page_total": {"description": "多少页", "type": "integer"}}}, "services.CompanyProductPaginator": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/models.CompanyProduct"}}, "paginator": {"type": "object", "$ref": "#/definitions/service.Paginator"}}}, "services.InfoPaginator": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/models.Info"}}, "paginator": {"type": "object", "$ref": "#/definitions/service.Paginator"}}}, "services.UserPubStatsPaginator": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/models.UserPubStat"}}, "paginator": {"type": "object", "$ref": "#/definitions/service.Paginator"}}}}, "securityDefinitions": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}