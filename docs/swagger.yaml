definitions:
  configs.ApiError:
    properties:
      code:
        type: string
      data:
        type: object
      msg:
        type: string
    type: object
  configs.ApiOkWrapper:
    properties:
      code:
        type: integer
      data:
        type: object
      msg:
        type: string
    type: object
  dbtypes.JSONArray:
    items:
      type: string
    type: array
  dbtypes.ScopeItem:
    properties:
      actions:
        items:
          type: string
        type: array
      description:
        type: string
      entity:
        type: string
      scope:
        type: string
    type: object
  dbtypes.Scopes:
    properties:
      actions:
        additionalProperties:
          type: string
        type: object
      scopes:
        items:
          $ref: '#/definitions/dbtypes.ScopeItem'
        type: array
    type: object
  models.AccountItem:
    properties:
      desc:
        type: string
      type:
        type: string
    type: object
  models.AicaigouUsers:
    properties:
      LinkPhone:
        description: 注册手机号
        type: string
      bank_areaids:
        $ref: '#/definitions/dbtypes.JSONArray'
        description: 银行地址 必填字段 ["省id","市id","县id"], 至少选到第二级
        type: object
      brank_name:
        description: 银行名称
        type: string
      business_img:
        description: 营业执照url路径
        type: string
      card_number:
        description: 银行卡号
        type: string
      company_areaids:
        $ref: '#/definitions/dbtypes.JSONArray'
        description: 公司地址 必填字段 ["省id","市id","县id"], 至少选到第二级
        type: object
      company_id:
        description: 关联登录用户
        type: integer
      company_logo:
        description: 公司logo 170x170 jpg
        type: string
      company_name:
        description: 与执照上公司名一致
        type: string
      company_type:
        description: 企业或个体工商户
        type: string
      company_web:
        description: 公司网址
        type: string
      contract_begin_date:
        description: 合同开始日期 yyyy-MM-dd格式
        type: string
      contract_end_date:
        description: 合同结束日期 yyyy-MM-dd格式
        type: string
      contract_file:
        description: 合同链接URL 8M以内，pdf格式
        type: string
      inter_bank_num:
        description: 银联号
        type: string
      link_email:
        description: 邮箱地址
        type: string
      link_person:
        description: 联系人
        type: string
      open_branch:
        description: 开户支行
        type: string
      product_type:
        description: 产品套餐类型(Int类型) 默认1
        type: integer
      social_credit_code:
        description: 社会统一信用代码
        type: string
    required:
    - LinkPhone
    - brank_name
    - business_img
    - company_logo
    - company_name
    - company_type
    - company_web
    - contract_begin_date
    - contract_end_date
    - contract_file
    - link_email
    - link_person
    - open_branch
    - social_credit_code
    type: object
  models.Album:
    properties:
      company_id:
        description: 公司id, 因为其他表和接口都是用的companyId, 统一使用这个。
        type: integer
      cover:
        description: 封面
        type: string
      created_at:
        type: string
      description:
        description: 相册描述
        type: string
      display_sizes:
        description: 用户可见总大小
        type: integer
      display_total:
        description: 用户可见文件数
        type: integer
      id:
        description: 主键自增id
        type: integer
      is_open:
        description: 是否公开，暂未启用。
        type: integer
      main:
        description: 主相册，也是默认相册。名字不能修改
        type: boolean
      name:
        description: 相册名字
        type: string
      sizes:
        description: 文件大小综合。单位字节
        type: integer
      status:
        description: 相册状态, 暂未启用
        type: integer
      total:
        description: 文件数
        type: integer
      updated_at:
        type: string
    type: object
  models.Cat:
    properties:
      active:
        type: integer
      afsChannel:
        type: string
      afsKey:
        type: string
      baike:
        type: integer
      bk:
        type: string
      bkurl:
        type: string
      buymessages:
        type: integer
      catEn:
        type: string
      catName:
        type: string
      catTrace:
        type: string
      catTraceEn:
        type: string
      ddescription:
        type: string
      description:
        type: string
      descriptionQg:
        type: string
      descriptionSpecial:
        type: string
      dkeyword:
        type: string
      dname:
        type: string
      dtitle:
        type: string
      friend:
        type: string
      friendName:
        type: string
      hotTitle:
        type: string
      id:
        type: integer
      isHot:
        type: boolean
      itemTemplate:
        type: string
      jpmark:
        type: integer
      level:
        type: integer
      messages:
        type: integer
      mitemTemplate:
        type: string
      module:
        type: string
      orderID:
        type: integer
      pageDescriptionB2blist:
        type: string
      pageDescriptionQg:
        type: string
      pageDescriptionSpecial:
        type: string
      pageKeywordB2blist:
        type: string
      pageKeywordQg:
        type: string
      pageKeywordSpecial:
        type: string
      pageTitle:
        type: string
      pageTitleB2blist:
        type: string
      pageTitleQg:
        type: string
      pageTitleSpecial:
        type: string
      parentID:
        type: integer
      pr:
        type: integer
      replacekeyword:
        type: string
      replaceproduct:
        type: string
      searchword:
        type: string
      seoDescription:
        type: string
      seoKeyword:
        type: string
      seoTitle:
        type: string
      shorttitle:
        type: string
      subdomain:
        type: integer
      templete:
        type: integer
      type:
        type: integer
      validate:
        type: boolean
      video:
        type: string
    type: object
  models.Company:
    properties:
      address:
        description: 公司详细地址，展示用, 51sole 为必填字段
        type: string
      advisor:
        $ref: '#/definitions/models.SystemUser'
        description: 管理员信息
        type: object
      advisor_id:
        description: 管理员id, 后台用
        type: integer
      area_names:
        $ref: '#/definitions/dbtypes.JSONArray'
        description: 和 areaids 对应的名字
        type: object
      areaids:
        $ref: '#/definitions/dbtypes.JSONArray'
        description: 公司地址 必填字段 ["省id","市id","县id"], 至少选到第二级
        type: object
      audit_res:
        description: 拒绝原因, 未启用
        type: string
      auditing_fields:
        $ref: '#/definitions/dbtypes.JSONArray'
        description: 修改的字段, 未启用
        type: object
      business:
        description: 营业执照信息:经营范围,business,string,0,1000 必填,数据库管理及技术开发；计算机系统分析；计算机技术咨询；计算机软、硬件的技术开发、系统集成及销售；电子商务的技术开发；经营电子商务；计算机网络技术的研发。(法律、行政法规、国务院决定规定在经营前须经批准的项目除外）
        type: string
      can_edit:
        description: 是否能编辑
        type: boolean
      cate:
        $ref: '#/definitions/dbtypes.JSONArray'
        description: 公司行业, 必填字段, 需要选到最后一级（第三级或第四级）
        type: object
      cate_names:
        $ref: '#/definitions/dbtypes.JSONArray'
        description: 分类名字，和 cate 一一对应
        type: object
      company_type:
        description: 公司类型(51sole必填)， 枚举类型：私营企业，国有企业，集体所有制企业，合资企业，外资企业，股份企业，个体经营，事业单位，社会团体，个人，其他
        type: string
      contact_name:
        description: 公司联系人, 必填字段
        type: string
      corp_type:
        description: 营业执照信息:公司类型,corp_type,string,0,50 必填,有限责任公司
        type: string
      created_at:
        type: string
      email:
        description: 公司邮箱（51sole必填)
        type: string
      gender:
        description: 性别
        type: integer
      id:
        type: integer
      introduce:
        description: 公司介绍, 必填字段
        type: string
      legal:
        description: 营业执照信息:公司法人(法定代表人),legal,string,0,20 必填,舒开勇
        type: string
      license:
        description: 营业执照, 必填字段
        type: string
      logo:
        description: 公司Logo, 选填
        type: string
      main_brand:
        description: 主要品牌(51sole必填)
        type: string
      main_product:
        description: 主营产品, 必填项
        type: string
      name:
        description: 公司名称, 必填字段, 不超过50字
        type: string
      op_end_time:
        description: 运营到期时间，默认为空
        type: string
      phone:
        $ref: '#/definitions/dbtypes.JSONArray'
        description: 联系电话, 必填字段 [手机,公司座机，座机]
        type: object
      qq:
        description: QQ, 51sole是必填字段
        type: string
      qrcode:
        description: 公司二维码
        type: string
      reason:
        description: 不能编辑的原因
        type: string
      reg_addr:
        description: 营业执照信息:注册地址(住所),reg_addr,string,0,100 必填,广东深圳
        type: string
      reg_authority:
        description: 营业执照信息:注册机构,reg_authority,string,0,100 必填,深圳市市场监督管理局宝安局
        type: string
      reg_date:
        description: 营业执照信息:注册时间(成立时间),reg_date,string,0,15 必填,2014-12-19
        type: string
      reg_money:
        description: 营业执照信息:注册资金,reg_money,string,0,50 必填,10万人民币
        type: string
      reg_no:
        description: 营业执照信息:统一社会信用代码 ,reg_no,string,0,50 必填,440306111877015
        type: string
      short_name:
        description: 公司简称，51搜了需要
        type: string
      site:
        description: 黄页88网站,其他的平台在商户表里
        type: string
      updated_at:
        type: string
      valid_period:
        description: 营业执照信息:有效时间(营业期限),valid_period,string,0,50 必填,2014-12-19 - 永续经营
        type: string
      working_model:
        description: 经营模式(51sole必填),枚举类型：生产型，贸易型，服务型，政府，其他机构
        type: string
    type: object
  models.CompanyProduct:
    properties:
      company:
        $ref: '#/definitions/models.Company'
        type: object
      company_id:
        type: integer
      content:
        type: string
      created_at:
        type: string
      failed_reason:
        type: string
      id:
        type: integer
      pic:
        $ref: '#/definitions/dbtypes.JSONArray'
        type: object
      price:
        type: number
      product:
        $ref: '#/definitions/models.Product'
        type: object
      product_id:
        description: 为空时用来判断是否自动发布的。必须用指针
        type: integer
      pub_res:
        type: string
      status:
        type: integer
      subject:
        type: string
      unit:
        type: string
      updated_at:
        type: string
      will_pub_at:
        type: string
    type: object
  models.Image:
    properties:
      album_id:
        type: integer
      company_id:
        description: 公司id
        type: integer
      created_at:
        type: string
      hash:
        description: 文件hash, 用于滤重的
        type: string
      id:
        description: 主键自增id
        type: integer
      name:
        description: 文件名字, 支持修改
        type: string
      size:
        description: 文件大小。单位字节
        type: integer
      status:
        description: 状态。 0 为可见状态。1 为不可见状态。删除图片就是标记为不可见状态
        type: integer
      tag:
        description: tag 自定义标签, 支持修改，暂时没有启用
        type: string
      updated_at:
        type: string
      url:
        description: 文件url
        type: string
      using:
        description: 0 未使用 1 已使用。 保留用。状态为0的可以真实删除。
        type: integer
    type: object
  models.Info:
    properties:
      audit_res:
        type: string
      company:
        $ref: '#/definitions/models.Company'
        type: object
      company_id:
        type: integer
      created_at:
        type: string
      description:
        type: string
      failed_reason:
        type: string
      id:
        type: integer
      pic:
        $ref: '#/definitions/dbtypes.JSONArray'
        type: object
      plat_form_name:
        description: 平台名称
        type: string
      platform:
        description: 平台
        type: integer
      price:
        type: number
      product:
        $ref: '#/definitions/models.Product'
        type: object
      product_id:
        description: 为空时用来判断是否自动发布的。必须用指针
        type: integer
      pub_res:
        type: string
      status:
        type: integer
      title:
        type: string
      titlepic:
        $ref: '#/definitions/dbtypes.JSONArray'
        description: 新增标题图片HY-5170
        type: object
      unit:
        type: string
      updated_at:
        type: string
      will_pub_at:
        type: string
      word:
        $ref: '#/definitions/dbtypes.JSONArray'
        type: object
    type: object
  models.Infos:
    properties:
      company_id:
        description: 公司id
        type: integer
      created_at:
        type: string
      id:
        type: integer
      keywords:
        $ref: '#/definitions/dbtypes.JSONArray'
        description: 关键词
        type: object
      mid:
        description: 第三方平台信息id
        type: integer
      plat_form_name:
        description: 平台名称
        type: string
      platform:
        description: 哪个平台的信息
        type: integer
      title:
        description: 标题
        type: string
      updated_at:
        type: string
      url:
        description: 第三方平台信息url
        type: string
    type: object
  models.Merchant:
    properties:
      account:
        type: string
      action:
        description: 需要进一步处理 0-不需要处理 1-上传营业执照 2-上传企业声明 3-编辑爱采购
        type: integer
      auto_pub:
        type: boolean
      auto_pub_time:
        type: string
      base_cnt:
        description: 基础信息条数
        type: integer
      company:
        $ref: '#/definitions/models.Company'
        type: object
      company_id:
        description: 这里是指發發助手的公司ID
        type: integer
      company_site:
        description: 第三方公司的商铺网址
        type: string
      contact:
        type: string
      contact_phone:
        type: string
      created_at:
        type: string
      daily_pub_products:
        type: integer
      deletedAt:
        type: string
      enable_post_product:
        description: 控制产品自动发布
        type: boolean
      ext:
        type: string
      id:
        type: integer
      name:
        type: string
      pause:
        type: boolean
      pause_reason:
        type: string
      plat_form:
        type: integer
      plat_form_name:
        description: 平台名称
        type: string
      product_auto_pub_time:
        type: string
      product_pause_reason:
        type: string
      pub_count:
        description: 控制信息自动发布
        type: integer
      pub_per_count:
        description: 单个产品每日发布量
        type: integer
      published_cnt:
        description: 已发布数量
        type: integer
      re_mark:
        description: 备注
        type: string
      reason:
        description: 原因。
        type: string
      renew_add_cnt:
        description: 续费一次增加的条数
        type: integer
      renew_count:
        type: integer
      status:
        description: 当前状态。文本描述。内容为空为正常
        type: string
      target_company_id:
        description: 第三方公司ID
        type: integer
      total:
        description: 发布总量。平台配置。特殊情况：-2 标示黄页88没开通。
        type: integer
      updated_at:
        type: string
    type: object
  models.MerchantMeta:
    properties:
      description:
        type: string
      home_url:
        type: string
      logo_url:
        type: string
      name:
        type: string
      plat_form:
        type: integer
      platFormName:
        description: 平台名称
        type: string
      required_account:
        additionalProperties:
          $ref: '#/definitions/models.AccountItem'
        type: object
    type: object
  models.PostName:
    properties:
      id:
        type: integer
      name:
        type: string
    type: object
  models.Product:
    properties:
      active:
        description: 是否活跃版本。已有改成active=1为活跃版本。
        type: integer
      alias:
        $ref: '#/definitions/dbtypes.JSONArray'
        type: object
      area_names:
        $ref: '#/definitions/dbtypes.JSONArray'
        description: 和 areaids 对应的名字
        type: object
      areaids:
        $ref: '#/definitions/dbtypes.JSONArray'
        description: '["省id","市id","县id"], 都填0，表示全国'
        type: object
      audit_res:
        type: string
      audit_res2:
        type: string
      bad_words:
        description: 违禁词
        type: string
      brand:
        description: 必填项
        type: string
      cate:
        $ref: '#/definitions/dbtypes.JSONArray'
        type: object
      cate_names:
        $ref: '#/definitions/dbtypes.JSONArray'
        description: 分类名字，和 cate 一一对应
        type: object
      company:
        $ref: '#/definitions/models.Company'
        type: object
      company_id:
        type: integer
      created_at:
        type: string
      currentId:
        description: CurrentId 当前使用的版本号
        type: integer
      faq:
        type: string
      id:
        type: integer
      inventory:
        type: integer
      material:
        $ref: '#/definitions/dbtypes.JSONArray'
        type: object
      min_order:
        type: integer
      modify_fields:
        $ref: '#/definitions/dbtypes.JSONArray'
        type: object
      name:
        type: string
      pic:
        $ref: '#/definitions/dbtypes.JSONArray'
        type: object
      platforms:
        description: '{''1'':{name:''黄页88'',on:true}, ''2'':{name:''八方资源'',on:false}}'
        type: string
      price:
        type: number
      properties:
        type: string
      status:
        type: integer
      title:
        $ref: '#/definitions/dbtypes.JSONArray'
        type: object
      titlepic:
        $ref: '#/definitions/dbtypes.JSONArray'
        description: 新增标题图片HY-5170
        type: object
      unit:
        type: string
      updated_at:
        type: string
      use_alias:
        type: boolean
      used_title:
        $ref: '#/definitions/dbtypes.JSONArray'
        type: object
      word:
        $ref: '#/definitions/dbtypes.JSONArray'
        type: object
    type: object
  models.Rank:
    properties:
      company_id:
        description: 哪个用户的
        type: integer
      created_at:
        type: string
      eg:
        description: 搜索引擎
        type: string
      id:
        type: integer
      keyword:
        description: 关键词
        type: string
      plat_form_name:
        description: 平台名称
        type: string
      platform:
        description: 哪个平台的信息
        type: integer
      rank:
        description: 排名 页码+排名
        type: integer
      snap:
        description: 快照
        type: string
      updated_at:
        type: string
      url:
        description: 链接url
        type: string
    type: object
  models.SystemUser:
    properties:
      ban:
        type: boolean
      created_at:
        type: string
      email:
        type: string
      id:
        type: integer
      last_login_at:
        type: string
      name:
        type: string
      password:
        type: string
      phone:
        type: string
      role:
        type: string
      scopes:
        $ref: '#/definitions/dbtypes.JSONArray'
        type: object
      updated_at:
        type: string
      username:
        type: string
    type: object
  models.User:
    properties:
      ban:
        type: boolean
      company:
        $ref: '#/definitions/models.Company'
        type: object
      company_id:
        type: integer
      created_at:
        type: string
      dialy_items:
        type: integer
      email:
        type: string
      expire_time:
        type: string
      id:
        type: integer
      last_login_at:
        type: string
      max_products:
        type: integer
      oem_class:
        type: string
      oem_id:
        type: integer
      phone:
        type: string
      post_buy_time:
        type: string
      post_expire_time:
        type: string
      post_names:
        items:
          $ref: '#/definitions/models.PostName'
        type: array
      rank_buy_time:
        type: string
      rank_expire_time:
        type: string
      seek_buy_time:
        type: string
      seek_expire_time:
        type: string
      total_items:
        type: integer
      updated_at:
        type: string
      username:
        type: string
    type: object
  models.UserPlatform:
    properties:
      company_id:
        type: integer
      created_at:
        type: string
      expire_time:
        type: string
      funcname:
        type: string
      plat_form_name:
        description: 平台名称
        type: string
      platform:
        type: integer
      status:
        type: integer
      updated_at:
        type: string
    type: object
  models.UserPubStat:
    properties:
      advisor_id:
        description: 顾问Id
        type: integer
      advisor_name:
        description: 顾问名字
        type: string
      company_id:
        description: 公司id
        type: integer
      company_name:
        description: 公司名，方便搜索
        type: string
      created_at:
        type: string
      info_failed_cnt_yesterday:
        description: 昨日推送失败的信息数
        type: integer
      info_succeed_cnt:
        description: 推送成功的信息数
        type: integer
      info_succeed_cnt_yesterday:
        description: 昨日推送成功的信息数
        type: integer
      op_end_time:
        description: 运营到期时间
        type: string
      product_cnt:
        description: 添加的产品数
        type: integer
      product_promotion_cnt:
        description: 推广中的产品数
        type: integer
      ranked_cnt:
        description: 排名总数
        type: integer
      updated_at:
        type: string
      user_name:
        description: 用户名，方便搜索
        type: string
    type: object
  models.UserPubStatDetails:
    properties:
      company_id:
        description: 公司id
        type: integer
      created_at:
        type: string
      info_stats:
        description: 信息统计
        type: string
      info_stats_yesterday:
        description: 昨日信息统计
        type: string
      product_stats:
        description: 产品统计
        type: string
      ranked_stats:
        description: 排名统计
        type: string
      updated_at:
        type: string
    type: object
  models.UserStat:
    properties:
      album_cnt:
        description: 相册数
        type: integer
      buy_photos:
        description: 购买的图片数
        type: integer
      can_uploaded:
        description: 可以上传的图片数。。可计算得出， 2000+buy_photos-uploaded_cnt
        type: integer
      company_id:
        description: 公司id
        type: integer
      created_at:
        type: string
      dig_keyword_limit:
        description: 挖掘关键词 可用次数
        type: integer
      dig_keyword_used:
        description: 挖掘关键词 已用次数
        type: integer
      dig_materials_limit:
        description: 挖掘物料 可用次数
        type: integer
      dig_materials_used:
        description: 挖掘物料 已用次数
        type: integer
      photo_cnt:
        description: 图片总数, 可查看的图片数，不包括删除的
        type: integer
      updated_at:
        type: string
      uploaded_cnt:
        description: 上传的图片数，包括被删除的 uploaded_cnt >= photo_cnt
        type: integer
    type: object
  publisher.Area:
    properties:
      area_en:
        type: string
      area_name:
        type: string
      area_trace:
        type: string
      area_trace_en:
        type: string
      id:
        type: string
    type: object
  publisher.Category:
    properties:
      cat_en:
        type: string
      cat_name:
        type: string
      cat_trace:
        type: string
      cat_trace_en:
        type: string
      id:
        type: string
    type: object
  publisher.CategoryDetail:
    properties:
      active:
        type: string
      afs_channel:
        type: string
      afs_key:
        type: string
      baike:
        type: string
      bk:
        type: string
      bkurl:
        type: string
      buymessages:
        type: string
      cat_en:
        type: string
      cat_name:
        type: string
      cat_trace:
        type: string
      cat_trace_en:
        type: string
      ddescription:
        type: string
      description:
        type: string
      description_qg:
        type: string
      description_special:
        type: string
      dkeyword:
        type: string
      dname:
        type: string
      dtitle:
        type: string
      friend:
        type: string
      friend_name:
        type: string
      hot_title:
        type: string
      id:
        type: string
      is_hot:
        type: string
      item_template:
        type: string
      jpmark:
        type: string
      level:
        type: string
      messages:
        type: string
      mitem_template:
        type: string
      module:
        type: string
      order_id:
        type: string
      page_description_b2blist:
        type: string
      page_description_qg:
        type: string
      page_description_special:
        type: string
      page_keyword_b2blist:
        type: string
      page_keyword_qg:
        type: string
      page_keyword_special:
        type: string
      page_title:
        type: string
      page_title_b2blist:
        type: string
      page_title_qg:
        type: string
      page_title_special:
        type: string
      parent_id:
        type: string
      pr:
        type: string
      replacekeyword:
        type: object
      replaceproduct:
        type: object
      searchword:
        type: object
      seo_description:
        type: string
      seo_keyword:
        type: string
      seo_title:
        type: string
      shorttitle:
        type: string
      subdomain:
        type: string
      templete:
        type: string
      type:
        type: string
      validate:
        type: string
      video:
        type: string
    type: object
  publisher.Property:
    properties:
      displayname:
        type: string
      fieldname:
        type: string
      fieldoptions:
        items:
          type: object
        type: array
      fieldtype:
        type: string
    type: object
  publisher.RanksData:
    properties:
      data:
        items:
          type: RankItem
        type: array
      pageinfos:
        type: Pageinfos
    type: object
  publisher.SeekCountData:
    properties:
      fromse:
        type: Fromse
      items:
        type: integer
    type: object
  schemas.AdminProduct:
    properties:
      audit_res:
        type: string
      audit_res2:
        additionalProperties: true
        type: object
      company:
        $ref: '#/definitions/schemas.Company'
        type: object
      company_id:
        type: integer
      created_at:
        type: string
      id:
        type: integer
      modify_fields:
        items:
          type: string
        type: array
      name:
        type: string
      platforms:
        type: string
      status:
        type: integer
      updated_at:
        type: string
    type: object
  schemas.Album:
    properties:
      description:
        type: string
      is_open:
        type: string
      name:
        type: string
    type: object
  schemas.AlbumResponse:
    properties:
      albumid:
        type: string
    type: object
  schemas.Area:
    properties:
      area_en:
        type: string
      area_name:
        type: string
      id:
        type: integer
      parent_id:
        type: integer
    type: object
  schemas.AuditNotPass:
    properties:
      audit_res:
        type: string
      audit_res2:
        additionalProperties: true
        type: object
    type: object
  schemas.Cat:
    properties:
      cat_en:
        type: string
      cat_name:
        type: string
      cat_trace:
        type: string
      cat_trace_en:
        type: string
      id:
        type: integer
      parent_id:
        type: integer
    type: object
  schemas.CatField:
    properties:
      displayname:
        type: string
      fieldname:
        type: string
      fieldoptions:
        items:
          type: object
        type: array
      fieldtype:
        type: string
    type: object
  schemas.CheckModifyCompany:
    properties:
      auditing_fields:
        additionalProperties: true
        type: object
      id:
        type: integer
    required:
    - auditing_fields
    - id
    type: object
  schemas.Company:
    properties:
      advisorID:
        description: 管理员id
        type: integer
      audit_res:
        description: 拒绝原因
        type: string
      auditing_fields:
        description: 拒绝字段
        type: string
      cate:
        $ref: '#/definitions/dbtypes.JSONArray'
        type: object
      contact_name:
        description: 公司联系人, 必填字段
        type: string
      created_at:
        type: string
      id:
        type: integer
      name:
        description: 企业名称
        type: string
      phone:
        $ref: '#/definitions/dbtypes.JSONArray'
        description: 联系电话, 必填字段 [手机,公司座机，座机]
        type: object
      updated_at:
        type: string
    required:
    - contact_name
    - phone
    type: object
  schemas.DelImages:
    properties:
      from:
        description: 来自哪个相册
        type: integer
      ids:
        items:
          type: integer
        type: array
    type: object
  schemas.EditMerchant:
    properties:
      pic:
        type: string
    required:
    - pic
    type: object
  schemas.InfoGroupForClient:
    properties:
      count:
        type: string
      enabled:
        type: boolean
      name:
        type: string
      platform:
        type: integer
    type: object
  schemas.InfoStat:
    additionalProperties:
      $ref: '#/definitions/schemas.InfoGroupForClient'
    type: object
  schemas.Login:
    properties:
      grant_type:
        default: user
        enum:
        - user
        - system
        type: string
      mobile:
        description: 黄页88登录必须
        type: string
      password:
        type: string
      username:
        description: 系统登录使用
        type: string
    required:
    - password
    type: object
  schemas.LoginResult:
    properties:
      access_token:
        type: string
      exp:
        type: integer
      id:
        type: integer
      oem_id:
        type: integer
      role:
        type: string
      scopes:
        items:
          type: string
        type: array
    type: object
  schemas.MerchantStep:
    properties:
      name:
        type: string
      steps:
        items:
          $ref: '#/definitions/schemas.Step'
        type: array
    type: object
  schemas.ModifyPassword:
    properties:
      new:
        type: string
      old:
        type: string
    required:
    - new
    - old
    type: object
  schemas.ModifySystemUser:
    properties:
      ban:
        type: boolean
      email:
        type: string
      name:
        type: string
      phone:
        type: string
      role:
        type: string
      scopes:
        items:
          type: string
        type: array
    type: object
  schemas.MoveImages:
    properties:
      from:
        description: 来自哪个相册
        type: integer
      ids:
        items:
          type: integer
        type: array
    type: object
  schemas.Product:
    properties:
      audit_res2:
        additionalProperties: true
        type: object
      brand:
        description: 必填项
        type: string
      created_at:
        type: string
      id:
        type: integer
      name:
        type: string
      platforms:
        type: string
      status:
        type: integer
      updated_at:
        type: string
    type: object
  schemas.RanksData:
    properties:
      data:
        items:
          type: RankItem
        type: array
      info_totals:
        type: integer
      pageinfos:
        type: Pageinfos
      product_totals:
        type: integer
    type: object
  schemas.Similar:
    properties:
      data:
        items:
          type: string
        type: array
      index:
        type: integer
    type: object
  schemas.StatusCount:
    additionalProperties:
      type: integer
    type: object
  schemas.Step:
    properties:
      action:
        description: 编辑去哪里 需要进一步处理 0-不需要处理 1-上传营业执照 2-上传企业声明 3-编辑爱采购
        type: integer
      reason:
        type: string
      status:
        description: 状态 1 完成。0 待完成。-1 未到达
        type: integer
      text:
        description: 描述
        type: string
    type: object
  schemas.UploadResponse:
    properties:
      etag:
        description: md5值
        type: string
      msg:
        type: string
      size:
        description: 文件大小
        type: integer
      url:
        type: string
    type: object
  service.Paginator:
    properties:
      has_next:
        description: 是否有下一页
        type: boolean
      has_prev:
        description: 是否有上一页
        type: boolean
      items_total:
        description: 总数
        type: integer
      page:
        description: 当前第几页
        type: integer
      page_size:
        description: 每页多少条
        type: integer
      page_total:
        description: 多少页
        type: integer
    type: object
  services.CompanyProductPaginator:
    properties:
      items:
        items:
          $ref: '#/definitions/models.CompanyProduct'
        type: array
      paginator:
        $ref: '#/definitions/service.Paginator'
        type: object
    type: object
  services.InfoPaginator:
    properties:
      items:
        items:
          $ref: '#/definitions/models.Info'
        type: array
      paginator:
        $ref: '#/definitions/service.Paginator'
        type: object
    type: object
  services.UserPubStatsPaginator:
    properties:
      items:
        items:
          $ref: '#/definitions/models.UserPubStat'
        type: array
      paginator:
        $ref: '#/definitions/service.Paginator'
        type: object
    type: object
info:
  contact:
    email: <EMAIL>
    name: shunping.liu(微信:maynardliu)
    url: https://www.go2live.cn
  description: This is a sample server for all_publish
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: all_publish API DOC
  version: "1.0"
paths:
  /aicaigou:
    post:
      consumes:
      - application/json
      description: |-
        添加爱采购资料
        需要先完善公司资料，注册好搜了网
      parameters:
      - description: AicaigouUsers
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.AicaigouUsers'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.AicaigouUsers'
      security:
      - ApiKeyAuth: []
      summary: 添加爱采购资料
      tags:
      - client
    put:
      consumes:
      - application/json
      description: 修改那个字段，提交那个字段, 直接修改
      parameters:
      - description: Aicaigou
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.AicaigouUsers'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.AicaigouUsers'
      security:
      - ApiKeyAuth: []
      summary: 提交/修改爱采购用户信息
      tags:
      - client
  /aicaigou/me/:
    get:
      description: 获取爱采购用户详情, 只能获取当前用户的
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.AicaigouUsers'
      security:
      - ApiKeyAuth: []
      summary: 获取爱采购用户详情
      tags:
      - client
  /aicaigou/save:
    post:
      consumes:
      - application/json
      description: 已有就是编辑，没有就是保存
      parameters:
      - description: Aicaigou
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.AicaigouUsers'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.AicaigouUsers'
      security:
      - ApiKeyAuth: []
      summary: 保存爱采购用户信息
      tags:
      - client
  /areas/{pid}:
    get:
      description: |-
        传0获取省份
        不用接口，用js的方式:  http://api.huangye88.com/js/apiarea.js
      parameters:
      - description: int
        in: path
        name: pid
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/publisher.Area'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 根据父级id获取下一级地区
      tags:
      - deprecated
  /auth:
    post:
      consumes:
      - application/json
      description: 登陆认证, 黄页88登录使用mobile(grant_type=user), 系统用户使用username(grant_type=system)
      parameters:
      - description: login
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/schemas.Login'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/schemas.LoginResult'
      summary: 登陆认证接口
      tags:
      - client
      - sys
  /category/{id}:
    get:
      parameters:
      - description: int
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/publisher.CategoryDetail'
      security:
      - ApiKeyAuth: []
      summary: 获取分类详情
      tags:
      - deprecated
  /category/{id}/properties:
    get:
      description: input->(string),textarea->长文本,radio->单选按钮,checkbox->多选按钮,select->下拉框
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/publisher.Property'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取属性列表
      tags:
      - deprecated
  /category/{id}/subs:
    get:
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/publisher.Category'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取子分类
      tags:
      - deprecated
  /company:
    put:
      consumes:
      - application/json
      description: 修改那个字段，提交那个字段, 直接修改
      parameters:
      - description: Company
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.Company'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Company'
      security:
      - ApiKeyAuth: []
      summary: 提交/修改公司信息
      tags:
      - client
  /company/me/:
    get:
      description: 获取公司详情, 只能获取当前用户的
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Company'
      security:
      - ApiKeyAuth: []
      summary: 获取公司详情
      tags:
      - client
  /companyproduct/items:
    get:
      consumes:
      - application/json
      description: |-
        获取发布产品列表 带分页
        status 0:等待推送 1:推送成功 3:推送失败
      parameters:
      - description: string enums
        enum:
        - created_at
        - updated_at
        in: query
        name: sortby
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      - description: 搜索关键词，搜索标题
        in: query
        name: keyword
        type: string
      - description: 发布状态，int enums
        enum:
        - "0"
        - "1"
        - "3"
        in: query
        name: status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.CompanyProductPaginator'
      security:
      - ApiKeyAuth: []
      summary: 获取发布产品列表 带分页
      tags:
      - client
  /companyproduct/query:
    get:
      consumes:
      - application/json
      description: |-
        获取发布产品列表
        status -1:所有状态(默认值) 0:等待推送 1:推送成功 3:推送失败
      parameters:
      - description: string enums
        enum:
        - created_at
        - updated_at
        in: query
        name: sortby
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      - description: 搜索关键词，搜索标题
        in: query
        name: keyword
        type: string
      - description: 发布状态，int enums
        enum:
        - "-1"
        - "0"
        - "1"
        - "3"
        in: query
        name: status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.CompanyProduct'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取发布产品列表
      tags:
      - client
  /info:
    post:
      consumes:
      - application/json
      description: 手动发布信息
      parameters:
      - description: Info
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.Info'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Info'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 手动发布信息
      tags:
      - client
  /info/{id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: 信息id
        in: path
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Info'
      security:
      - ApiKeyAuth: []
      summary: 获取发布信息详情
      tags:
      - client
  /info/items:
    get:
      consumes:
      - application/json
      description: |-
        获取发布信息列表 带分页
        status 0:全部(默认) 1:等待推送 2:推送成功 4:推送失败
      parameters:
      - description: string enums
        enum:
        - created_at
        - updated_at
        in: query
        name: sortby
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      - description: 搜索关键词，搜索标题
        in: query
        name: keyword
        type: string
      - description: 发布状态，int enums
        enum:
        - "1"
        - "2"
        - "4"
        in: query
        name: status
        type: string
      - description: 发布类型，string enums
        enum:
        - auto
        - manual
        in: query
        name: pub_type
        type: string
      - description: 目标平台，int enums, 默认-1所有，0--黄页88，1--八方资源
        enum:
        - "-1"
        - "0"
        - "1"
        in: query
        name: platform
        type: string
      - description: '是否已同步到爱采购，int enums, 默认: 0--不查 1--已同步到爱采购'
        enum:
        - 0
        - 1
        in: query
        name: synced
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.InfoPaginator'
      security:
      - ApiKeyAuth: []
      summary: 获取发布信息列表 带分页
      tags:
      - client
  /info/query:
    get:
      consumes:
      - application/json
      description: |-
        获取发布信息列表
        status 0:所有状态 1:等待推送 2:推送成功 4:推送失败
      parameters:
      - description: string enums
        enum:
        - created_at
        - updated_at
        in: query
        name: sortby
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      - description: 搜索关键词，搜索标题
        in: query
        name: keyword
        type: string
      - description: 发布状态，int enums
        enum:
        - "1"
        - "2"
        - "4"
        in: query
        name: status
        type: string
      - description: 发布类型，string enums
        enum:
        - auto
        - manual
        in: query
        name: pub_type
        type: string
      - description: 目标平台，int enums, 默认-1所有，0--黄页88，1--八方资源
        enum:
        - "-1"
        - "0"
        - "1"
        in: query
        name: platform
        type: string
      - description: '是否已同步到爱采购，int enums, 默认: 0--不查 1--已同步到爱采购'
        enum:
        - 0
        - 1
        in: query
        name: synced
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Info'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取发布信息列表
      tags:
      - client
  /info/stat:
    get:
      description: 获取前一天信息统计信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/schemas.InfoStat'
      security:
      - ApiKeyAuth: []
      summary: 获取前一天信息统计信息
      tags:
      - client
  /merchant/{id}:
    put:
      consumes:
      - application/json
      description: 提交企业声明, pic
      parameters:
      - description: 商铺id
        in: path
        name: id
        required: true
        type: integer
      - description: EditMerchant
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/schemas.EditMerchant'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Company'
      security:
      - ApiKeyAuth: []
      summary: 提交企业声明
      tags:
      - client
  /merchant/enables:
    get:
      description: 获取当前用户已开通的商铺列表
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Merchant'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取当前用户已开通的商铺列表
      tags:
      - client
  /merchant/query:
    get:
      description: 获取当前用户开通的商户列表
      parameters:
      - description: string enums
        enum:
        - created_at
        - updated_at
        in: query
        name: sortby
        required: true
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Merchant'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取当前用户开通的商户列表
      tags:
      - client
  /merchant/status:
    get:
      description: 获取当前用户开通的商户列表状态
      parameters:
      - description: string enums
        enum:
        - created_at
        - updated_at
        in: query
        name: sortby
        required: true
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/schemas.MerchantStep'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取当前用户开通的商户列表状态
      tags:
      - client
  /openapi/rank:
    post:
      description: 添加排名，添加前，需要小调用status, 避免重复插入
      parameters:
      - description: string
        in: query
        name: hash
        required: true
        type: string
      - description: string
        in: query
        name: annouce
        required: true
        type: string
      - description: url
        in: query
        name: url
        required: true
        type: string
      - description: 关键词
        in: query
        name: keyword
        required: true
        type: string
      - description: 排名
        in: query
        name: rank
        required: true
        type: string
      - description: source:like BD
        in: query
        name: eg
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Infos'
      summary: 添加排名
      tags:
      - client
  /openapi/rank/status:
    get:
      description: |-
        获取关键词列表。。 关键词组成 'word source'
        source: baidu:百度电脑端; baidu_m:百度移动
      parameters:
      - description: string
        in: query
        name: hash
        required: true
        type: string
      - description: string
        in: query
        name: annouce
        required: true
        type: string
      - description: url
        in: query
        name: url
        required: true
        type: string
      - description: 关键词
        in: query
        name: keyword
        required: true
        type: string
      - description: 排名
        in: query
        name: rank
        required: true
        type: string
      - description: source:like BD
        in: query
        name: eg
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Infos'
      summary: 获取关键词列表
      tags:
      - client
  /openapi/tasks:
    get:
      description: |-
        获取关键词列表。。 关键词组成 'word source'
        source: baidu:百度电脑端; baidu_m:百度移动
      parameters:
      - description: string
        in: query
        name: hash
        required: true
        type: string
      - description: string
        in: query
        name: annouce
        required: true
        type: string
      - description: int
        in: query
        name: limit
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
      summary: 获取关键词列表
      tags:
      - client
  /product:
    post:
      consumes:
      - application/json
      description: 添加产品
      parameters:
      - description: Product
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.Product'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Product'
      security:
      - ApiKeyAuth: []
      summary: 添加产品
      tags:
      - client
  /product/{productId}:
    get:
      description: 获取产品详情, 只能获取当前用户的
      parameters:
      - description: productId
        in: path
        name: productId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 获取产品详情
      tags:
      - client
    put:
      consumes:
      - application/json
      description: 修改产品描述， 只上传需要修改的部分
      parameters:
      - description: productId
        in: path
        name: productId
        required: true
        type: integer
      - description: Product
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.Product'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Product'
      security:
      - ApiKeyAuth: []
      summary: 修改产品内容
      tags:
      - client
  /product/opt/{productId}:
    get:
      description: |-
        提交审核、推广、暂停推广等操作
        query string parmas:
        opt: 1 提交审核 2 开始推广 3 取消推广 4 删除
      parameters:
      - description: int enum Enum(1,2,3,4)
        in: query
        name: opt
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 操作产品
      tags:
      - client
  /product/query:
    get:
      description: |-
        获取产品列表
        0.2.57 开始字段精简
      parameters:
      - description: string enums
        enum:
        - created_at
        - updated_at
        in: query
        name: sortby
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/schemas.Product'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取产品列表
      tags:
      - client
  /product/stat:
    get:
      description: 获取产品统计信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/schemas.StatusCount'
      security:
      - ApiKeyAuth: []
      summary: 获取产品统计信息
      tags:
      - client
  /ranks:
    get:
      description: |-
        获取搜索排名列表
        query string parmas:
        eg: 0 全部 1 百度 2 360， 3 搜狗 4 神马 5头条
      parameters:
      - description: int enums
        enum:
        - 0
        - 1
        - 2
        - 3
        - 4
        - 5
        in: query
        name: eg
        required: true
        type: integer
      - description: int
        in: query
        name: page
        required: true
        type: integer
      - description: int
        in: query
        name: debug
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/schemas.RanksData'
      security:
      - ApiKeyAuth: []
      summary: 获取搜索排名列表
      tags:
      - client
  /seeks:
    get:
      description: |-
        获取收录排名列表
        query string parmas:
        eg: pc_baidu:百度电脑端; m_baidu:百度移动; pc_sogou:搜狗电脑端;m_sogou:搜狗移动;m_haosou:好搜移动;m_toutiao:头条移动
      parameters:
      - description: string enums
        enum:
        - pc_baidu
        - m_baidu
        - pc_sogou
        - m_sogou
        - m_haosou
        - m_toutiao
        in: query
        name: eg
        required: true
        type: string
      - description: int
        in: query
        name: page
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/publisher.RanksData'
      security:
      - ApiKeyAuth: []
      summary: 获取收录列表
      tags:
      - client
  /seeks/count:
    get:
      description: 获取当天用户的收录统计
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/publisher.SeekCountData'
      security:
      - ApiKeyAuth: []
      summary: 获取收录统计
      tags:
      - client
  /sys/{companyId}/aicaigou:
    get:
      description: 获取爱采购用户详情, 只能获取当前用户的
      parameters:
      - description: companyId
        in: path
        name: companyId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.AicaigouUsers'
      security:
      - ApiKeyAuth: []
      summary: 获取爱采购用户详情
      tags:
      - sys
    post:
      consumes:
      - application/json
      description: |-
        添加爱采购资料
        需要先完善公司资料，注册好搜了网
      parameters:
      - description: companyId
        in: path
        name: companyId
        required: true
        type: integer
      - description: AicaigouUsers
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.AicaigouUsers'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.AicaigouUsers'
      security:
      - ApiKeyAuth: []
      summary: 添加爱采购资料
      tags:
      - sys
    put:
      consumes:
      - application/json
      description: 修改那个字段，提交那个字段, 直接修改
      parameters:
      - description: companyId
        in: path
        name: companyId
        required: true
        type: integer
      - description: Aicaigou
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.AicaigouUsers'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.AicaigouUsers'
      security:
      - ApiKeyAuth: []
      summary: 提交/修改爱采购用户信息
      tags:
      - sys
  /sys/{companyId}/aicaigou/save:
    post:
      consumes:
      - application/json
      description: 已有就是编辑，没有就是保存
      parameters:
      - description: Aicaigou
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.AicaigouUsers'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.AicaigouUsers'
      security:
      - ApiKeyAuth: []
      summary: 保存爱采购用户信息
      tags:
      - sys
  /sys/{companyId}/merchant:
    post:
      consumes:
      - application/json
      description: |-
        需要权限：company:modify or add
        仅需传输需要修改的部分
      parameters:
      - description: companyId
        in: path
        name: companyId
        required: true
        type: integer
      - description: Merchant
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.Merchant'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Merchant'
      security:
      - ApiKeyAuth: []
      summary: 为某公司添加商户信息
      tags:
      - sys
  /sys/{companyId}/merchant/{merchantId}:
    put:
      consumes:
      - application/json
      description: |-
        需要权限：company:modify
        仅需传输需要修改的部分
      parameters:
      - description: companyId
        in: path
        name: companyId
        required: true
        type: integer
      - description: merchantId
        in: path
        name: merchantId
        required: true
        type: integer
      - description: Merchant
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.Merchant'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/schemas.Album'
      security:
      - ApiKeyAuth: []
      summary: 修改某公司的某个商户信息
      tags:
      - sys
  /sys/{companyId}/merchant/meta:
    get:
      consumes:
      - application/json
      description: <company_id> 可传任意值 需要权限：company:get 或者 company:list 、company:add
        、company:modify
      parameters:
      - description: companyId
        in: path
        name: companyId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.MerchantMeta'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取商户Meta信息
      tags:
      - sys
  /sys/{companyId}/merchant/query:
    get:
      consumes:
      - application/json
      description: 需要权限：company:get 或者 company:list
      parameters:
      - description: companyId
        in: path
        name: companyId
        required: true
        type: integer
      - description: string enums
        enum:
        - created_at
        - updated_at
        in: query
        name: sortby
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Merchant'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取某个公司的商户列表
      tags:
      - sys
  /sys/album/upload/{cid}:
    post:
      consumes:
      - multipart/form-data
      description: 支持JPG/JPEG/BMP/GIF/PNG两种格式
      parameters:
      - description: cid 公司id
        in: path
        name: cid
        required: true
        type: integer
      - description: jpg,jpeg,bmp, gif, png file
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/schemas.UploadResponse'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 上传文件
      tags:
      - sys
  /sys/areas/{pid}:
    get:
      description: |-
        传0获取省份
        不用接口，用js的方式:  http://api.huangye88.com/js/apiarea.js
      parameters:
      - description: int
        in: path
        name: pid
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/publisher.Area'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 根据父级id获取下一级地区
      tags:
      - deprecated
  /sys/company/{id}:
    get:
      description: 获取公司详情, 只能获取当前用户的
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Company'
      security:
      - ApiKeyAuth: []
      summary: 获取公司详情
      tags:
      - sys
    put:
      consumes:
      - application/json
      description: 修改那个字段，提交那个字段, 直接修改
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: Company
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.Company'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Company'
      security:
      - ApiKeyAuth: []
      summary: 提交/修改公司信息
      tags:
      - sys
  /sys/company/check_not_pass/{companyid}:
    put:
      consumes:
      - application/json
      description: 需要权限：company:modify
      parameters:
      - description: companyid
        in: path
        name: companyid
        required: true
        type: integer
      - description: AuditNotPass
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/schemas.AuditNotPass'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 审核拒绝公司修改
      tags:
      - sys
  /sys/company/check_pass:
    post:
      consumes:
      - application/json
      description: |-
        需要权限：company:modify
        仅需传输auditing_fields 和 id两个字段，系统将修改id对应的company信息依据auditing_fields
      parameters:
      - description: CheckModifyCompany
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/schemas.CheckModifyCompany'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Company'
      security:
      - ApiKeyAuth: []
      summary: 审核、修改企业信息
      tags:
      - sys
  /sys/company/query:
    get:
      consumes:
      - application/json
      description: 需要权限：company:get 或者 company:list
      parameters:
      - description: string enums
        enum:
        - created_at
        - updated_at
        in: query
        name: sortby
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      - description: 搜索关键词，搜索公司名
        in: query
        name: keyword
        type: string
      - default: true
        description: 是否只过滤等待审核的公司， string default
        in: query
        name: wait_audit
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Company'
            type: array
      security:
      - ApiKeyAuth: []
      summary: '[搜索]获取[待审核]公司列表'
      tags:
      - sys
  /sys/companyproduct/items:
    get:
      consumes:
      - application/json
      description: |-
        需要权限：companyproduct:list
        状态0-待发布 1-推送成功 3-推送失败
      parameters:
      - description: string enums
        enum:
        - created_at
        - updated_at
        in: query
        name: sortby
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      - description: 搜索关键词，搜索标题
        in: query
        name: keyword
        type: string
      - description: 公司id
        in: query
        name: company_id
        type: integer
      - description: 过滤状态， 默认全部
        enum:
        - 0
        - 1
        - 3
        in: query
        name: status
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.CompanyProductPaginator'
      security:
      - ApiKeyAuth: []
      summary: 获取发布信息列表 带分页
      tags:
      - sys
  /sys/companyproduct/query:
    get:
      consumes:
      - application/json
      description: |-
        需要权限：companyproduct:list
        状态0-待发布 1-推送成功 3-推送失败
      parameters:
      - description: string enums
        enum:
        - created_at
        - updated_at
        in: query
        name: sortby
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      - description: 搜索关键词，搜索标题
        in: query
        name: keyword
        type: string
      - description: 公司id
        in: query
        name: company_id
        type: integer
      - description: 过滤状态， 默认全部
        enum:
        - 0
        - 1
        - 3
        in: query
        name: status
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.CompanyProduct'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取发布信息列表
      tags:
      - sys
  /sys/info/{id}:
    get:
      consumes:
      - application/json
      description: 需要权限：info:list
      parameters:
      - description: 信息id
        in: path
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Info'
      security:
      - ApiKeyAuth: []
      summary: 获取发布信息详情
      tags:
      - sys
  /sys/info/check_not_pass/{infoId}:
    put:
      consumes:
      - application/json
      description: 需要权限：info:modify
      parameters:
      - description: infoId
        in: path
        name: infoId
        required: true
        type: integer
      - description: AuditNotPass
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/schemas.AuditNotPass'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 审核拒绝信息
      tags:
      - sys
  /sys/info/items:
    get:
      consumes:
      - application/json
      description: 需要权限：info:list
      parameters:
      - description: string enums
        enum:
        - created_at
        - updated_at
        in: query
        name: sortby
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      - description: 搜索关键词，搜索标题
        in: query
        name: keyword
        type: string
      - description: 发布类型，string enums
        enum:
        - auto
        - manual
        in: query
        name: pub_type
        type: string
      - description: 公司id
        in: query
        name: company_id
        type: integer
      - description: 过滤状态， 默认全部
        enum:
        - 1
        - 2
        - 3
        - 4
        in: query
        name: status
        type: integer
      - description: 目标平台，int enums, 默认-1所有，0--黄页88，1--八方资源
        enum:
        - "-1"
        - "0"
        - "1"
        in: query
        name: platform
        type: string
      - description: '是否已同步到爱采购，int enums, 默认: 0--不查 1--已同步到爱采购'
        enum:
        - 0
        - 1
        in: query
        name: synced
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.InfoPaginator'
      security:
      - ApiKeyAuth: []
      summary: 获取发布信息列表 带分页
      tags:
      - sys
  /sys/info/publish/{infoId}:
    put:
      consumes:
      - application/json
      description: |-
        需要权限：info:modify
        如果信息需要，仅需传输需要修改的部分
        信息推送成功后修改部分才会保存
      parameters:
      - description: Info
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.Info'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Info'
      security:
      - ApiKeyAuth: []
      summary: 审核并发布产品信息
      tags:
      - sys
  /sys/info/query:
    get:
      consumes:
      - application/json
      description: 需要权限：info:list
      parameters:
      - description: string enums
        enum:
        - created_at
        - updated_at
        in: query
        name: sortby
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      - description: 搜索关键词，搜索标题
        in: query
        name: keyword
        type: string
      - description: 发布类型，string enums
        enum:
        - auto
        - manual
        in: query
        name: pub_type
        type: string
      - description: 公司id
        in: query
        name: company_id
        type: integer
      - description: 过滤状态， 默认全部
        enum:
        - 1
        - 2
        - 3
        - 4
        in: query
        name: status
        type: integer
      - description: 目标平台，int enums, 默认-1所有，0--黄页88，1--八方资源
        enum:
        - "-1"
        - "0"
        - "1"
        in: query
        name: platform
        type: string
      - description: '是否已同步到爱采购，int enums, 默认: 0--不查 1--已同步到爱采购'
        enum:
        - 0
        - 1
        in: query
        name: synced
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Info'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取发布信息列表
      tags:
      - sys
  /sys/product/{productId}:
    get:
      description: 获取产品详情
      parameters:
      - description: productId
        in: path
        name: productId
        required: true
        type: integer
      - description: int enums
        enum:
        - 0
        - 1
        in: query
        name: check
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 获取产品详情
      tags:
      - sys
    put:
      consumes:
      - application/json
      description: |-
        需要权限：product:modify
        仅需传输需要修改的部分
      parameters:
      - description: productId
        in: path
        name: productId
        required: true
        type: integer
      - description: Product
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.Product'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Product'
      security:
      - ApiKeyAuth: []
      summary: 直接修改产品信息
      tags:
      - sys
  /sys/product/check_not_pass/{productId}:
    put:
      consumes:
      - application/json
      description: 需要权限：product:modify\
      parameters:
      - description: productId
        in: path
        name: productId
        required: true
        type: integer
      - description: AuditNotPass
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/schemas.AuditNotPass'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 审核拒绝产品
      tags:
      - sys
  /sys/product/check_pass/{productId}:
    put:
      consumes:
      - application/json
      description: |-
        需要权限：product:modify
        仅需传输需要修改的部分
      parameters:
      - description: productId
        in: path
        name: productId
        required: true
        type: integer
      - description: Product
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.Product'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Product'
      security:
      - ApiKeyAuth: []
      summary: 审核、修改产品信息
      tags:
      - sys
  /sys/product/check_to_draft/{productId}:
    put:
      consumes:
      - application/json
      description: 需要权限：product:modify\
      parameters:
      - description: productId
        in: path
        name: productId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 重置为草稿
      tags:
      - sys
  /sys/product/query:
    get:
      consumes:
      - application/json
      description: 需要权限：product:get 或者 product:list
      parameters:
      - description: string enums
        enum:
        - created_at
        - updated_at
        in: query
        name: sortby
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      - description: 搜索关键词，搜索产品名或描述
        in: query
        name: keyword
        type: string
      - description: 公司id
        in: query
        name: company_id
        type: integer
      - description: 过滤状态， 默认全部
        enum:
        - 1
        - 2
        - 3
        - 4
        in: query
        name: status
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/schemas.AdminProduct'
            type: array
      security:
      - ApiKeyAuth: []
      summary: '[搜索]获取[待审核]产品列表'
      tags:
      - sys
  /sys/product/stat:
    get:
      description: 获取产品统计信息
      parameters:
      - description: 公司id
        in: query
        name: company_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/schemas.StatusCount'
      security:
      - ApiKeyAuth: []
      summary: 获取产品统计信息
      tags:
      - sys
  /sys/stats/{companyId}:
    get:
      description: 获取公司统计详情
      parameters:
      - description: companyId
        in: path
        name: companyId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserPubStatDetails'
      security:
      - ApiKeyAuth: []
      summary: 获取公司统计详情
      tags:
      - sys
  /sys/stats/query:
    get:
      description: 获取公司统计信息列表
      parameters:
      - description: 搜索词
        in: query
        name: kw
        type: string
      - description: string enums
        enum:
        - product_cnt
        - product_promotion_cnt
        - info_succeed_cnt
        - info_succeed_cnt_yesterday
        - info_failed_cnt_yesterday
        - op_end_time
        - ranked_cnt
        in: query
        name: sortby
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.UserPubStatsPaginator'
      security:
      - ApiKeyAuth: []
      summary: 获取公司统计信息列表
      tags:
      - sys
  /sys/task/query:
    get:
      consumes:
      - application/json
      description: |-
        需要权限：product/info:get 或者 product/info:list
        任务接口获取到的是当前系统用户所绑定的企业等待推送的产品信息列表
      parameters:
      - description: string enums
        enum:
        - created_at
        - updated_at
        in: query
        name: sortby
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 50
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      - description: 目标平台，int enums, 默认-1所有，0--黄页88，2--八方资源
        enum:
        - "-1"
        - "0"
        - "2"
        in: query
        name: platform
        type: string
      - description: '是否已同步到爱采购，int enums, 默认: 0--不查 1--已同步到爱采购'
        enum:
        - 0
        - 1
        in: query
        name: synced
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Info'
      security:
      - ApiKeyAuth: []
      summary: 获取任务列表
      tags:
      - sys
  /sys/user:
    get:
      description: 系统用户获取当前用户信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SystemUser'
      security:
      - ApiKeyAuth: []
      summary: 系统用户获取当前用户信息
      tags:
      - sys
  /sys/user/client_user:
    post:
      consumes:
      - application/json
      description: 用于管理员添加后台用户,需要权限：user:add
      parameters:
      - description: User
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.User'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.User'
      security:
      - ApiKeyAuth: []
      summary: 添加客户账号
      tags:
      - sys
  /sys/user/client_user/{userId}:
    put:
      consumes:
      - application/json
      description: |-
        "用于管理员修改用户信息,需要权限：user:modify
        仅可修改以下字段 'phone', 'email', 'ban', 'company','max_products(超级管理员才可以)'"
      parameters:
      - description: userId
        in: path
        name: userId
        required: true
        type: integer
      - description: User
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.User'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.User'
      security:
      - ApiKeyAuth: []
      summary: 修改用户信息
      tags:
      - sys
  /sys/user/client_user/query:
    get:
      consumes:
      - application/json
      description: 需要权限：user:get 或者 user:list
      parameters:
      - description: string enums
        enum:
        - created_at
        - updated_at
        in: query
        name: sortby
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      - description: 搜索关键词，搜索name
        in: query
        name: keyword
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.User'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取客户列表
      tags:
      - sys
  /sys/user/func_list:
    get:
      description: 获取指定用户信息
      parameters:
      - description: 公司id
        in: query
        name: company_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.UserPlatform'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取指定用户开通功能列表
      tags:
      - sys
  /sys/user/modify_password:
    post:
      consumes:
      - application/json
      description: 系统用户用户修改密码
      parameters:
      - description: ModifyPassword
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/schemas.ModifyPassword'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 系统用户用户修改密码
      tags:
      - sys
  /sys/user/scopes:
    get:
      description: 请求权限：system_user:add or system_user:modify
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dbtypes.Scopes'
      security:
      - ApiKeyAuth: []
      summary: 获取系统权限列表
      tags:
      - sys
  /sys/user/sys_user/query:
    get:
      consumes:
      - application/json
      description: 需要权限：system_user:get 或者 system_user:list
      parameters:
      - description: string enums
        enum:
        - created_at
        - updated_at
        in: query
        name: sortby
        type: string
      - description: string enums
        enum:
        - desc
        - asc
        in: query
        name: order
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      - description: 搜索关键词，搜索name
        in: query
        name: keyword
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.SystemUser'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取系统用户列表
      tags:
      - sys
  /sys/user/system_user:
    post:
      consumes:
      - application/json
      description: 用于管理员添加后台用户,需要权限：system_user:add
      parameters:
      - description: SystemUser
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.SystemUser'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SystemUser'
      security:
      - ApiKeyAuth: []
      summary: 添加系统用户
      tags:
      - sys
  /sys/user/system_user/{userId}:
    put:
      consumes:
      - application/json
      description: |-
        "用于管理员修改后台用户,需要权限：system_user:modify
        仅可修改以下字段 'phone', 'email', 'ban', 'scopes', 'role','name'"
      parameters:
      - description: userId
        in: path
        name: userId
        required: true
        type: integer
      - description: SystemUser
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/schemas.ModifySystemUser'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SystemUser'
      security:
      - ApiKeyAuth: []
      summary: 修改系统用户
      tags:
      - sys
  /tools/dig/keywords:
    get:
      consumes:
      - application/json
      description: 返回推荐的关键词
      parameters:
      - description: string
        in: query
        name: kw
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/configs.ApiOkWrapper'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 数据挖掘
      tags:
      - common
  /tools/dig/materials:
    get:
      consumes:
      - application/json
      description: 返回推荐的素材
      parameters:
      - description: string
        in: query
        name: kw
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/configs.ApiOkWrapper'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 数据挖掘
      tags:
      - common
  /tools/similar:
    post:
      consumes:
      - application/json
      description: 最后一条和前面的一一对比
      parameters:
      - description: Similar
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/schemas.Similar'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/configs.ApiOkWrapper'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 检查重复度
      tags:
      - common
  /upload/img:
    post:
      consumes:
      - multipart/form-data
      description: 支持JPG PNG两种格式
      parameters:
      - description: jpg/png file
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: url_path
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/configs.ApiError'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 上传文件
      tags:
      - deprecated
  /user:
    get:
      description: 获取当前用户信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.User'
      security:
      - ApiKeyAuth: []
      summary: 获取当前用户信息
      tags:
      - client
  /user/func_list:
    get:
      description: 获取当前用户信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.UserPlatform'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取当前用户开通功能列表
      tags:
      - client
  /user/modify_password:
    post:
      consumes:
      - application/json
      description: 用户修改密码, 未启用
      parameters:
      - description: ModifyPassword
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/schemas.ModifyPassword'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 用户修改密码
      tags:
      - client
  /user/redirect:
    get:
      description: 免登陆到黄页88页面
      parameters:
      - description: 要跳转的huangye88页面
        in: query
        name: url
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/configs.ApiOkWrapper'
      security:
      - ApiKeyAuth: []
      summary: 免登陆到黄页88页面
      tags:
      - client
  /v2/album:
    post:
      consumes:
      - application/json
      description: 创建相册
      parameters:
      - description: Album
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/schemas.Album'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/schemas.AlbumResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/configs.ApiError'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 创建相册
      tags:
      - client
  /v2/album/:
    get:
      description: 获取当前用户的相册列表
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Album'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/configs.ApiError'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 获取相册列表
      tags:
      - client
  /v2/album/{id}:
    delete:
      description: 删除相册，干4件事。1是把相册里的图片转移到默认相册。 2. 更新默认相册属性，增加图片数和文件总大小。3 更新相册统计表中的相册数.
        4是删相册记录
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/configs.ApiError'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/configs.ApiError'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 删除相册
      tags:
      - client
    get:
      consumes:
      - application/json
      description: 获取相册内图片列表
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: 默认20
        in: query
        name: limit
        type: integer
      - description: 默认0
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Image'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取相册内图片列表
      tags:
      - client
    put:
      consumes:
      - application/json
      description: 修改相册
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: album
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/schemas.Album'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Album'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/configs.ApiError'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 修改相册
      tags:
      - client
  /v2/album/{id}/images/:
    get:
      consumes:
      - application/json
      description: 根据图片名字做模糊索索
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: 关键词
        in: query
        name: kw
        required: true
        type: string
      - default: 20
        description: int default
        in: query
        name: limit
        required: true
        type: integer
      - default: 0
        description: int default
        in: query
        name: offset
        required: true
        type: integer
      - description: 默认20
        in: query
        name: limit
        type: integer
      - description: 默认0
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Image'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 搜索图片
      tags:
      - client
  /v2/album/images/:
    delete:
      description: 删除照片，其实是改状态
      parameters:
      - description: DelImages
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/schemas.DelImages'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/configs.ApiError'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/configs.ApiError'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 删除照片
      tags:
      - client
  /v2/album/images/{id}:
    put:
      consumes:
      - application/json
      description: 修改图片信息，只能改名字
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: album
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.Image'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Image'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/configs.ApiError'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 修改图片
      tags:
      - client
  /v2/album/images/moveto/{id}:
    post:
      description: 把图片从一个相册移动到另一个相册
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: MoveImages
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/schemas.MoveImages'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/configs.ApiError'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/configs.ApiError'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 移动图片
      tags:
      - client
  /v2/album/stat/:
    get:
      description: 相册总数， 图片总数， 购买的图片数， 赠送的图片数， 已上传图片数，可上传图片数。
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserStat'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/configs.ApiError'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/configs.ApiError'
      security:
      - ApiKeyAuth: []
      summary: 获取相册统计数据
      tags:
      - client
  /v2/album/upload/{id}:
    post:
      consumes:
      - multipart/form-data
      description: 支持JPG/JPEG/BMP/GIF/PNG两种格式
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: jpg,jpeg,bmp, gif, png file
        in: formData
        name: file
        required: true
        type: file
      - description: 上传目的，默认0-上传到相册。1-公司营业执照。2-八方资源企业证明。3-爱采购营业执照。4-爱采购合同文件
        in: query
        name: purpose
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/schemas.UploadResponse'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 上传文件
      tags:
      - client
  /v2/areas/{pid}:
    get:
      description: |-
        传0获取省份和直辖市
        不用接口，用js的方式:  http://api.huangye88.com/js/apiarea.js
      parameters:
      - description: int
        in: path
        name: pid
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/schemas.Area'
            type: array
      summary: 根据父级id获取下一级地区
      tags:
      - common
  /v2/areas/all:
    get:
      description: http://api.huangye88.com/js/apiarea.js的發發助手版本
      produces:
      - text/html
      - application/json
      - text/xml
      - application/javascript
      responses:
        "200":
          description: OK
          schema:
            type: string
      summary: js版本
      tags:
      - common
  /v2/areas/check:
    get:
      description: |-
        检查分类是否ok
        只检查已经开通的平台，如果没有对应的数据，需要提示用户。
      parameters:
      - description: 地区id
        in: query
        name: id
        required: true
        type: integer
      - description: 公司id
        in: query
        name: cid
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
      security:
      - ApiKeyAuth: []
      summary: 检查地区是否ok
      tags:
      - common
  /v2/category:
    get:
      produces:
      - text/html
      - application/json
      - text/xml
      - application/javascript
      responses:
        "200":
          description: OK
          schema:
            type: string
      summary: 获取所有分类,level版
      tags:
      - common
  /v2/category/{id}:
    get:
      parameters:
      - description: int
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Cat'
      summary: 获取分类详情
      tags:
      - common
  /v2/category/{id}/properties:
    get:
      description: input->(string),textarea->长文本,radio->单选按钮,checkbox->多选按钮,select->下拉框
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/schemas.CatField'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取属性列表
      tags:
      - common
  /v2/category/{id}/subs:
    get:
      description: id=0表示获取1级分类。。其他为具体分类的子分类
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/schemas.Cat'
            type: array
      security:
      - ApiKeyAuth: []
      summary: 获取子分类
      tags:
      - common
  /v2/category/check:
    get:
      description: 只检查已经开通的平台，如果没有对应的数据，需要提示用户。
      parameters:
      - description: 分类id
        in: query
        name: id
        required: true
        type: integer
      - description: 公司id
        in: query
        name: cid
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
      security:
      - ApiKeyAuth: []
      summary: 检查分类是否ok
      tags:
      - common
  /v2/category/tree:
    get:
      produces:
      - text/html
      - application/json
      - text/xml
      - application/javascript
      responses:
        "200":
          description: OK
          schema:
            type: string
      summary: 获取所有分类, 树版
      tags:
      - common
  /v2/ranks:
    get:
      description: |-
        获取搜索排名列表
        query string parmas:
        eg: 0 全部(默认值) 1 百度 2 360， 3 搜狗 4 神马 5头条
        platform: -1 全部(默认值) 0 黄页88 1 八方资源 2 中国供应商 4 sole网  5 爱采购
      parameters:
      - description: int enums
        enum:
        - 0
        - 1
        - 2
        - 3
        - 4
        - 5
        in: query
        name: eg
        required: true
        type: integer
      - description: int enums
        enum:
        - 0
        - 1
        - 2
        - 3
        - 4
        - 5
        in: query
        name: pf
        required: true
        type: integer
      - description: int
        in: query
        name: page
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Rank'
      security:
      - ApiKeyAuth: []
      summary: 获取搜索排名列表
      tags:
      - client
securityDefinitions:
  ApiKeyAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
